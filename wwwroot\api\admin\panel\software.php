<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

try {
    // 根据请求方法处理不同操作
    switch ($method) {
        case 'GET':
            // 列出所有软件或获取单个软件详情
            if (isset($_GET['id'])) {
                // 获取单个软件详情
                $id = intval($_GET['id']);
                $stmt = $db->prepare('SELECT * FROM softwares WHERE id = :id');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $result = $stmt->execute();
                $software = $result->fetchArray(SQLITE3_ASSOC);

                if (!$software) {
                    json_response(['success' => false, 'message' => '软件不存在'], 404);
                }

                // 获取分类名称
                if ($software['category']) {
                    $stmt = $db->prepare('SELECT name FROM categories WHERE id = :id');
                    $stmt->bindValue(':id', $software['category'], SQLITE3_INTEGER);
                    $result = $stmt->execute();
                    $category = $result->fetchArray(SQLITE3_ASSOC);
                    if ($category) {
                        $software['category_name'] = $category['name'];
                    }
                }

                json_response(['success' => true, 'data' => $software]);
            } else {
                // 列出所有软件，支持分页和分类筛选
                $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
                $pageSize = isset($_GET['pageSize']) ? min(50, max(10, intval($_GET['pageSize']))) : 20;
                $offset = ($page - 1) * $pageSize;

                $categoryFilter = isset($_GET['category']) ? intval($_GET['category']) : null;
                $includeSubcategories = isset($_GET['include_subcategories']) && $_GET['include_subcategories'] == '1';
                $searchFilter = isset($_GET['search']) ? $_GET['search'] : null;

                $whereClauses = [];
                $params = [];

                if ($categoryFilter) {
                    if ($includeSubcategories) {
                        // 查找选定分类及其所有子分类
                        $categoryIds = [$categoryFilter];

                        // 查询所有子分类
                        $childQuery = "SELECT id FROM categories WHERE parent_id = $categoryFilter OR path LIKE '%,$categoryFilter,%' OR path LIKE '%,$categoryFilter' OR path = '$categoryFilter'";
                        $childResult = $db->query($childQuery);

                        while ($childRow = $childResult->fetchArray(SQLITE3_ASSOC)) {
                            $categoryIds[] = $childRow['id'];
                        }

                        // 构建IN条件
                        $placeholders = [];
                        foreach ($categoryIds as $index => $id) {
                            $placeholder = ":category_$index";
                            $placeholders[] = $placeholder;
                            $params[$placeholder] = $id;
                        }

                        $whereClauses[] = "category IN (" . implode(", ", $placeholders) . ")";
                    } else {
                        $whereClauses[] = 'category = :category';
                        $params[':category'] = $categoryFilter;
                    }
                }

                if ($searchFilter) {
                    $whereClauses[] = '(name LIKE :search OR description LIKE :search)';
                    $params[':search'] = '%' . $searchFilter . '%';
                }

                $whereClause = !empty($whereClauses) ? 'WHERE ' . implode(' AND ', $whereClauses) : '';

                // 查询总数
                $countQuery = 'SELECT COUNT(*) FROM softwares ' . $whereClause;
                $countStmt = $db->prepare($countQuery);
                foreach ($params as $key => $value) {
                    $countStmt->bindValue($key, $value);
                }
                $totalItems = $countStmt->execute()->fetchArray()[0];

                // 查询分页数据
                $query = 'SELECT * FROM softwares ' . $whereClause . ' ORDER BY sort DESC, id DESC LIMIT :limit OFFSET :offset';
                $stmt = $db->prepare($query);
                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }
                $stmt->bindValue(':limit', $pageSize, SQLITE3_INTEGER);
                $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

                $result = $stmt->execute();
                $items = [];
                while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                    // 处理下载量显示
                    if (!empty($row['fake_downloads'])) {
                        $row['display_downloads'] = $row['fake_downloads'] . '万';
                    } else {
                        $row['display_downloads'] = $row['downloads'];
                    }
                    $items[] = $row;
                }

                // 获取分类名称
                $categoryIds = array_unique(array_filter(array_column($items, 'category')));
                $categories = [];
                if (!empty($categoryIds)) {
                    $categoryIdList = implode(',', $categoryIds);
                    $categoryQuery = "SELECT id, name FROM categories WHERE id IN ($categoryIdList)";
                    $categoryResult = $db->query($categoryQuery);
                    while ($row = $categoryResult->fetchArray(SQLITE3_ASSOC)) {
                        $categories[$row['id']] = $row['name'];
                    }
                }

                // 添加分类名称
                foreach ($items as &$item) {
                    if ($item['category'] && isset($categories[$item['category']])) {
                        $item['category_name'] = $categories[$item['category']];
                    } else {
                        $item['category_name'] = '';
                    }
                }

                json_response([
                    'success' => true,
                    'data' => [
                        'items' => $items,
                        'pagination' => [
                            'page' => $page,
                            'pageSize' => $pageSize,
                            'totalItems' => $totalItems,
                            'totalPages' => ceil($totalItems / $pageSize)
                        ]
                    ]
                ]);
            }
            break;

        case 'POST':
            // 创建新软件
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);

            // 验证必填字段
            if (empty($data['name'])) {
                json_response(['success' => false, 'message' => '软件名称不能为空'], 400);
            }

            // 准备数据
            $category = isset($data['category']) ? intval($data['category']) : null;
            $icon = $data['icon'] ?? null;
            $description = $data['description'] ?? null;
            $version = $data['version'] ?? null;
            $size = $data['size'] ?? null;
            $price = $data['price'] ?? null;
            $videoUrl = $data['video_url'] ?? null;
            // 支持新旧字段格式的兼容性
            $downloadUrl1 = $data['download_url_1'] ?? $data['download_url'] ?? null;
            $downloadUrl2 = $data['download_url_2'] ?? $data['backup_download_url'] ?? null;
            $downloadUrl3 = $data['download_url_3'] ?? $data['baidu_url'] ?? null;
            $downloadUrl4 = $data['download_url_4'] ?? null;
            $downloadUrl5 = $data['download_url_5'] ?? null;
            $buyUrl = $data['buy_url'] ?? null;
            $sort = isset($data['sort']) ? intval($data['sort']) : 0;
            $fakeDownloads = $data['fake_downloads'] ?? null;
            $createdAt = time();

            // 检查分类是否存在
            if (!empty($data['category'])) {
                $category = intval($data['category']);
                $categoryExists = $db->querySingle("SELECT id FROM categories WHERE id = $category");
                if (!$categoryExists) {
                    json_response(['success' => false, 'message' => '指定的分类不存在'], 400);
                }
            }

            // 插入数据
            $stmt = $db->prepare('
                INSERT INTO softwares (
                    name, category, icon, description, version, size,
                    downloads, fake_downloads, price, video_url, download_url_1,
                    download_url_2, download_url_3, download_url_4, download_url_5,
                    buy_url, sort, created_at
                ) VALUES (
                    :name, :category, :icon, :description, :version, :size,
                    0, :fake_downloads, :price, :video_url, :download_url_1,
                    :download_url_2, :download_url_3, :download_url_4, :download_url_5,
                    :buy_url, :sort, :created_at
                )
            ');

            $stmt->bindValue(':name', $data['name'], SQLITE3_TEXT);
            $stmt->bindValue(':category', $category, $category === null ? SQLITE3_NULL : SQLITE3_INTEGER);
            $stmt->bindValue(':icon', $icon, $icon === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':description', $description, $description === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':version', $version, $version === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':size', $size, $size === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':fake_downloads', $fakeDownloads, $fakeDownloads === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':price', $price, $price === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':video_url', $videoUrl, $videoUrl === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':download_url_1', $downloadUrl1, $downloadUrl1 === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':download_url_2', $downloadUrl2, $downloadUrl2 === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':download_url_3', $downloadUrl3, $downloadUrl3 === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':download_url_4', $downloadUrl4, $downloadUrl4 === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':download_url_5', $downloadUrl5, $downloadUrl5 === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':buy_url', $buyUrl, $buyUrl === null ? SQLITE3_NULL : SQLITE3_TEXT);
            $stmt->bindValue(':sort', $sort, SQLITE3_INTEGER);
            $stmt->bindValue(':created_at', $createdAt, SQLITE3_INTEGER);

            $stmt->execute();
            $newId = $db->lastInsertRowID();

            json_response([
                'success' => true,
                'message' => '软件添加成功',
                'data' => ['id' => $newId]
            ], 201);
            break;

        case 'PUT':
            // 更新软件
            // 批量更新分类
            if (isset($_GET['ids']) && isset($_GET['category'])) {
                $ids = $_GET['ids'];
                $category = intval($_GET['category']);

                // 解析ID列表
                $idArray = explode(',', $ids);
                if (empty($idArray)) {
                    json_response(['success' => false, 'message' => '未提供有效的ID列表'], 400);
                }

                // 将ID转换为整数并用逗号连接
                $idList = implode(',', array_map('intval', $idArray));

                // 检查分类是否存在
                if ($category) {
                    $categoryExists = $db->querySingle("SELECT id FROM categories WHERE id = $category");
                    if (!$categoryExists) {
                        json_response(['success' => false, 'message' => '指定的分类不存在'], 400);
                    }
                }

                // 开始事务
                $db->exec('BEGIN TRANSACTION');

                try {
                    // 更新软件分类
                    $result = $db->exec("UPDATE softwares SET category = $category WHERE id IN ($idList)");

                    if ($result === false) {
                        throw new Exception('更新软件分类失败');
                    }

                    // 提交事务
                    $db->exec('COMMIT');

                    json_response([
                        'success' => true,
                        'message' => '批量更新分类成功',
                        'affected_rows' => $db->changes()
                    ]);
                } catch (Exception $e) {
                    // 回滚事务
                    $db->exec('ROLLBACK');
                    throw $e;
                }
            }
            // 单个软件更新
            else if (isset($_GET['id'])) {
                $id = intval($_GET['id']);

                // 检查软件是否存在
                $stmt = $db->prepare('SELECT id FROM softwares WHERE id = :id');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $result = $stmt->execute();
                $software = $result->fetchArray(SQLITE3_ASSOC);

                if (!$software) {
                    json_response(['success' => false, 'message' => '软件不存在'], 404);
                }

                $json = file_get_contents('php://input');
                $data = json_decode($json, true);

                // 验证必填字段
                if (empty($data['name'])) {
                    json_response(['success' => false, 'message' => '软件名称不能为空'], 400);
                }

                // 准备数据
                $category = isset($data['category']) ? intval($data['category']) : null;
                $icon = $data['icon'] ?? null;
                $description = $data['description'] ?? null;
                $version = $data['version'] ?? null;
                $size = $data['size'] ?? null;
                $downloads = isset($data['downloads']) ? intval($data['downloads']) : 0;
                $fakeDownloads = $data['fake_downloads'] ?? null;
                $price = $data['price'] ?? null;
                $videoUrl = $data['video_url'] ?? null;
                // 支持新旧字段格式的兼容性
                $downloadUrl1 = $data['download_url_1'] ?? $data['download_url'] ?? null;
                $downloadUrl2 = $data['download_url_2'] ?? $data['backup_download_url'] ?? null;
                $downloadUrl3 = $data['download_url_3'] ?? $data['baidu_url'] ?? null;
                $downloadUrl4 = $data['download_url_4'] ?? null;
                $downloadUrl5 = $data['download_url_5'] ?? null;
                $buyUrl = $data['buy_url'] ?? null;
                $sort = isset($data['sort']) ? intval($data['sort']) : 0;

                // 更新数据
                $stmt = $db->prepare('
                    UPDATE softwares SET
                        name = :name,
                        category = :category,
                        icon = :icon,
                        description = :description,
                        version = :version,
                        size = :size,
                        downloads = :downloads,
                        fake_downloads = :fake_downloads,
                        price = :price,
                        video_url = :video_url,
                        download_url_1 = :download_url_1,
                        download_url_2 = :download_url_2,
                        download_url_3 = :download_url_3,
                        download_url_4 = :download_url_4,
                        download_url_5 = :download_url_5,
                        buy_url = :buy_url,
                        sort = :sort
                    WHERE id = :id
                ');

                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $stmt->bindValue(':name', $data['name'], SQLITE3_TEXT);
                $stmt->bindValue(':category', $category, $category === null ? SQLITE3_NULL : SQLITE3_INTEGER);
                $stmt->bindValue(':icon', $icon, $icon === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':description', $description, $description === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':version', $version, $version === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':size', $size, $size === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':downloads', $downloads, SQLITE3_INTEGER);
                $stmt->bindValue(':fake_downloads', $fakeDownloads, $fakeDownloads === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':price', $price, $price === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':video_url', $videoUrl, $videoUrl === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':download_url_1', $downloadUrl1, $downloadUrl1 === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':download_url_2', $downloadUrl2, $downloadUrl2 === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':download_url_3', $downloadUrl3, $downloadUrl3 === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':download_url_4', $downloadUrl4, $downloadUrl4 === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':download_url_5', $downloadUrl5, $downloadUrl5 === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':buy_url', $buyUrl, $buyUrl === null ? SQLITE3_NULL : SQLITE3_TEXT);
                $stmt->bindValue(':sort', $sort, SQLITE3_INTEGER);

                $stmt->execute();

                json_response(['success' => true, 'message' => '软件更新成功']);
            }
            break;

        case 'DELETE':
            // 删除软件
            if (!isset($_GET['id'])) {
                json_response(['success' => false, 'message' => '缺少软件ID'], 400);
            }

            $id = intval($_GET['id']);

            // 检查软件是否存在
            $stmt = $db->prepare('SELECT id FROM softwares WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $software = $result->fetchArray(SQLITE3_ASSOC);

            if (!$software) {
                json_response(['success' => false, 'message' => '软件不存在'], 404);
            }

            // 删除软件
            $stmt = $db->prepare('DELETE FROM softwares WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $stmt->execute();

            json_response(['success' => true, 'message' => '软件删除成功']);
            break;

        default:
            json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}
