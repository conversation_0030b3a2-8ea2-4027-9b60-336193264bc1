<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 包含支付通知处理类
require_once dirname(dirname(__DIR__)) . DS . 'includes' . DS . 'PaymentNotifyHandler.php';

// 包含支付签名验证类
require_once dirname(dirname(__DIR__)) . DS . 'includes' . DS . 'PaymentSignature.php';

// 获取数据库连接
$db = get_db_connection();

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

try {
    // GET 请求 - 获取支付通知记录列表或单个记录详情
    if ($method === 'GET') {
        // 检查查看支付通知记录的权限
        require_permission('payment_notify.view');

        // 如果指定了ID，获取单个记录详情
        if (isset($_GET['action']) && $_GET['action'] === 'get' && isset($_GET['id'])) {
            $id = intval($_GET['id']);
            $stmt = $db->prepare('SELECT * FROM payment_notify_logs WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $record = $result->fetchArray(SQLITE3_ASSOC);

            if (!$record) {
                json_response(['success' => false, 'message' => '记录不存在'], 404);
            }

            json_response(['success' => true, 'record' => $record]);
        }

        // 获取列表参数
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $pageSize = isset($_GET['pageSize']) ? max(1, min(100, intval($_GET['pageSize']))) : 20;
        $offset = ($page - 1) * $pageSize;

        // 构建查询条件
        $whereConditions = [];
        $params = [];

        // 支付类型筛选
        if (!empty($_GET['payment_type'])) {
            $whereConditions[] = 'payment_type = :payment_type';
            $params[':payment_type'] = $_GET['payment_type'];
        }

        // 签名验证状态筛选
        if (isset($_GET['signature_valid']) && $_GET['signature_valid'] !== '') {
            $whereConditions[] = 'signature_valid = :signature_valid';
            $params[':signature_valid'] = intval($_GET['signature_valid']);
        }

        // 处理结果筛选
        if (!empty($_GET['processing_result'])) {
            $whereConditions[] = 'processing_result = :processing_result';
            $params[':processing_result'] = $_GET['processing_result'];
        }

        // 订单号搜索
        if (!empty($_GET['order_no'])) {
            $whereConditions[] = 'order_no LIKE :order_no';
            $params[':order_no'] = '%' . $_GET['order_no'] . '%';
        }

        // IP地址搜索
        if (!empty($_GET['ip_address'])) {
            $whereConditions[] = 'ip_address LIKE :ip_address';
            $params[':ip_address'] = '%' . $_GET['ip_address'] . '%';
        }

        // 时间范围筛选
        if (!empty($_GET['start_time'])) {
            $whereConditions[] = 'created_at >= :start_time';
            $params[':start_time'] = strtotime($_GET['start_time']);
        }
        if (!empty($_GET['end_time'])) {
            $whereConditions[] = 'created_at <= :end_time';
            $params[':end_time'] = strtotime($_GET['end_time']) + 86400; // 加一天
        }

        $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

        // 获取总数
        $countSql = "SELECT COUNT(*) FROM payment_notify_logs $whereClause";
        $countStmt = $db->prepare($countSql);
        foreach ($params as $key => $value) {
            $countStmt->bindValue($key, $value);
        }
        $countResult = $countStmt->execute();
        $total = $countResult->fetchArray(SQLITE3_NUM)[0];

        // 获取记录列表
        $sql = "
            SELECT id, payment_type, order_no, signature_valid, processing_result,
                   processing_error, ip_address, request_line, created_at
            FROM payment_notify_logs
            $whereClause
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
        ";

        $stmt = $db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $pageSize, SQLITE3_INTEGER);
        $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

        $result = $stmt->execute();
        $records = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $records[] = $row;
        }

        json_response([
            'success' => true,
            'data' => $records,
            'pagination' => [
                'page' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => ceil($total / $pageSize)
            ]
        ]);
    }

    // POST 请求 - 手动验签
    if ($method === 'POST') {
        // 获取请求数据（支持JSON和表单格式）
        $requestData = [];
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';

        if (strpos($contentType, 'application/json') !== false) {
            // JSON格式
            $rawInput = file_get_contents('php://input');
            $requestData = json_decode($rawInput, true) ?: [];
        } else {
            // 表单格式
            $requestData = $_POST;
        }

        $action = $requestData['action'] ?? '';

        if ($action === 'verify') {
            // 检查手动验签权限
            require_permission('payment_notify.verify');

            $id = intval($requestData['id'] ?? 0);
            if (!$id) {
                json_response(['success' => false, 'message' => '缺少记录ID'], 400);
            }

            // 获取记录
            $stmt = $db->prepare('SELECT * FROM payment_notify_logs WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $record = $result->fetchArray(SQLITE3_ASSOC);

            if (!$record) {
                json_response(['success' => false, 'message' => '记录不存在'], 404);
            }

            // 使用统一的处理逻辑从原始数据重新开始处理
            $handler = new PaymentNotifyHandler($db);

            // 检查原始数据
            if (empty($record['raw_data'])) {
                json_response(['success' => false, 'message' => '原始通知数据为空'], 400);
            }

            // 从原始数据重新处理（不更新订单状态，只验证）
            $result = $handler->processNotify($record['payment_type'], $record['raw_data'], [], false);

            $verifyResult = [
                'valid' => $result['signature_valid'],
                'error' => $result['signature_error'],
                'debug_info' => $result['signature_debug'] ?? null,
                'full_result' => $result  // 添加完整结果用于调试
            ];

            // 更新验签结果
            $updateStmt = $db->prepare('
                UPDATE payment_notify_logs
                SET signature_valid = :signature_valid, signature_error = :signature_error
                WHERE id = :id
            ');
            $updateStmt->bindValue(':signature_valid', $verifyResult['valid'] ? 1 : 0, SQLITE3_INTEGER);
            $updateStmt->bindValue(':signature_error', $verifyResult['error'], $verifyResult['error'] ? SQLITE3_TEXT : SQLITE3_NULL);
            $updateStmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $updateStmt->execute();

            json_response([
                'success' => true,
                'message' => '验签完成',
                'verify_result' => $verifyResult
            ]);
        }

        json_response(['success' => false, 'message' => '不支持的操作'], 400);
    }

    // DELETE 请求 - 删除记录
    if ($method === 'DELETE') {
        // 检查删除权限（使用管理员权限）
        require_permission('admin.delete');

        $id = intval($_GET['id'] ?? 0);
        if (!$id) {
            json_response(['success' => false, 'message' => '缺少记录ID'], 400);
        }

        // 检查记录是否存在
        $stmt = $db->prepare('SELECT id FROM payment_notify_logs WHERE id = :id');
        $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
        $result = $stmt->execute();
        if (!$result->fetchArray(SQLITE3_ASSOC)) {
            json_response(['success' => false, 'message' => '记录不存在'], 404);
        }

        // 删除记录
        $stmt = $db->prepare('DELETE FROM payment_notify_logs WHERE id = :id');
        $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
        $stmt->execute();

        json_response(['success' => true, 'message' => '记录删除成功']);
    }

    json_response(['success' => false, 'message' => '不支持的请求方法'], 405);

} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}
