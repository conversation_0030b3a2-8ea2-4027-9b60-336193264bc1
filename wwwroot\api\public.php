<?php
/**
 * 公共API验证文件
 * 此文件应被所有公共API文件包含，用于基本验证和公共函数
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含公共函数库
require_once (__DIR__) . DS . 'common.php';

// 检查是否已安装，如果未安装则会自动重定向或返回JSON响应
check_installed(true);

/**
 * 生成下载令牌
 * 
 * @param int $softwareId 软件ID
 * @param string $orderNo 订单号（可选）
 * @return string 下载令牌
 */
function generate_download_token($softwareId, $orderNo = '') {
    $timestamp = time();
    $secret = get_download_secret();
    $data = $softwareId . '|' . $timestamp . '|' . $orderNo;
    $signature = hash_hmac('sha256', $data, $secret);
    return base64_encode($data . '|' . $signature);
}

/**
 * 验证下载令牌
 * 
 * @param string $token 下载令牌
 * @param int $validMinutes 有效分钟数，默认为5分钟
 * @return array|false 成功返回包含软件ID和订单号的数组，失败返回false
 */
function validate_download_token($token, $validMinutes = 5) {
    try {
        $decoded = base64_decode($token);
        if ($decoded === false) {
            return false;
        }
        
        $parts = explode('|', $decoded);
        if (count($parts) < 4) {
            return false;
        }
        
        $softwareId = $parts[0];
        $timestamp = $parts[1];
        $orderNo = $parts[2];
        $signature = $parts[3];
        
        // 验证时间戳（令牌有效期为5分钟）
        $now = time();
        if ($now - $timestamp > $validMinutes * 60) {
            return false;
        }
        
        // 验证签名
        $secret = get_download_secret();
        $data = $softwareId . '|' . $timestamp . '|' . $orderNo;
        $expectedSignature = hash_hmac('sha256', $data, $secret);
        
        if (!hash_equals($expectedSignature, $signature)) {
            return false;
        }
        
        return [
            'software_id' => $softwareId,
            'order_no' => $orderNo
        ];
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 获取下载密钥
 * 
 * @return string 下载密钥
 */
function get_download_secret() {
    // 从站点设置中获取密钥，如果不存在则使用安装时间作为密钥
    $settings = Settings::load();
    if (isset($settings['download_secret'])) {
        return $settings['download_secret'];
    }
    
    // 使用安装时间作为密钥
    if (isset($settings['installed_time'])) {
        return (string)$settings['installed_time'];
    }
    
    // 如果都不存在，使用一个默认值
    return 'nbbrj_default_download_secret';
}

/**
 * 记录下载日志
 * 
 * @param int $softwareId 软件ID
 * @param string $orderNo 订单号（可选）
 * @return bool 是否成功
 */
function log_download($softwareId, $orderNo = '') {
    $db = get_db_connection();
    
    // 获取客户端信息
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    
    // 插入下载日志
    $stmt = $db->prepare('
        INSERT INTO download_logs (software_id, order_no, ip, user_agent, referer, created_at)
        VALUES (:software_id, :order_no, :ip, :user_agent, :referer, :created_at)
    ');
    
    $stmt->bindValue(':software_id', $softwareId, SQLITE3_INTEGER);
    $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
    $stmt->bindValue(':ip', $ip, SQLITE3_TEXT);
    $stmt->bindValue(':user_agent', $userAgent, SQLITE3_TEXT);
    $stmt->bindValue(':referer', $referer, SQLITE3_TEXT);
    $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
    
    $result = $stmt->execute();
    
    // 更新软件下载次数
    if ($result) {
        $db->exec("UPDATE softwares SET downloads = downloads + 1 WHERE id = $softwareId");
        return true;
    }
    
    return false;
}
