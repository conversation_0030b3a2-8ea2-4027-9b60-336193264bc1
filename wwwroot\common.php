<?php
/**
 * 全局公共函数库 - 前后端共用
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 定义根目录常量（如果尚未定义）
if (!defined('__ROOT__')) {
    define('__ROOT__', dirname(__DIR__));
}

// 定义网站根目录
define('__WWWROOT__', __DIR__);

// 加载前台配置
$frontendConfig = include __DIR__ . '/config.php';

// 定义站点设置文件路径
if (!isset($frontendConfig['site_settings_path'])) {
    throw new RuntimeException('no site_settings_path in frontendConfig?');
}
define('__SITE_SETTINGS_FILE__', $frontendConfig['site_settings_path']);

// 确保Settings类已加载
if (!class_exists('Settings')) {
    require_once __DIR__ . '/includes/Settings.php';
}
Settings::setSettingsPath(__SITE_SETTINGS_FILE__);

// 安全检查函数 - 防止直接访问文件
function prevent_direct_access() {
    if (basename($_SERVER['SCRIPT_FILENAME']) === basename(__FILE__)) {
        header('HTTP/1.0 403 Forbidden');
        exit('访问被拒绝');
    }
}

/**
 * 获取组件配置信息
 *
 * @return array 组件配置信息
 */
function get_config() {
    global $frontendConfig;
    return $frontendConfig;
}

/**
 * 获取站点设置信息
 *
 * @return array 站点设置信息
 */
function get_settings() {
    return Settings::load();
}

/**
 * 检查站点是否已安装
 * 如果未安装，则重定向到安装页面
 */
function check_installed() {
    global $frontendConfig;

    // 检查是否已安装
    if (!Settings::isInstalled()) {
        // 获取安装页面URL
        $installUrl = isset($frontendConfig['install_url']) ? $frontendConfig['install_url'] : '/install.php';

        // 如果是AJAX请求，返回JSON响应
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'redirect' => $installUrl,
                'message' => '系统尚未安装，请先完成安装'
            ]);
            exit;
        }

        // 否则重定向到安装页面
        header('Location: ' . $installUrl);
        exit;
    }
}

/**
 * 获取支付配置
 *
 * @param string $type 支付类型（wechat_pay 或 alipay）
 * @return array|null 支付配置
 */
function get_payment_config($type = null) {
    // 调用 Settings 类的静态方法
    return Settings::getPaymentConfig($type);
}

/**
 * 判断是否为移动设备
 *
 * @return bool 是否为移动设备
 */
function is_mobile_device() {
    $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    return preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $userAgent) || preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i', substr($userAgent, 0, 4));
}

return $frontendConfig;
