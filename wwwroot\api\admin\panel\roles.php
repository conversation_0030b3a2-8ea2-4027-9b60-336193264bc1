<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

try {
    // GET 请求 - 获取角色列表或单个角色详情
    if ($method === 'GET') {
        // 检查查看角色的权限
        require_permission(['role.view', 'role.edit', 'role.delete', 'admin.view', 'admin.edit', 'admin.add'], true);

        // 如果指定了ID，获取单个角色详情
        if (isset($_GET['action']) && $_GET['action'] === 'get' && isset($_GET['id'])) {
            $id = intval($_GET['id']);
            $stmt = $db->prepare('SELECT * FROM admin_roles WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $role = $result->fetchArray(SQLITE3_ASSOC);

            if (!$role) {
                json_response(['success' => false, 'message' => '角色不存在'], 404);
            }

            json_response(['success' => true, 'role' => $role]);
        }
        // 否则获取角色列表
        else {
            $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
            $pageSize = isset($_GET['pageSize']) ? min(50, max(1, intval($_GET['pageSize']))) : 10;
            $offset = ($page - 1) * $pageSize;
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';

            // 构建查询条件
            $whereClause = '';
            $params = [];

            if (!empty($search)) {
                $whereClause = ' WHERE name LIKE :search OR description LIKE :search';
                $params[':search'] = '%' . $search . '%';
            }

            // 获取总记录数
            $countQuery = "SELECT COUNT(*) FROM admin_roles $whereClause";
            $stmt = $db->prepare($countQuery);

            foreach ($params as $param => $value) {
                $stmt->bindValue($param, $value, SQLITE3_TEXT);
            }

            $result = $stmt->execute();
            $totalItems = $result->fetchArray()[0];
            $totalPages = ceil($totalItems / $pageSize);

            // 获取分页数据
            $query = "SELECT r.*, COUNT(a.id) as admin_count
                      FROM admin_roles r
                      LEFT JOIN admins a ON r.id = a.role_id
                      $whereClause
                      GROUP BY r.id
                      ORDER BY r.id ASC
                      LIMIT :limit OFFSET :offset";

            $stmt = $db->prepare($query);
            $stmt->bindValue(':limit', $pageSize, SQLITE3_INTEGER);
            $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

            foreach ($params as $param => $value) {
                $stmt->bindValue($param, $value, SQLITE3_TEXT);
            }

            $result = $stmt->execute();

            $roles = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                $roles[] = $row;
            }

            // 构建分页信息
            $pagination = [
                'page' => $page,
                'pageSize' => $pageSize,
                'totalItems' => $totalItems,
                'totalPages' => $totalPages
            ];

            json_response([
                'success' => true,
                'data' => [
                    'items' => $roles,
                    'pagination' => $pagination
                ]
            ]);
        }
    }
    // POST 请求 - 添加、更新或删除角色
    else if ($method === 'POST') {
        // 获取操作类型
        $action = $_POST['action'] ?? '';

        // 添加角色
        if ($action === 'add') {
            // 检查添加角色的权限
            require_permission('role.add');

            // 验证必填字段
            if (empty($_POST['name'])) {
                json_response(['success' => false, 'message' => '角色名称不能为空'], 400);
            }

            // 检查名称是否已存在
            $stmt = $db->prepare('SELECT id FROM admin_roles WHERE name = :name');
            $stmt->bindValue(':name', $_POST['name'], SQLITE3_TEXT);
            $result = $stmt->execute();
            if ($result->fetchArray(SQLITE3_ASSOC)) {
                json_response(['success' => false, 'message' => '角色名称已存在'], 400);
            }

            // 获取权限数据
            $permissions = $_POST['permissions'] ?? '[]';

            // 插入数据
            $stmt = $db->prepare('
                INSERT INTO admin_roles (name, description, permissions, created_at, updated_at)
                VALUES (:name, :description, :permissions, :created_at, :updated_at)
            ');

            $now = time();
            $stmt->bindValue(':name', $_POST['name'], SQLITE3_TEXT);
            $stmt->bindValue(':description', $_POST['description'] ?? '', SQLITE3_TEXT);
            $stmt->bindValue(':permissions', $permissions, SQLITE3_TEXT);
            $stmt->bindValue(':created_at', $now, SQLITE3_INTEGER);
            $stmt->bindValue(':updated_at', $now, SQLITE3_INTEGER);
            $stmt->execute();

            $newId = $db->lastInsertRowID();

            json_response([
                'success' => true,
                'message' => '角色添加成功',
                'id' => $newId
            ]);
        }
        // 更新角色
        else if ($action === 'update') {
            // 检查编辑角色的权限
            require_permission('role.edit');

            // 验证必填字段
            if (empty($_POST['id']) || empty($_POST['name'])) {
                json_response(['success' => false, 'message' => '角色ID和名称不能为空'], 400);
            }

            $id = intval($_POST['id']);

            // 检查角色是否存在
            $stmt = $db->prepare('SELECT id FROM admin_roles WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            if (!$result->fetchArray(SQLITE3_ASSOC)) {
                json_response(['success' => false, 'message' => '角色不存在'], 404);
            }

            // 检查名称是否已存在（排除当前角色）
            $stmt = $db->prepare('SELECT id FROM admin_roles WHERE name = :name AND id != :id');
            $stmt->bindValue(':name', $_POST['name'], SQLITE3_TEXT);
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            if ($result->fetchArray(SQLITE3_ASSOC)) {
                json_response(['success' => false, 'message' => '角色名称已存在'], 400);
            }

            // 获取权限数据
            $permissions = $_POST['permissions'] ?? '[]';

            // 更新数据
            $stmt = $db->prepare('
                UPDATE admin_roles
                SET name = :name,
                    description = :description,
                    permissions = :permissions,
                    updated_at = :updated_at
                WHERE id = :id
            ');

            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $stmt->bindValue(':name', $_POST['name'], SQLITE3_TEXT);
            $stmt->bindValue(':description', $_POST['description'] ?? '', SQLITE3_TEXT);
            $stmt->bindValue(':permissions', $permissions, SQLITE3_TEXT);
            $stmt->bindValue(':updated_at', time(), SQLITE3_INTEGER);
            $stmt->execute();

            json_response([
                'success' => true,
                'message' => '角色更新成功'
            ]);
        }
        // 删除角色
        else if ($action === 'delete') {
            // 检查删除角色的权限
            require_permission('role.delete');

            // 验证必填字段
            if (empty($_POST['id'])) {
                json_response(['success' => false, 'message' => '角色ID不能为空'], 400);
            }

            $id = intval($_POST['id']);

            // 禁止删除ID为1的角色（超级管理员）
            if ($id === 1) {
                json_response(['success' => false, 'message' => '超级管理员角色不能删除'], 400);
            }

            // 检查角色是否存在
            $stmt = $db->prepare('SELECT id FROM admin_roles WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            if (!$result->fetchArray(SQLITE3_ASSOC)) {
                json_response(['success' => false, 'message' => '角色不存在'], 404);
            }

            // 检查是否有管理员正在使用该角色
            $stmt = $db->prepare('SELECT COUNT(*) as count FROM admins WHERE role_id = :role_id');
            $stmt->bindValue(':role_id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $row = $result->fetchArray(SQLITE3_ASSOC);

            if ($row['count'] > 0) {
                json_response(['success' => false, 'message' => '该角色下存在管理员账户，请先修改或删除相关账户'], 400);
            }

            // 删除角色
            $stmt = $db->prepare('DELETE FROM admin_roles WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $stmt->execute();

            json_response([
                'success' => true,
                'message' => '角色删除成功'
            ]);
        }
        else {
            json_response(['success' => false, 'message' => '未知操作'], 400);
        }
    }
    else {
        json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}
