/**
 * 软件列表和下载处理
 */

// 加载分类列表（树状结构）
async function loadCategories() {
    try {
        const data = await apiGet('/api/public/categories.php');

        if (data && data.success) {
            const categories = data.data;
            const categoryList = document.querySelector('.category-list');

            // 计算所有软件总数
            let totalSoftwareCount = 0;
            const countAllSoftware = (cats) => {
                cats.forEach(cat => {
                    totalSoftwareCount += cat.software_count || 0;
                    if (cat.children && cat.children.length > 0) {
                        countAllSoftware(cat.children);
                    }
                });
            };
            countAllSoftware(categories);

            // 清空现有分类
            categoryList.innerHTML = `
                <li class="category-item active" data-category="all">
                    <a href="#" onclick="return false;">
                        <span>全部软件</span>
                        <span class="category-badge">${totalSoftwareCount}个</span>
                    </a>
                </li>
            `;

            // 递归添加分类和子分类
            const addCategories = (categories, parentElement, level = 0) => {
                categories.forEach(category => {
                    const categoryItem = document.createElement('li');
                    categoryItem.className = 'category-item';
                    categoryItem.setAttribute('data-category', category.id);
                    categoryItem.setAttribute('data-level', level);

                    // 根据层级添加缩进和样式
                    const indent = level > 0 ? `<span class="category-indent" style="margin-left: ${level * 16}px;"></span>` : '';
                    const icon = category.children && category.children.length > 0 ?
                        `<i class="fas fa-caret-right category-toggle"></i>` :
                        (level > 0 ? `<i class="fas fa-minus category-bullet"></i>` : '');

                    categoryItem.innerHTML = `
                        <a href="#" onclick="return false;">
                            ${indent}
                            ${icon}
                            <span>${category.name}</span>
                            <span class="category-badge">${category.total_software_count || category.software_count || 0}个</span>
                        </a>
                    `;
                    parentElement.appendChild(categoryItem);

                    // 添加点击事件
                    categoryItem.addEventListener('click', function(e) {
                        // 如果点击的是展开/折叠图标
                        if (e.target.classList.contains('category-toggle')) {
                            e.target.classList.toggle('rotate-90');

                            // 切换子分类的显示/隐藏
                            const childrenContainer = this.nextElementSibling;
                            if (childrenContainer && childrenContainer.classList.contains('subcategory-container')) {
                                childrenContainer.classList.toggle('hidden');
                            }

                            // 阻止事件冒泡，不触发分类选择
                            e.stopPropagation();
                            return;
                        }

                        // 移除所有分类项的active类
                        document.querySelectorAll('.category-item').forEach(item => {
                            item.classList.remove('active');
                        });
                        // 为当前点击的分类项添加active类
                        this.classList.add('active');

                        // 加载该分类的软件
                        loadSoftwareList(category.id);
                    });

                    // 如果有子分类，创建子分类容器
                    if (category.children && category.children.length > 0) {
                        const subContainer = document.createElement('div');
                        subContainer.className = 'subcategory-container hidden';
                        parentElement.appendChild(subContainer);

                        // 递归添加子分类
                        addCategories(category.children, subContainer, level + 1);
                    }
                });
            };

            // 添加所有顶级分类及其子分类
            addCategories(categories, categoryList);

            // 添加CSS样式
            if (!document.getElementById('category-tree-styles')) {
                const styleEl = document.createElement('style');
                styleEl.id = 'category-tree-styles';
                styleEl.textContent = `
                    .category-toggle {
                        transition: transform 0.2s;
                        margin-right: 5px;
                    }
                    .rotate-90 {
                        transform: rotate(90deg);
                    }
                    .category-bullet {
                        font-size: 8px;
                        margin-right: 5px;
                        vertical-align: middle;
                    }
                    .subcategory-container {
                        margin-left: 10px;
                    }
                    .hidden {
                        display: none;
                    }
                `;
                document.head.appendChild(styleEl);
            }
        }
    } catch (error) {
        console.error('加载分类列表出错:', error);
    }
}

// 加载软件列表
async function loadSoftwareList(categoryId = null, searchQuery = null) {
    try {
        // 构建API URL
        let url = '/api/public/software.php';
        const params = [];

        if (categoryId) {
            params.push(`category=${categoryId}`);
        }

        if (searchQuery) {
            params.push(`search=${encodeURIComponent(searchQuery)}`);
        }

        if (params.length > 0) {
            url += '?' + params.join('&');
        }

        const data = await apiGet(url);

        if (data && data.success) {
            const softwareList = document.querySelector('.software-list');
            const searchEmpty = document.getElementById('search-empty');

            // 清空现有软件列表
            softwareList.innerHTML = '';

            // 检查是否有软件
            if (data.data.length === 0) {
                searchEmpty.style.display = 'block';
                return;
            }

            // 隐藏空结果提示
            searchEmpty.style.display = 'none';

            // 添加软件卡片
            data.data.forEach(software => {
                const softwareCard = document.createElement('div');
                softwareCard.className = 'software-card';
                softwareCard.setAttribute('data-id', software.id);
                softwareCard.setAttribute('data-category', software.category);

                // 判断是否为付费软件
                const isPaid = software.price_value > 0;
                const hasPaid = software.has_paid || false;

                // 构建软件卡片HTML
                softwareCard.innerHTML = `
                    <div class="software-icon-container">
                        <img src="${software.icon || 'img/default-icon.png'}" alt="${software.name}" class="software-icon">
                    </div>
                    <div class="software-content">
                        <span class="software-badge badge-dev">${software.category_name || '未分类'}</span>
                        <h3>${software.name}</h3>
                        <p class="software-description">${software.description || '暂无描述'}</p>
                        <div class="software-meta">
                            <span>版本: ${software.version || '未知'}</span>
                            <span>大小: ${software.size || '未知'}</span>
                            <span>下载量: ${software.fake_downloads || software.downloads || 0}${software.fake_downloads ? '万' : ''}</span>
                            <span>价格: ${software.price || '免费'}</span>
                        </div>
                    </div>
                    <div class="software-actions">
                        ${software.video_url ? `<a href="${software.video_url}" target="_blank" class="video-demo-btn">视频演示</a>` : ''}
                        ${software.download_url && (!isPaid || hasPaid) ?
                            `<button class="download-btn" onclick="handleDownload(${software.id})">下载选项</button>` : ''}
                        ${software.download_url && isPaid && !hasPaid ?
                            `<button class="purchase-btn" onclick="showPaymentOptions(${JSON.stringify(software).replace(/"/g, '&quot;')})">自助购买</button>` : ''}
                    </div>
                `;

                softwareList.appendChild(softwareCard);

                // 添加动画延迟
                setTimeout(() => {
                    softwareCard.style.opacity = '1';
                    softwareCard.style.transform = 'translateY(0)';
                }, 100 * softwareList.children.length);
            });
        }
    } catch (error) {
        console.error('加载软件列表出错:', error);
    }
}

// 处理软件下载
async function handleDownload(softwareId, urlIndex = 1) {
    try {
        // 生成下载令牌和时间戳
        const { token, timestamp } = generateDownloadToken(softwareId);

        // 构建下载URL，包含令牌、时间戳和URL索引
        const downloadUrl = `/api/public/download.php?id=${softwareId}&token=${token}&ts=${timestamp}&url_index=${urlIndex}`;

        // 发送下载请求
        const data = await apiGet(downloadUrl, {}, {}, { operation: '下载' });

        // 检查是否需要支付
        if (data && !data.success && data.need_payment) {
            // 存储当前下载的软件ID
            localStorage.setItem('current_download_software_id', softwareId);

            // 显示支付选择弹窗
            showPaymentOptions(data.software);
            return;
        }

        // 如果请求成功，执行下载
        if (data && data.success) {
            window.location.href = data.download_url;
        }
    } catch (error) {
        console.error('处理下载出错:', error);
    }
}

// 使用订单号下载软件
async function downloadSoftware(softwareId, orderNo, urlIndex = 1) {
    try {
        // 生成下载令牌和时间戳
        const { token, timestamp } = generateDownloadToken(softwareId);

        // 构建下载URL，包含令牌、时间戳、订单号和URL索引
        const downloadUrl = `/api/public/download.php?id=${softwareId}&token=${token}&ts=${timestamp}&order_no=${orderNo}&url_index=${urlIndex}`;

        // 发送下载请求
        const data = await apiGet(downloadUrl, {}, {}, { operation: '下载' });

        // 如果请求成功，执行下载
        if (data && data.success) {
            window.location.href = data.download_url;
        }
    } catch (error) {
        console.error('下载软件出错:', error);
    }
}

// 生成下载令牌
function generateDownloadToken(softwareId) {
    // 使用当前时间戳（秒级）
    const timestamp = Math.floor(Date.now() / 1000);

    // 构建令牌字符串：软件ID_时间戳
    const tokenStr = `${softwareId}_${timestamp}`;

    console.log('生成下载令牌:', tokenStr); // 调试信息

    // 使用外部MD5库生成令牌
    let token;
    if (typeof window.md5 === 'function') {
        token = window.md5(tokenStr);
    } else {
        console.error('MD5 库未加载，请确保在页面中引入了 js-md5 库');
        token = 'md5-library-not-loaded';
    }

    // 返回令牌和时间戳
    return {
        token: token,
        timestamp: timestamp
    };
}

// 显示支付选择弹窗
function showPaymentOptions(software) {
    // 检查必要的对象是否存在
    if (typeof paymentHandler === 'undefined') {
        console.error('paymentHandler 未定义');
        alert('支付系统未初始化，请刷新页面重试');
        return;
    }

    if (typeof universalModal === 'undefined') {
        console.error('universalModal 未定义');
        alert('弹窗系统未初始化，请刷新页面重试');
        return;
    }

    // 检查支付是否可用
    if (!paymentHandler.isPaymentEnabled()) {
        alert('该软件需要付费下载，但支付功能未启用');
        return;
    }

    // 获取可用的支付方式
    const availablePayments = paymentHandler.getAvailablePayments();

    if (availablePayments.length === 0) {
        alert('该软件需要付费下载，但没有可用的支付方式');
        return;
    }

    // 使用通用弹窗显示支付选择
    universalModal.show('payment-options', {
        software: software,
        availablePayments: availablePayments
    });
}

// 监听从支付二维码界面返回的事件
document.addEventListener('payment:showOptions', (event) => {
    const softwareId = event.detail.softwareId;

    // 查找软件信息
    if (window.vueApp && window.vueApp.software) {
        const software = window.vueApp.software.find(s => s.id == softwareId);
        if (software) {
            // 使用通用弹窗重新显示支付选择
            const availablePayments = paymentHandler.getAvailablePayments();
            universalModal.show('payment-options', {
                software: software,
                availablePayments: availablePayments
            });
        }
    }
});

// 检查公告
async function checkAnnouncement() {
    try {
        const data = await apiGet('/api/public/announcement.php');

        if (data && data.success && data.data.enabled) {
            // 显示公告
            showAnnouncement(data.data);
        }
    } catch (error) {
        console.error('检查公告出错:', error);
    }
}

// 显示公告
function showAnnouncement(announcement) {
    // 检查是否已经显示过
    const lastShown = localStorage.getItem('announcement_last_shown');
    const timestamp = announcement.timestamp;

    // 如果不重复显示且已经显示过，则不再显示
    if (!announcement.repeat_show && lastShown && parseInt(lastShown) >= timestamp) {
        return;
    }

    // 使用通用弹窗显示公告
    universalModal.show('announcement', {
        announcement: announcement,
        timestamp: timestamp
    });
}

// 使用外部 js-md5 库计算MD5
// 不再定义全局 md5 函数，直接使用库提供的函数
// 生成下载令牌时直接调用 window.md5