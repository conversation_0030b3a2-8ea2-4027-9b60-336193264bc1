<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
/**
 * 订单管理API
 */

// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

// 获取请求方法
$method = $_SERVER['REQUEST_METHOD'];

try {
    // 根据请求方法处理不同操作
    switch ($method) {
        case 'GET':
            // 获取订单列表或单个订单详情
            if (isset($_GET['id'])) {
                // 获取单个订单详情
                $id = intval($_GET['id']);

                // 查询订单
                $stmt = $db->prepare('
                    SELECT o.*, s.name as software_name
                    FROM payment_orders o
                    LEFT JOIN softwares s ON o.software_id = s.id
                    WHERE o.id = :id
                ');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $result = $stmt->execute();
                $order = $result->fetchArray(SQLITE3_ASSOC);

                if (!$order) {
                    json_response(['success' => false, 'message' => '订单不存在'], 404);
                }

                json_response(['success' => true, 'data' => $order]);
            } else {
                // 获取订单列表
                $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
                $pageSize = isset($_GET['pageSize']) ? intval($_GET['pageSize']) : 10;
                $search = isset($_GET['search']) ? $_GET['search'] : '';
                $status = isset($_GET['status']) ? $_GET['status'] : '';

                // 计算偏移量
                $offset = ($page - 1) * $pageSize;

                // 构建查询条件
                $whereClause = '';
                $params = [];

                if (!empty($search)) {
                    $whereClause .= " WHERE (o.order_no LIKE :search OR s.name LIKE :search)";
                    $params[':search'] = '%' . $search . '%';
                }

                if (!empty($status)) {
                    $whereClause .= empty($whereClause) ? " WHERE o.status = :status" : " AND o.status = :status";
                    $params[':status'] = $status;
                }

                // 查询总记录数
                $countQuery = "
                    SELECT COUNT(*) as count
                    FROM payment_orders o
                    LEFT JOIN softwares s ON o.software_id = s.id
                    $whereClause
                ";

                $countStmt = $db->prepare($countQuery);
                foreach ($params as $key => $value) {
                    $countStmt->bindValue($key, $value);
                }
                $countResult = $countStmt->execute();
                $totalItems = $countResult->fetchArray(SQLITE3_ASSOC)['count'];

                // 查询订单列表
                $query = "
                    SELECT o.*, s.name as software_name
                    FROM payment_orders o
                    LEFT JOIN softwares s ON o.software_id = s.id
                    $whereClause
                    ORDER BY o.created_at DESC
                    LIMIT :limit OFFSET :offset
                ";

                $stmt = $db->prepare($query);
                $stmt->bindValue(':limit', $pageSize, SQLITE3_INTEGER);
                $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }

                $result = $stmt->execute();

                // 获取结果
                $orders = [];
                while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                    $orders[] = $row;
                }

                // 计算总页数
                $totalPages = ceil($totalItems / $pageSize);

                // 返回结果
                json_response([
                    'success' => true,
                    'data' => [
                        'items' => $orders,
                        'pagination' => [
                            'page' => $page,
                            'pageSize' => $pageSize,
                            'totalItems' => $totalItems,
                            'totalPages' => $totalPages
                        ]
                    ]
                ]);
            }
            break;

        case 'PUT':
            // 更新订单状态
            // 获取请求体
            $requestBody = file_get_contents('php://input');
            $data = json_decode($requestBody, true);

            // 验证必要参数
            if (!isset($_GET['id']) || !isset($data['status'])) {
                json_response(['success' => false, 'message' => '缺少必要参数'], 400);
            }

            $id = intval($_GET['id']);
            $status = $data['status'];

            // 验证状态值
            $validStatuses = ['pending', 'paid', 'failed'];
            if (!in_array($status, $validStatuses)) {
                json_response(['success' => false, 'message' => '无效的状态值'], 400);
            }

            // 查询订单是否存在
            $stmt = $db->prepare('SELECT * FROM payment_orders WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $order = $result->fetchArray(SQLITE3_ASSOC);

            if (!$order) {
                json_response(['success' => false, 'message' => '订单不存在'], 404);
            }

            // 更新订单状态
            $stmt = $db->prepare('
                UPDATE payment_orders
                SET status = :status,
                    paid_at = :paid_at
                WHERE id = :id
            ');

            $stmt->bindValue(':status', $status, SQLITE3_TEXT);
            $stmt->bindValue(':paid_at', $status === 'paid' ? time() : null, $status === 'paid' ? SQLITE3_INTEGER : SQLITE3_NULL);
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $stmt->execute();

            // 如果状态改为已支付，添加下载权限
            if ($status === 'paid' && $order['status'] !== 'paid') {
                $stmt = $db->prepare('
                    INSERT INTO download_permissions (
                        software_id, ip, order_no, created_at
                    ) VALUES (
                        :software_id, :ip, :order_no, :created_at
                    )
                ');

                $stmt->bindValue(':software_id', $order['software_id'], SQLITE3_INTEGER);
                $stmt->bindValue(':ip', $order['user_ip'], SQLITE3_TEXT);
                $stmt->bindValue(':order_no', $order['order_no'], SQLITE3_TEXT);
                $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
                $stmt->execute();
            }

            json_response(['success' => true, 'message' => '订单状态已更新']);
            break;

        case 'DELETE':
            // 删除订单
            if (!isset($_GET['id'])) {
                json_response(['success' => false, 'message' => '缺少订单ID'], 400);
            }

            $id = intval($_GET['id']);

            // 查询订单是否存在
            $stmt = $db->prepare('SELECT * FROM payment_orders WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $order = $result->fetchArray(SQLITE3_ASSOC);

            if (!$order) {
                json_response(['success' => false, 'message' => '订单不存在'], 404);
            }

            // 删除订单
            $stmt = $db->prepare('DELETE FROM payment_orders WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $stmt->execute();

            json_response(['success' => true, 'message' => '订单已删除']);
            break;

        default:
            json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}

