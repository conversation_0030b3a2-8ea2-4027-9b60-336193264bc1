/**
 * 图片加载工具
 * 用于解决图片上传或URL更新后不显示的问题
 * 优化版本：减少不必要的延迟和日志，提高性能
 */
const ImageLoader = {
    /**
     * 强制重新加载图片
     * @param {HTMLImageElement} imgElement - 图片元素
     * @param {string} url - 图片URL
     */
    forceReload: function(imgElement, url) {
        if (!imgElement || !url) return;

        // 添加时间戳参数，避免缓存
        const timestamp = Date.now();
        const hasQuery = url.includes('?');
        const newUrl = url + (hasQuery ? '&' : '?') + '_t=' + timestamp;

        // 直接设置新的src，现代浏览器不需要先清空
        imgElement.src = newUrl;

        // 监听错误事件，尝试使用完整URL
        imgElement.onerror = function() {
            if (!url.startsWith('http')) {
                const fullUrl = window.location.origin + (url.startsWith('/') ? '' : '/') + url;
                imgElement.src = fullUrl + (fullUrl.includes('?') ? '&' : '?') + '_t=' + Date.now();
            }
        };
    },

    /**
     * 初始化图片刷新按钮
     */
    initRefreshButtons: function() {
        // 查找所有图片预览容器
        const containers = document.querySelectorAll('.preview-container');

        containers.forEach(container => {
            // 如果已经有刷新按钮，跳过
            if (container.querySelector('.refresh-button')) return;

            // 查找容器中的图片
            const imgElement = container.querySelector('img');
            if (!imgElement || !imgElement.src) return;

            // 设置容器为相对定位
            container.style.position = 'relative';

            // 创建刷新按钮
            const refreshButton = document.createElement('button');
            refreshButton.className = 'refresh-button absolute top-1 right-1 bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-blue-700';
            refreshButton.innerHTML = '<i class="fas fa-sync-alt"></i>';
            refreshButton.title = '刷新图片';

            // 添加点击事件
            refreshButton.addEventListener('click', e => {
                e.preventDefault();
                e.stopPropagation();
                this.forceReload(imgElement, imgElement.src);
            });

            // 将按钮添加到容器中
            container.appendChild(refreshButton);
        });
    }
};

// 在页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化刷新按钮
    ImageLoader.initRefreshButtons();

    // 监听图片上传事件
    document.addEventListener('imageUploaded', function(e) {
        if (e.detail && e.detail.fieldName) {
            // 查找对应的图片元素
            const container = document.querySelector(`[data-field="${e.detail.fieldName}"]`);
            if (container) {
                const imgElement = container.querySelector('img');
                if (imgElement && e.detail.imageUrl) {
                    // 强制重新加载图片
                    ImageLoader.forceReload(imgElement, e.detail.imageUrl);

                    // 确保刷新按钮存在
                    ImageLoader.initRefreshButtons();
                }
            }
        }
    });
});
