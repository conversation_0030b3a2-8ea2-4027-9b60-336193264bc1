<?php
/**
 * 系统安装页面
 */

// 定义根目录常量（如果尚未定义）
if (!defined('__ROOT__')) {
    define('__ROOT__', __DIR__);
}

// 包含公共函数库
require_once __DIR__ . '/common.php';

// 加载前台配置
$frontendConfig = include __DIR__ . '/config.php';

// 获取站点设置文件路径
if (!isset($frontendConfig['site_settings_path'])) {
    throw new RuntimeException('no site_settings_path in frontendConfig?');
}
$siteSettingsFile = $frontendConfig['site_settings_path'];

// 检查是否已安装 - 检查配置文件和installed标志
$installed = false;
if (file_exists($siteSettingsFile)) {
    $config = include $siteSettingsFile;
    // 检查installed标志
    $installed = isset($config['installed']) && $config['installed'] === true;

    // 如果标志为true，再检查数据库是否存在
    if ($installed && isset($config['db_path'])) {
        $dbPath = $config['db_path'];
        // 检查数据库文件是否存在
        if (!file_exists($dbPath)) {
            $installed = false;
        }
    }
}

// 如果已安装，重定向到首页
if ($installed) {
    header('Location: /');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>安装娜宝贝软件管理系统</title>
    <script src="<?php echo $frontendConfig['assets_path']; ?>libs/tailwindcss/tailwind.js"></script>
    <link href="<?php echo $frontendConfig['assets_path']; ?>libs/font-awesome/css/all.min.css" rel="stylesheet">
    <script src="<?php echo $frontendConfig['assets_path']; ?>libs/vue/vue.min.js"></script>
    <script src="<?php echo $frontendConfig['assets_path']; ?>libs/axios/axios.min.js"></script>
    <script src="<?php echo $frontendConfig['assets_path']; ?>js/api.js"></script>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #0a0a1a;
            color: #e0e0ff;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 100, 255, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(150, 0, 255, 0.1) 0%, transparent 20%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem 0;
        }

        .install-container {
            width: 100%;
            max-width: 600px;
            background: rgba(20, 30, 60, 0.6);
            border-radius: 8px;
            border: 1px solid #334466;
            padding: 2rem;
            box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);
            backdrop-filter: blur(5px);
        }

        .install-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .install-header h1 {
            font-size: 1.8rem;
            color: #00ffff;
            text-shadow: 0 0 10px #00aaff, 0 0 20px #0066ff;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .install-header p {
            color: #aaccff;
            font-size: 0.9rem;
        }

        .step {
            margin-bottom: 2rem;
        }

        .step h2 {
            color: #00ccff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #334477;
            text-shadow: 0 0 5px #00aaff;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #aaccff;
        }

        .form-input {
            width: 100%;
            padding: 0.8rem;
            background: rgba(10, 20, 50, 0.5);
            border: 1px solid #334466;
            border-radius: 4px;
            color: #e0e0ff;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            border-color: #00aaff;
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.3);
            outline: none;
        }

        .install-btn {
            width: 100%;
            padding: 0.8rem;
            background: linear-gradient(135deg, #0066cc, #0044aa);
            color: #ffffff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .install-btn:hover {
            background: linear-gradient(135deg, #0077dd, #0055bb);
            box-shadow: 0 0 15px rgba(0, 100, 255, 0.5);
        }

        .install-btn:disabled {
            background: #334466;
            cursor: not-allowed;
        }

        .error-message {
            color: #ff5555;
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .success-message {
            color: #55ff55;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .step-item {
            flex: 1;
            text-align: center;
            padding: 0.5rem;
            border-bottom: 2px solid #334466;
            color: #aaccff;
            transition: all 0.3s ease;
        }

        .step-item.active {
            border-bottom-color: #00aaff;
            color: #00ffff;
        }

        .step-item.completed {
            border-bottom-color: #00cc66;
            color: #00ffaa;
        }
    </style>
</head>
<body>
    <div id="app" class="install-container">
        <div class="install-header">
            <h1>娜宝贝软件管理系统</h1>
            <p>安装向导</p>
        </div>

        <div class="step-indicator">
            <div class="step-item" :class="{ 'active': currentStep === 1, 'completed': currentStep > 1 }">
                <i class="fas fa-check-circle" v-if="currentStep > 1"></i>
                <i class="fas fa-circle" v-else></i>
                环境检查
            </div>
            <div class="step-item" :class="{ 'active': currentStep === 2, 'completed': currentStep > 2 }">
                <i class="fas fa-check-circle" v-if="currentStep > 2"></i>
                <i class="fas fa-circle" v-else></i>
                系统设置
            </div>
            <div class="step-item" :class="{ 'active': currentStep === 3, 'completed': currentStep > 3 }">
                <i class="fas fa-check-circle" v-if="currentStep > 3"></i>
                <i class="fas fa-circle" v-else></i>
                安装完成
            </div>
        </div>

        <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
        </div>

        <div v-if="successMessage" class="success-message">
            {{ successMessage }}
        </div>

        <!-- 步骤1：环境检查 -->
        <div v-if="currentStep === 1" class="step">
            <h2>步骤 1: 环境检查</h2>

            <div class="grid grid-cols-1 gap-4">
                <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border" :class="phpVersionOk ? 'border-green-500' : 'border-red-500'">
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="text-blue-300 font-medium">PHP 版本</div>
                            <div class="text-sm">当前版本: {{ phpVersion }}</div>
                        </div>
                        <div v-if="phpVersionOk" class="text-green-400">
                            <i class="fas fa-check-circle"></i> 符合要求
                        </div>
                        <div v-else class="text-red-400">
                            <i class="fas fa-times-circle"></i> 不符合要求
                        </div>
                    </div>
                </div>

                <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border" :class="sqliteOk ? 'border-green-500' : 'border-red-500'">
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="text-blue-300 font-medium">SQLite 支持</div>
                            <div class="text-sm">{{ sqliteVersion }}</div>
                        </div>
                        <div v-if="sqliteOk" class="text-green-400">
                            <i class="fas fa-check-circle"></i> 已启用
                        </div>
                        <div v-else class="text-red-400">
                            <i class="fas fa-times-circle"></i> 未启用
                        </div>
                    </div>
                </div>

                <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border" :class="jsonOk ? 'border-green-500' : 'border-red-500'">
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="text-blue-300 font-medium">JSON 支持</div>
                        </div>
                        <div v-if="jsonOk" class="text-green-400">
                            <i class="fas fa-check-circle"></i> 已启用
                        </div>
                        <div v-else class="text-red-400">
                            <i class="fas fa-times-circle"></i> 未启用
                        </div>
                    </div>
                </div>

                <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border" :class="writableOk ? 'border-green-500' : 'border-red-500'">
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="text-blue-300 font-medium">目录写入权限</div>
                            <div class="text-sm">配置文件和数据库目录</div>
                        </div>
                        <div v-if="writableOk" class="text-green-400">
                            <i class="fas fa-check-circle"></i> 可写入
                        </div>
                        <div v-else class="text-red-400">
                            <i class="fas fa-times-circle"></i> 不可写入
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6">
                <button class="install-btn" @click="nextStep" :disabled="!allRequirementsMet">
                    <span v-if="allRequirementsMet">下一步</span>
                    <span v-else>环境不符合要求</span>
                </button>
            </div>
        </div>

        <!-- 步骤2：系统设置 -->
        <div v-if="currentStep === 2" class="step">
            <h2>步骤 2: 系统设置</h2>

            <form @submit.prevent="install">
                <div class="form-group">
                    <label for="siteTitle" class="form-label">网站标题</label>
                    <input type="text" id="siteTitle" v-model="siteTitle" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="siteSubtitle" class="form-label">网站副标题</label>
                    <input type="text" id="siteSubtitle" v-model="siteSubtitle" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="dbPath" class="form-label">数据库路径（相对于网站根目录）</label>
                    <input type="text" id="dbPath" v-model="dbPath" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="frontendUrl" class="form-label">前端网站URL（用于支付回调等功能）</label>
                    <input type="text" id="frontendUrl" v-model="frontendUrl" class="form-input" required>
                    <div class="text-xs text-blue-300 mt-1">系统自动检测的URL，如有误请手动修改</div>
                </div>

                <div class="form-group">
                    <label for="username" class="form-label">管理员用户名</label>
                    <input type="text" id="username" v-model="username" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">管理员密码</label>
                    <input type="password" id="password" v-model="password" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="confirmPassword" class="form-label">确认密码</label>
                    <input type="password" id="confirmPassword" v-model="confirmPassword" class="form-input" required>
                </div>

                <div class="flex space-x-4">
                    <button type="button" class="install-btn bg-gray-700" @click="currentStep = 1">上一步</button>
                    <button type="submit" class="install-btn" :disabled="installing">
                        <span v-if="installing">安装中...</span>
                        <span v-else>安装</span>
                    </button>
                </div>
            </form>
        </div>

        <!-- 步骤3：安装完成 -->
        <div v-if="currentStep === 3" class="step">
            <h2>步骤 3: 安装完成</h2>

            <div class="bg-green-900 bg-opacity-20 p-6 rounded-lg border border-green-700 text-center mb-6">
                <i class="fas fa-check-circle text-green-400 text-5xl mb-4"></i>
                <div class="text-xl text-green-300 mb-2">安装成功！</div>
                <div class="text-green-200">娜宝贝软件管理系统已成功安装</div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <a href="/admin/index.php/public/login.html" class="bg-blue-900 bg-opacity-30 p-4 rounded-lg border border-blue-700 text-center hover:bg-blue-800 transition-all">
                    <i class="fas fa-sign-in-alt text-blue-400 text-xl mb-2"></i>
                    <div class="text-blue-300">管理员登录</div>
                </a>

                <a href="/" class="bg-purple-900 bg-opacity-30 p-4 rounded-lg border border-purple-700 text-center hover:bg-purple-800 transition-all">
                    <i class="fas fa-home text-purple-400 text-xl mb-2"></i>
                    <div class="text-purple-300">访问网站首页</div>
                </a>
            </div>

            <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border border-blue-700">
                <div class="text-blue-300 font-medium mb-2">安装信息</div>
                <div class="text-sm text-blue-200">
                    <div class="mb-1">网站标题: {{ siteTitle }}</div>
                    <div class="mb-1">管理员用户名: {{ username }}</div>
                    <div class="mb-1">数据库路径: {{ dbPath }}</div>
                    <div class="mb-1">安装时间: {{ installedTime }}</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                currentStep: 1,
                errorMessage: '',
                successMessage: '',

                // 环境检查
                phpVersion: '<?php echo PHP_VERSION; ?>',
                phpVersionOk: <?php echo version_compare(PHP_VERSION, '7.0.0', '>=') ? 'true' : 'false'; ?>,
                sqliteVersion: '<?php echo class_exists('SQLite3') ? SQLite3::version()['versionString'] : '未安装'; ?>',
                sqliteOk: <?php echo class_exists('SQLite3') ? 'true' : 'false'; ?>,
                jsonOk: <?php echo function_exists('json_encode') && function_exists('json_decode') ? 'true' : 'false'; ?>,
                writableOk: <?php echo is_writable(__ROOT__) ? 'true' : 'false'; ?>,

                // 系统设置
                siteTitle: '娜宝贝软件',
                siteSubtitle: '探索未来科技的软件集合',
                dbPath: 'database/nbbrj.db',
                username: '',
                password: '',
                confirmPassword: '',
                frontendUrl: '', // 前端URL，将由JavaScript自动获取
                installing: false,

                // 安装完成
                installedTime: ''
            },
            computed: {
                allRequirementsMet() {
                    return this.phpVersionOk && this.sqliteOk && this.jsonOk && this.writableOk;
                }
            },
            created() {
                // 自动获取当前URL作为前端URL
                this.frontendUrl = this.getCurrentUrl();
            },
            methods: {
                nextStep() {
                    if (this.currentStep === 1 && this.allRequirementsMet) {
                        this.currentStep = 2;
                    }
                },
                // 获取当前URL（去除路径部分，只保留域名）
                getCurrentUrl() {
                    const url = window.location.href;
                    const urlObj = new URL(url);
                    // 返回协议 + 主机名 + 端口（如果有）
                    let baseUrl = urlObj.protocol + '//' + urlObj.hostname;
                    if (urlObj.port) {
                        baseUrl += ':' + urlObj.port;
                    }

                    // 获取安装脚本所在的目录（不包括install.php）
                    const pathParts = urlObj.pathname.split('/');
                    pathParts.pop(); // 移除install.php
                    if (pathParts.length > 0 && pathParts[pathParts.length - 1] !== '') {
                        baseUrl += pathParts.join('/') + '/';
                    } else {
                        baseUrl += '/';
                    }

                    return baseUrl;
                },

                async install() {
                    // 验证表单
                    if (!this.siteTitle || !this.siteSubtitle || !this.dbPath || !this.username || !this.password || !this.frontendUrl) {
                        this.errorMessage = '请填写所有必填字段';
                        return;
                    }

                    if (this.password !== this.confirmPassword) {
                        this.errorMessage = '两次输入的密码不一致';
                        return;
                    }

                    if (this.password.length < 6) {
                        this.errorMessage = '密码长度不能少于6个字符';
                        return;
                    }

                    // 清除错误信息
                    this.errorMessage = '';
                    this.installing = true;

                    // 发送安装请求
                    try {
                        const data = await apiPost(
                            '/api/public/install.php',
                            {
                                siteTitle: this.siteTitle,
                                siteSubtitle: this.siteSubtitle,
                                dbPath: this.dbPath,
                                username: this.username,
                                password: this.password,
                                frontendUrl: this.frontendUrl
                            },
                            {},
                            { showSuccess: true, successMessage: '系统安装成功！', operation: '安装' }
                        );

                        this.installing = false;

                        if (data && data.success) {
                            // 安装成功
                            this.installedTime = new Date().toLocaleString();
                            this.currentStep = 3;
                        } else {
                            // 安装失败
                            this.errorMessage = data.message || '安装失败，请重试';
                        }
                    } catch (error) {
                        this.installing = false;
                        this.errorMessage = '安装请求出错，请重试';
                        console.error('安装请求出错：', error);
                    }
                }
            }
        });
    </script>
</body>
</html>
