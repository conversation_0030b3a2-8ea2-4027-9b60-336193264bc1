/**
 * 表单验证工具类
 */

class FormValidator {
    constructor(form, rules = {}) {
        this.form = form;
        this.rules = rules;
        this.errors = {};
        this.isValid = true;
    }

    /**
     * 验证表单
     * @param {object} data 表单数据
     * @returns {object} 验证结果
     */
    validate(data = null) {
        this.errors = {};
        this.isValid = true;

        // 如果没有传入数据，从表单元素中获取
        if (!data) {
            data = this.getFormData();
        }

        // 遍历验证规则
        for (const field in this.rules) {
            const fieldRules = this.rules[field];
            const value = data[field];

            this.validateField(field, value, fieldRules);
        }

        return {
            isValid: this.isValid,
            errors: this.errors,
            data: data
        };
    }

    /**
     * 验证单个字段
     * @param {string} field 字段名
     * @param {any} value 字段值
     * @param {array} rules 验证规则
     */
    validateField(field, value, rules) {
        for (const rule of rules) {
            const result = this.applyRule(field, value, rule);
            if (!result.valid) {
                this.addError(field, result.message);
                break; // 一个字段只显示第一个错误
            }
        }
    }

    /**
     * 应用验证规则
     * @param {string} field 字段名
     * @param {any} value 字段值
     * @param {object} rule 验证规则
     * @returns {object} 验证结果
     */
    applyRule(field, value, rule) {
        const { type, message, ...params } = rule;

        switch (type) {
            case 'required':
                return this.validateRequired(value, message || `${field}是必填项`);
            
            case 'email':
                return this.validateEmail(value, message || `${field}格式不正确`);
            
            case 'phone':
                return this.validatePhone(value, message || `${field}格式不正确`);
            
            case 'url':
                return this.validateUrl(value, message || `${field}格式不正确`);
            
            case 'min_length':
                return this.validateMinLength(value, params.length, message || `${field}长度不能少于${params.length}个字符`);
            
            case 'max_length':
                return this.validateMaxLength(value, params.length, message || `${field}长度不能超过${params.length}个字符`);
            
            case 'min_value':
                return this.validateMinValue(value, params.value, message || `${field}不能小于${params.value}`);
            
            case 'max_value':
                return this.validateMaxValue(value, params.value, message || `${field}不能大于${params.value}`);
            
            case 'numeric':
                return this.validateNumeric(value, message || `${field}必须是数字`);
            
            case 'integer':
                return this.validateInteger(value, message || `${field}必须是整数`);
            
            case 'regex':
                return this.validateRegex(value, params.pattern, message || `${field}格式不正确`);
            
            case 'in':
                return this.validateIn(value, params.values, message || `${field}值不在允许范围内`);
            
            case 'not_in':
                return this.validateNotIn(value, params.values, message || `${field}值不允许`);
            
            case 'confirmed':
                return this.validateConfirmed(value, params.field, message || `${field}确认不匹配`);
            
            case 'unique':
                return this.validateUnique(value, params.endpoint, params.exclude, message || `${field}已存在`);
            
            case 'custom':
                return params.validator(value, message || `${field}验证失败`);
            
            default:
                return { valid: true };
        }
    }

    /**
     * 必填验证
     */
    validateRequired(value, message) {
        const isEmpty = value === null || value === undefined || 
                       (typeof value === 'string' && value.trim() === '') ||
                       (Array.isArray(value) && value.length === 0);
        
        return {
            valid: !isEmpty,
            message: isEmpty ? message : null
        };
    }

    /**
     * 邮箱验证
     */
    validateEmail(value, message) {
        if (!value) return { valid: true }; // 空值跳过验证
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = emailRegex.test(value);
        
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 手机号验证
     */
    validatePhone(value, message) {
        if (!value) return { valid: true };
        
        const phoneRegex = /^1[3-9]\d{9}$/;
        const isValid = phoneRegex.test(value);
        
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * URL验证
     */
    validateUrl(value, message) {
        if (!value) return { valid: true };
        
        try {
            new URL(value);
            return { valid: true };
        } catch {
            return { valid: false, message };
        }
    }

    /**
     * 最小长度验证
     */
    validateMinLength(value, minLength, message) {
        if (!value) return { valid: true };
        
        const isValid = value.toString().length >= minLength;
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 最大长度验证
     */
    validateMaxLength(value, maxLength, message) {
        if (!value) return { valid: true };
        
        const isValid = value.toString().length <= maxLength;
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 最小值验证
     */
    validateMinValue(value, minValue, message) {
        if (!value && value !== 0) return { valid: true };
        
        const numValue = parseFloat(value);
        const isValid = !isNaN(numValue) && numValue >= minValue;
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 最大值验证
     */
    validateMaxValue(value, maxValue, message) {
        if (!value && value !== 0) return { valid: true };
        
        const numValue = parseFloat(value);
        const isValid = !isNaN(numValue) && numValue <= maxValue;
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 数字验证
     */
    validateNumeric(value, message) {
        if (!value && value !== 0) return { valid: true };
        
        const isValid = !isNaN(parseFloat(value)) && isFinite(value);
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 整数验证
     */
    validateInteger(value, message) {
        if (!value && value !== 0) return { valid: true };
        
        const isValid = Number.isInteger(parseFloat(value));
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 正则表达式验证
     */
    validateRegex(value, pattern, message) {
        if (!value) return { valid: true };
        
        const regex = new RegExp(pattern);
        const isValid = regex.test(value);
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 值在指定范围内验证
     */
    validateIn(value, values, message) {
        if (!value) return { valid: true };
        
        const isValid = values.includes(value);
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 值不在指定范围内验证
     */
    validateNotIn(value, values, message) {
        if (!value) return { valid: true };
        
        const isValid = !values.includes(value);
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 确认字段验证
     */
    validateConfirmed(value, confirmField, message) {
        const formData = this.getFormData();
        const confirmValue = formData[confirmField];
        
        const isValid = value === confirmValue;
        return {
            valid: isValid,
            message: isValid ? null : message
        };
    }

    /**
     * 唯一性验证（异步）
     */
    async validateUnique(value, endpoint, exclude, message) {
        if (!value) return { valid: true };
        
        try {
            const params = new URLSearchParams({ value });
            if (exclude) params.append('exclude', exclude);
            
            const response = await fetch(`${endpoint}?${params}`);
            const result = await response.json();
            
            return {
                valid: result.unique,
                message: result.unique ? null : message
            };
        } catch (error) {
            console.error('唯一性验证失败:', error);
            return { valid: true }; // 验证失败时默认通过
        }
    }

    /**
     * 添加错误信息
     */
    addError(field, message) {
        if (!this.errors[field]) {
            this.errors[field] = [];
        }
        this.errors[field].push(message);
        this.isValid = false;
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        if (!this.form) return {};
        
        const formData = new FormData(this.form);
        const data = {};
        
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        return data;
    }

    /**
     * 显示错误信息
     */
    showErrors() {
        // 清除之前的错误显示
        this.clearErrors();
        
        for (const field in this.errors) {
            const fieldElement = this.form.querySelector(`[name="${field}"]`);
            if (fieldElement) {
                this.showFieldError(fieldElement, this.errors[field][0]);
            }
        }
    }

    /**
     * 显示字段错误
     */
    showFieldError(fieldElement, message) {
        // 添加错误样式
        fieldElement.classList.add('error');
        
        // 创建错误信息元素
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message text-red-400 text-sm mt-1';
        errorElement.textContent = message;
        
        // 插入错误信息
        fieldElement.parentNode.appendChild(errorElement);
    }

    /**
     * 清除错误显示
     */
    clearErrors() {
        if (!this.form) return;
        
        // 移除错误样式
        const errorFields = this.form.querySelectorAll('.error');
        errorFields.forEach(field => field.classList.remove('error'));
        
        // 移除错误信息
        const errorMessages = this.form.querySelectorAll('.error-message');
        errorMessages.forEach(message => message.remove());
    }

    /**
     * 实时验证
     */
    enableRealTimeValidation() {
        if (!this.form) return;
        
        for (const field in this.rules) {
            const fieldElement = this.form.querySelector(`[name="${field}"]`);
            if (fieldElement) {
                fieldElement.addEventListener('blur', () => {
                    this.validateSingleField(field);
                });
            }
        }
    }

    /**
     * 验证单个字段并显示结果
     */
    validateSingleField(field) {
        const fieldElement = this.form.querySelector(`[name="${field}"]`);
        if (!fieldElement) return;
        
        const value = fieldElement.value;
        const rules = this.rules[field];
        
        // 清除该字段的错误
        fieldElement.classList.remove('error');
        const existingError = fieldElement.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // 验证字段
        for (const rule of rules) {
            const result = this.applyRule(field, value, rule);
            if (!result.valid) {
                this.showFieldError(fieldElement, result.message);
                break;
            }
        }
    }
}

// 如果在浏览器环境中，将类挂载到全局对象
if (typeof window !== 'undefined') {
    window.FormValidator = FormValidator;
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormValidator;
}
