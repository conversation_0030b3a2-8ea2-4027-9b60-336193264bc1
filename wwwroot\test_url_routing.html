<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL路由测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .test-link:hover {
            background: #005a87;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .current-url {
            font-family: monospace;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>URL路由功能测试</h1>

    <div class="test-section">
        <h2>当前URL信息</h2>
        <div class="current-url" id="currentUrl"></div>
        <div class="current-url" id="currentHash"></div>
    </div>

    <div class="test-section">
        <h2>测试链接</h2>
        <div class="info">
            <strong>说明：</strong>点击下面的链接测试URL路由功能。这些链接会跳转到主页并自动显示对应软件的下载选项弹窗。
        </div>

        <a href="/#/software/1" class="test-link">测试软件ID 1</a>
        <a href="/#/software/2" class="test-link">测试软件ID 2</a>
        <a href="/#/software/3" class="test-link">测试软件ID 3</a>
        <a href="/#/software/999" class="test-link">测试不存在的软件ID 999</a>

        <br><br>
        <h3>支付宝回调测试</h3>
        <a href="/#/software/1?order_no=20241201123456" class="test-link">测试支付宝回调（仅订单号）</a>
        <a href="/#/software/1?passback_params=%7B%22software_id%22%3A1%2C%22order_no%22%3A%2220241201123456%22%7D&sign=test&out_trade_no=20241201123456&total_amount=10.00" class="test-link">测试支付宝回调（passback_params）</a>

        <br><br>
        <a href="/" class="test-link">返回主页（无路由）</a>
    </div>

    <div class="test-section">
        <h2>手动测试</h2>
        <div class="info">
            <strong>手动测试步骤：</strong>
            <ol>
                <li>在浏览器地址栏中输入：<code>http://你的域名/#/software/1</code></li>
                <li>按回车访问</li>
                <li>观察是否自动显示下载选项弹窗</li>
                <li>检查是否没有显示公告弹窗</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>功能验证清单</h2>
        <div class="info">
            <strong>需要验证的功能：</strong>
            <ul>
                <li>✓ URL格式 <code>/#/software/{id}</code> 能被正确识别</li>
                <li>✓ 能够获取到正确的软件信息</li>
                <li>✓ 自动显示下载选项弹窗</li>
                <li>✓ 不显示公告弹窗</li>
                <li>✓ 软件不存在时显示错误提示</li>
                <li>✓ 支持浏览器前进后退</li>
                <li>✓ hash变化时能够正确响应</li>
                <li>✓ 支付宝回调处理（带订单号参数）</li>
                <li>✓ 支付宝回调验证（带签名参数）</li>
                <li>✓ 支付宝passback_params自定义参数解析</li>
                <li>✓ 支付成功后清理URL参数</li>
                <li>✓ 支付成功提示显示</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示当前URL信息
        function updateUrlInfo() {
            document.getElementById('currentUrl').textContent = 'URL: ' + window.location.href;
            document.getElementById('currentHash').textContent = 'Hash: ' + window.location.hash;
        }

        // 页面加载时更新URL信息
        updateUrlInfo();

        // 监听hash变化
        window.addEventListener('hashchange', function() {
            updateUrlInfo();
            console.log('Hash变化:', window.location.hash);
        });

        // 测试URL路由检查函数
        function testUrlRouting() {
            const hash = window.location.hash;
            console.log('当前hash:', hash);

            // 检查是否是软件直链格式：#/software/12345
            const softwareMatch = hash.match(/^#\/software\/(\d+)$/);
            if (softwareMatch) {
                const softwareId = parseInt(softwareMatch[1]);
                console.log('检测到软件直链，软件ID:', softwareId);
                return true;
            }

            return false;
        }

        // 页面加载时测试
        console.log('URL路由测试结果:', testUrlRouting());
    </script>
</body>
</html>
