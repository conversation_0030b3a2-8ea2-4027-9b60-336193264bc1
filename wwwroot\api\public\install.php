<?php
// 设置响应类型为JSON
header('Content-Type: application/json');

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 定义API根目录常量
if (!defined('__API_ROOT__')) {
    define('__API_ROOT__', dirname(__DIR__));
}

// 定义网站根目录常量
if (!defined('__WWWROOT__')) {
    define('__WWWROOT__', dirname(dirname(__DIR__)));
}

// 定义根目录常量
if (!defined('__ROOT__')) {
    define('__ROOT__', dirname(__WWWROOT__));
}

// 包含公共函数库
require_once __API_ROOT__ . DS . 'common.php';

// 加载API配置
$apiConfig = include __API_ROOT__ . '/config.php';

// 获取站点设置文件路径
if (!isset($apiConfig['site_settings_path'])) {
    throw new RuntimeException('no site_settings_path in apiConfig?');
}
$siteSettingsFile = $apiConfig['site_settings_path'];

// 检查是否已安装 - 检查配置文件和installed标志
$installed = false;
if (file_exists($siteSettingsFile)) {
    $config = include $siteSettingsFile;
    // 检查installed标志
    $installed = isset($config['installed']) && $config['installed'] === true;

    // 如果标志为true，再检查数据库是否存在
    if ($installed && isset($config['db_path'])) {
        $dbPath = $config['db_path'];
        // 检查数据库文件是否存在
        if (!file_exists($dbPath)) {
            $installed = false;
        }
    }

    // 如果已完全安装，则退出
    if ($installed) {
        echo json_encode(['success' => false, 'message' => '系统已安装，如需重新安装请先删除数据库文件']);
        exit;
    }
}

// 检查是否POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

// 获取JSON数据
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// 验证必填字段
$requiredFields = ['siteTitle', 'siteSubtitle', 'dbPath', 'username', 'password', 'frontendUrl'];
foreach ($requiredFields as $field) {
    if (empty($data[$field])) {
        echo json_encode(['success' => false, 'message' => "{$field} 字段不能为空"]);
        exit;
    }
}

// 从前端传递的参数中获取前端URL
$frontendUrl = isset($data['frontendUrl']) ? $data['frontendUrl'] : '';

// 确保URL以斜杠结尾
if ($frontendUrl && substr($frontendUrl, -1) !== '/') {
    $frontendUrl .= '/';
}

// 创建数据库目录
$relativePath = $data['dbPath'];
// 使用绝对路径，不做任何处理，直接存储绝对路径
$dbFullPath = __WWWROOT__ . DS . $relativePath;
$dbDir = dirname($dbFullPath);

if (!is_dir($dbDir)) {
    if (!mkdir($dbDir, 0755, true)) {
        echo json_encode(['success' => false, 'message' => "无法创建数据库目录: {$dbDir}"]);
        exit;
    }
}

try {
    // 创建数据库并初始化表结构
    $db = new SQLite3($dbFullPath);

    // 创建管理员表
    $db->exec('
    CREATE TABLE admins (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        salt TEXT NOT NULL,
        last_login INTEGER,
        created_at INTEGER NOT NULL,
        role_id INTEGER DEFAULT 1
    )');

    // 创建角色表
    $db->exec('
    CREATE TABLE admin_roles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        permissions TEXT NOT NULL,
        description TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
    )');

    // 创建分类表 (树状多级分类)
    $db->exec('
    CREATE TABLE categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        parent_id INTEGER DEFAULT 0,
        level INTEGER DEFAULT 0,
        path TEXT DEFAULT "",
        sort INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (parent_id) REFERENCES categories(id)
    )');

    // 创建软件表
    $db->exec('
    CREATE TABLE softwares (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        category INTEGER,
        icon TEXT,
        description TEXT,
        version TEXT,
        size TEXT,
        downloads INTEGER DEFAULT 0,
        fake_downloads TEXT,
        price TEXT,
        video_url TEXT,
        download_url_1 TEXT,
        download_url_2 TEXT,
        download_url_3 TEXT,
        download_url_4 TEXT,
        download_url_5 TEXT,
        buy_url TEXT,
        sort INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (category) REFERENCES categories(id)
    )');

    // 创建下载日志表
    $db->exec('
    CREATE TABLE download_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        software_id INTEGER NOT NULL,
        ip TEXT NOT NULL,
        user_agent TEXT,
        referer TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (software_id) REFERENCES softwares(id)
    )');

    // 创建支付订单表
    $db->exec('
    CREATE TABLE payment_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_no TEXT NOT NULL UNIQUE,
        software_id INTEGER NOT NULL,
        user_ip TEXT NOT NULL,
        amount REAL NOT NULL,
        payment_type TEXT NOT NULL,
        status TEXT NOT NULL,
        transaction_id TEXT,
        created_at INTEGER NOT NULL,
        paid_at INTEGER,
        FOREIGN KEY (software_id) REFERENCES softwares(id)
    )');

    // 创建下载权限表
    $db->exec('
    CREATE TABLE download_permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        software_id INTEGER NOT NULL,
        ip TEXT NOT NULL,
        order_no TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (software_id) REFERENCES softwares(id),
        FOREIGN KEY (order_no) REFERENCES payment_orders(order_no)
    )');

    // 创建支付通知记录表
    $db->exec('
    CREATE TABLE payment_notify_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        payment_type TEXT NOT NULL,
        raw_data TEXT NOT NULL,
        order_no TEXT,
        signature_valid INTEGER DEFAULT -1,
        signature_error TEXT,
        processing_result TEXT DEFAULT "error",
        processing_error TEXT,
        ip_address TEXT NOT NULL,
        request_line TEXT NOT NULL,
        request_headers TEXT NOT NULL,
        server_info TEXT NOT NULL,
        created_at INTEGER NOT NULL
    )');

    // 生成随机盐值
    $salt = bin2hex(random_bytes(16));

    // 哈希密码
    $passwordHash = password_hash($data['password'] . $salt, PASSWORD_DEFAULT);

    // 添加默认角色
    $now = time();
    $defaultRoles = [
        [
            'name' => '超级管理员',
            'permissions' => json_encode(['all']),
            'description' => '拥有系统所有权限'
        ],
        [
            'name' => '内容管理员',
            'permissions' => json_encode(['software.view', 'software.add', 'software.edit', 'software.delete', 'category.view', 'category.add', 'category.edit', 'category.delete']),
            'description' => '可以管理软件和分类内容'
        ],
        [
            'name' => '查看员',
            'permissions' => json_encode(['software.view', 'category.view', 'stats.view']),
            'description' => '仅有查看权限'
        ]
    ];

    foreach ($defaultRoles as $role) {
        $stmt = $db->prepare('
            INSERT INTO admin_roles (name, permissions, description, created_at, updated_at)
            VALUES (:name, :permissions, :description, :created_at, :updated_at)
        ');
        $stmt->bindValue(':name', $role['name'], SQLITE3_TEXT);
        $stmt->bindValue(':permissions', $role['permissions'], SQLITE3_TEXT);
        $stmt->bindValue(':description', $role['description'], SQLITE3_TEXT);
        $stmt->bindValue(':created_at', $now, SQLITE3_INTEGER);
        $stmt->bindValue(':updated_at', $now, SQLITE3_INTEGER);
        $stmt->execute();
    }

    // 创建管理员账户
    $stmt = $db->prepare('INSERT INTO admins (username, password, salt, role_id, created_at) VALUES (:username, :password, :salt, :role_id, :created_at)');
    $stmt->bindValue(':username', $data['username'], SQLITE3_TEXT);
    $stmt->bindValue(':password', $passwordHash, SQLITE3_TEXT);
    $stmt->bindValue(':salt', $salt, SQLITE3_TEXT);
    $stmt->bindValue(':role_id', 1, SQLITE3_INTEGER); // 超级管理员角色
    $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
    $stmt->execute();

    // 创建示例分类 (树状多级分类)
    $mainCategories = [
        '远程软件' => ['远程控制', '远程桌面', '远程协助'],
        'Autodesk软件' => ['AutoCAD', '3ds Max', 'Maya', 'Revit'],
        '三维设计' => ['3D建模', '3D渲染', '3D打印'],
        '办公软件' => ['文档处理', '表格处理', '演示文稿', '邮件客户端'],
        '媒体播放' => ['视频播放', '音频播放', '图像查看'],
        '安全防护' => ['杀毒软件', '防火墙', '系统优化'],
        '网络工具' => ['浏览器', '下载工具', '网络监控'],
        '游戏娱乐' => ['单机游戏', '网络游戏', '游戏工具'],
        '教育学习' => ['语言学习', '编程学习', '考试培训']
    ];

    $currentTime = time();

    // 先创建主分类
    foreach ($mainCategories as $mainCategory => $subCategories) {
        $stmt = $db->prepare('INSERT INTO categories (name, parent_id, level, path, sort, created_at) VALUES (:name, :parent_id, :level, :path, :sort, :created_at)');
        $stmt->bindValue(':name', $mainCategory, SQLITE3_TEXT);
        $stmt->bindValue(':parent_id', 0, SQLITE3_INTEGER); // 主分类的parent_id为0
        $stmt->bindValue(':level', 0, SQLITE3_INTEGER); // 主分类的level为0
        $stmt->bindValue(':path', '', SQLITE3_TEXT); // 主分类的path为空
        $stmt->bindValue(':sort', array_search($mainCategory, array_keys($mainCategories)), SQLITE3_INTEGER);
        $stmt->bindValue(':created_at', $currentTime, SQLITE3_INTEGER);
        $stmt->execute();

        $parentId = $db->lastInsertRowID();
        $parentPath = $parentId;

        // 创建子分类
        foreach ($subCategories as $index => $subCategory) {
            $stmt = $db->prepare('INSERT INTO categories (name, parent_id, level, path, sort, created_at) VALUES (:name, :parent_id, :level, :path, :sort, :created_at)');
            $stmt->bindValue(':name', $subCategory, SQLITE3_TEXT);
            $stmt->bindValue(':parent_id', $parentId, SQLITE3_INTEGER);
            $stmt->bindValue(':level', 1, SQLITE3_INTEGER); // 子分类的level为1
            $stmt->bindValue(':path', $parentPath, SQLITE3_TEXT);
            $stmt->bindValue(':sort', $index, SQLITE3_INTEGER);
            $stmt->bindValue(':created_at', $currentTime, SQLITE3_INTEGER);
            $stmt->execute();
        }
    }

    // 添加示例软件数据
    $sampleSoftware = [
        [
            'name' => 'Todesk远程软件',
            'category' => 1, // 远程软件
            'icon' => 'https://nbbrj.com/png/todesk.png',
            'description' => 'ToDesk 是一款多平台远程协作软件，支持主流操作系统Windows、Linux、Mac、Android、iOS跨平台协同操作。',
            'version' => 'v4.7.7.0',
            'size' => '25.27 MB',
            'fake_downloads' => '12.8',
            'price' => '免费版',
            'download_url' => 'https://dl.todesk.com/windows/ToDesk_Lite.exe',
            'video_url' => 'https://www.youtube.com/watch?v=sample1'
        ],
        [
            'name' => 'DeskIn远程【国外专用】',
            'category' => 1, // 远程软件
            'icon' => 'https://nbbrj.com/png/DeskIn.png',
            'description' => '全息3D设计工具，支持神经接口输入和多人协同创作，设计师的未来之选。可在虚拟空间中直接建模，实时渲染效果惊艳。',
            'version' => 'v3.3.0',
            'size' => '54.54 MB',
            'fake_downloads' => '0.7',
            'price' => '免费版',
            'download_url' => 'https://dl.deskin.io/windows/DeskIn_Setup_v3.3.0.0_x64.exe',
            'video_url' => 'https://www.youtube.com/watch?v=sample2'
        ],
        [
            'name' => 'AutoCAD下载',
            'category' => 2, // Autodesk软件
            'icon' => 'https://nbbrj.com/png/2023.png',
            'description' => '点击立即下载，下载的工具运行，把客服发您的密码输入进去就可以极速下载，安装教程都在程序里面。全套2007-2026版本',
            'version' => 'v4.7.7.0',
            'size' => '25.27 MB',
            'fake_downloads' => '12.8',
            'price' => '付费版，可自助购买，自动发密码',
            'download_url' => 'https://nbbrj.com/Setup/CAD_Setup.exe',
            'video_url' => 'https://www.youtube.com/watch?v=sample3',
            'baidu_url' => 'https://pan.baidu.com/s/1-TfOfcSCaKzzjt7mkTz5Cw',
            'buy_url' => 'https://item.taobao.com/item.htm?id=744987530810'
        ],
        [
            'name' => 'CAD天正插件',
            'category' => 2, // Autodesk软件
            'icon' => 'https://nbbrj.com/png/tz.png',
            'description' => 'CAD天正插件，包含，T20V8 T30V1支持CAD2010-2026.包含【建筑/暖通/电气/给排水/结构】',
            'version' => 'v8 T30v1',
            'size' => '2.6 MB',
            'fake_downloads' => '0.8',
            'price' => '付费版，可自助购买，自动发密码',
            'download_url' => 'https://nbbrj.com/Setup/TZ_Setup.exe',
            'video_url' => 'https://www.youtube.com/watch?v=sample4',
            'baidu_url' => 'https://pan.baidu.com/s/1B1VYO6XwPfkfHoFyrBqsuQ',
            'buy_url' => 'https://item.taobao.com/item.htm?ft=t&id=745088871503&spm=a21dvs.23580594.0.0.52de2c1bcfk5qG'
        ],
        [
            'name' => 'SketchupPro_草图大师',
            'category' => 3, // 三维设计
            'icon' => 'https://nbbrj.com/png/Su.png',
            'description' => 'su版本包含2016-2025版本/并且还包含了VR插件4-5-6-7版本的VR插件，支持Su2016-2025',
            'version' => 'v8 T30v1',
            'size' => '2.6 MB',
            'fake_downloads' => '0.8',
            'price' => '付费版，可自助购买，自动发密码',
            'download_url' => 'https://nbbrj.com/Setup/SketchUp_Setup.exe',
            'video_url' => 'https://www.youtube.com/watch?v=sample5',
            'baidu_url' => 'https://pan.baidu.com/s/1mcFgL-KvSEzkossR4Rn3TA',
            'buy_url' => 'https://item.taobao.com/item.htm?id=922510812579&spm=a213gs.v2success.0.0.4e724831Wu7MUU'
        ]
    ];

    foreach ($sampleSoftware as $index => $software) {
        $stmt = $db->prepare('
            INSERT INTO softwares (
                name, category, icon, description, version, size,
                downloads, fake_downloads, price, video_url, download_url_1,
                download_url_2, download_url_3, download_url_4, download_url_5,
                buy_url, sort, created_at
            ) VALUES (
                :name, :category, :icon, :description, :version, :size,
                :downloads, :fake_downloads, :price, :video_url, :download_url_1,
                :download_url_2, :download_url_3, :download_url_4, :download_url_5,
                :buy_url, :sort, :created_at
            )
        ');

        $stmt->bindValue(':name', $software['name'], SQLITE3_TEXT);
        $stmt->bindValue(':category', $software['category'], SQLITE3_INTEGER);
        $stmt->bindValue(':icon', $software['icon'] ?? null, $software['icon'] ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':description', $software['description'] ?? null, $software['description'] ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':version', $software['version'] ?? null, $software['version'] ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':size', $software['size'] ?? null, $software['size'] ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':downloads', 0, SQLITE3_INTEGER);
        $stmt->bindValue(':fake_downloads', $software['fake_downloads'] ?? null, $software['fake_downloads'] ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':price', $software['price'] ?? null, $software['price'] ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':video_url', $software['video_url'] ?? null, isset($software['video_url']) ? SQLITE3_TEXT : SQLITE3_NULL);
        // 将原字段映射到新的数字字段
        $stmt->bindValue(':download_url_1', $software['download_url'] ?? null, isset($software['download_url']) ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':download_url_2', $software['backup_download_url'] ?? null, isset($software['backup_download_url']) ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':download_url_3', $software['baidu_url'] ?? null, isset($software['baidu_url']) ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':download_url_4', null, SQLITE3_NULL);
        $stmt->bindValue(':download_url_5', null, SQLITE3_NULL);
        $stmt->bindValue(':buy_url', $software['buy_url'] ?? null, isset($software['buy_url']) ? SQLITE3_TEXT : SQLITE3_NULL);
        $stmt->bindValue(':sort', 5 - $index, SQLITE3_INTEGER); // 逆序排列，使示例软件显示在前面
        $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);

        $stmt->execute();
    }

    // 关闭数据库连接
    $db->close();

    // 创建或更新站点设置文件
    if (file_exists($siteSettingsFile)) {
        // 如果站点设置文件已存在，读取现有配置
        $existingConfig = include $siteSettingsFile;

        // 记录安装时间（如果已存在则保留原有时间）
        $installedTime = $existingConfig['installed_time'] ?? time();

        // 更新配置项，只保留必要的参数，其他使用现有值或由 Settings 类处理默认值
        $config = [
            'installed' => true,
            'basic' => [
                'site_title' => $data['siteTitle'] ?? $existingConfig['basic']['site_title'] ?? '',
                'site_subtitle' => $data['siteSubtitle'] ?? $existingConfig['basic']['site_subtitle'] ?? '',
                'frontend_url' => $frontendUrl ?? $existingConfig['basic']['frontend_url'] ?? '',
            ],
            'db_path' => $dbFullPath,
            'installed_time' => $installedTime, // 添加安装时间
            'admin' => [
                'username' => $data['username'] ?? $existingConfig['admin']['username'] ?? '',
                'password' => $data['password'],  // 救援模式明文密码
                'rescue_mode' => true  // 标记为救援模式
            ]
        ];

        // 保留现有的其他设置（如果有）
        foreach ($existingConfig as $key => $value) {
            if (!isset($config[$key]) && $key !== 'basic' && $key !== 'admin') {
                $config[$key] = $value;
            }
        }
    } else {
        // 创建新配置
        $installedTime = time(); // 记录安装时间
        // 只保留安装时必要的参数，其他默认值由 Settings 类处理
        $config = [
            'installed' => true,
            'basic' => [
                'site_title' => $data['siteTitle'],
                'site_subtitle' => $data['siteSubtitle'],
                'frontend_url' => $frontendUrl,
            ],
            'db_path' => $dbFullPath,
            'installed_time' => $installedTime, // 添加安装时间
            'admin' => [
                'username' => $data['username'],
                'password' => $data['password'],  // 救援模式明文密码
                'rescue_mode' => true  // 标记为救援模式
            ]
            // 其他所有设置使用 Settings 类中的默认值
        ];
    }

    $configContent = "<?php\nreturn " . var_export($config, true) . ";\n";
    if (file_put_contents($siteSettingsFile, $configContent) === false) {
        // 如果站点设置文件写入失败，删除数据库文件
        unlink($dbFullPath);
        echo json_encode(['success' => false, 'message' => '无法写入站点设置文件']);
        exit;
    }

    echo json_encode(['success' => true, 'message' => '安装成功']);

} catch (Exception $e) {
    // 安装失败，删除数据库文件（如果存在）
    if (file_exists($dbFullPath)) {
        unlink($dbFullPath);
    }

    echo json_encode(['success' => false, 'message' => '安装失败: ' . $e->getMessage()]);
}