<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
/**
 * 支付通知处理
 */

// 包含公共API验证文件
require_once dirname(__DIR__) . DS . 'public.php';

// 包含支付通知处理类
require_once dirname(__DIR__) . DS . 'includes' . DS . 'PaymentNotifyHandler.php';

// 包含支付签名验证类
require_once dirname(__DIR__) . DS . 'includes' . DS . 'PaymentSignature.php';

// 获取数据库连接
$db = get_db_connection();

// 创建支付通知处理器
$handler = new PaymentNotifyHandler($db);

// 获取支付类型（可选参数）
$paymentType = isset($_GET['type']) ? $_GET['type'] : '';

// 获取原始通知数据
$rawData = file_get_contents('php://input');

// 获取POST数据
$postData = $_POST;

// 对于支付宝，如果raw_data为空但有POST数据，则将POST数据序列化作为raw_data
if (empty($rawData) && !empty($postData)) {
    $rawData = http_build_query($postData);
}

// 获取完整的请求信息
$requestInfo = collect_request_info();

// 记录通知日志到文件（保持原有功能）
$logFile = dirname(dirname(__DIR__)) . '/logs/payment_notify.log';
$logDir = dirname($logFile);

// 确保日志目录存在
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// 使用新的处理器处理支付通知
$result = $handler->processNotify($paymentType, $rawData, $postData, true);

// 记录通知内容到文件
$detectedType = $result['payment_type'] ?: 'unknown';
$logContent = date('Y-m-d H:i:s') . " [{$detectedType}] Received notification:\n" . $rawData . "\n";
$logContent .= "Processing result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
$logContent .= "Message: " . $result['message'] . "\n\n";
file_put_contents($logFile, $logContent, FILE_APPEND);

// 准备数据库日志数据
$notifyLogData = [
    'payment_type' => $result['payment_type'],
    'raw_data' => $rawData,
    'order_no' => $result['order_no'],
    'signature_valid' => $result['signature_valid'] ? 1 : 0,
    'signature_error' => $result['signature_error'],
    'processing_result' => $result['success'] ? 'success' : 'error',
    'processing_error' => $result['success'] ? null : $result['message'],
    'ip_address' => $requestInfo['ip_address'],
    'request_line' => $requestInfo['request_line'],
    'request_headers' => json_encode($requestInfo['request_headers'], JSON_UNESCAPED_UNICODE),
    'server_info' => json_encode($requestInfo['server_info'], JSON_UNESCAPED_UNICODE),
    'created_at' => time()
];

// 保存通知日志到数据库
$handler->saveNotifyLog($notifyLogData);

// 生成并输出响应
$response = $handler->generateResponse($result['payment_type'], $result['success'], $result['message']);
echo $response;

/**
 * 收集完整的请求信息
 *
 * @return array 请求信息数组
 */







/**
 * 收集完整的请求信息
 *
 * @return array 请求信息数组
 */
function collect_request_info() {
    // 获取客户端IP
    $ipAddress = get_client_ip();

    // 获取所有请求头
    $headers = [];
    if (function_exists('getallheaders')) {
        $headers = getallheaders();
    } else {
        // 备用方法获取请求头
        foreach ($_SERVER as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header = str_replace('_', '-', substr($key, 5));
                $headers[$header] = $value;
            }
        }
    }

    // 不再单独处理POST数据，raw_data已经包含了原始POST数据

    // 获取服务器信息
    $serverInfo = [
        'server_name' => $_SERVER['SERVER_NAME'] ?? '',
        'server_port' => $_SERVER['SERVER_PORT'] ?? '',
        'server_protocol' => $_SERVER['SERVER_PROTOCOL'] ?? '',
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? '',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? '',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? '',
        'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? '',
        'request_time' => $_SERVER['REQUEST_TIME'] ?? time(),
        'request_time_float' => $_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true),
        'https' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off',
        'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? '',
        'remote_port' => $_SERVER['REMOTE_PORT'] ?? '',
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? '',
        'content_length' => $_SERVER['CONTENT_LENGTH'] ?? 0
    ];

    // 构建HTTP请求行
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    $requestUri = $_SERVER['REQUEST_URI'] ?? '';
    $requestProtocol = $_SERVER['SERVER_PROTOCOL'] ?? 'HTTP/1.1';
    $requestLine = "{$requestMethod} {$requestUri} {$requestProtocol}";

    return [
        'ip_address' => $ipAddress,
        'request_line' => $requestLine, // HTTP请求行：METHOD URI PROTOCOL
        'request_headers' => $headers, // 返回原始数组，让调用方决定如何处理
        'server_info' => $serverInfo // 返回原始数组，让调用方决定如何处理
    ];
}



