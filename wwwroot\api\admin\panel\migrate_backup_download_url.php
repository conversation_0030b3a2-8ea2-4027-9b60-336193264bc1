<?php
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 检查权限
require_permission('admin.edit');

try {
    // 获取数据库连接
    $db = get_db_connection();

    // 检查当前表结构
    $tableInfo = $db->query("PRAGMA table_info(softwares)");
    $columns = [];
    while ($row = $tableInfo->fetchArray(SQLITE3_ASSOC)) {
        $columns[] = $row['name'];
    }

    $hasNewFields = in_array('download_url_1', $columns);
    $hasOldFields = in_array('download_url', $columns);

    if ($hasNewFields) {
        json_response([
            'success' => true,
            'message' => '数字化下载URL字段已存在，无需迁移'
        ]);
        exit;
    }

    if (!$hasOldFields) {
        json_response([
            'success' => false,
            'message' => '未找到原有字段，无法进行迁移'
        ], 400);
        exit;
    }

    // 开始迁移过程
    $db->exec('BEGIN TRANSACTION');

    try {
        // 添加新的数字字段
        $db->exec('ALTER TABLE softwares ADD COLUMN download_url_1 TEXT');
        $db->exec('ALTER TABLE softwares ADD COLUMN download_url_2 TEXT');
        $db->exec('ALTER TABLE softwares ADD COLUMN download_url_3 TEXT');
        $db->exec('ALTER TABLE softwares ADD COLUMN download_url_4 TEXT');
        $db->exec('ALTER TABLE softwares ADD COLUMN download_url_5 TEXT');

        // 迁移数据：将旧字段数据复制到新字段
        $db->exec('UPDATE softwares SET download_url_1 = download_url WHERE download_url IS NOT NULL AND download_url != ""');

        if (in_array('backup_download_url', $columns)) {
            $db->exec('UPDATE softwares SET download_url_2 = backup_download_url WHERE backup_download_url IS NOT NULL AND backup_download_url != ""');
        }

        if (in_array('baidu_url', $columns)) {
            $db->exec('UPDATE softwares SET download_url_3 = baidu_url WHERE baidu_url IS NOT NULL AND baidu_url != ""');
        }

        $db->exec('COMMIT');

        json_response([
            'success' => true,
            'message' => '成功迁移到数字化下载URL字段结构'
        ]);

    } catch (Exception $e) {
        $db->exec('ROLLBACK');
        throw $e;
    }

} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '迁移失败: ' . $e->getMessage()
    ], 500);
}
