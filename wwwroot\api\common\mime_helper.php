<?php
if (!function_exists('mime_content_type')) {
    /**
     * 获取文件的MIME类型
     * 
     * @param string $filename 文件路径
     * @return string MIME类型
     */
    function mime_content_type($filename) {
        // 常见文件扩展名对应的MIME类型
        $mime_types = array(
            // 文本文件
            'txt' => 'text/plain',
            'htm' => 'text/html',
            'html' => 'text/html',
            'php' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'swf' => 'application/x-shockwave-flash',
            'flv' => 'video/x-flv',

            // 图片
            'png' => 'image/png',
            'jpe' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'jpg' => 'image/jpeg',
            'gif' => 'image/gif',
            'bmp' => 'image/bmp',
            'ico' => 'image/vnd.microsoft.icon',
            'tiff' => 'image/tiff',
            'tif' => 'image/tiff',
            'svg' => 'image/svg+xml',
            'svgz' => 'image/svg+xml',
            'webp' => 'image/webp',

            // 压缩文件
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            '7z' => 'application/x-7z-compressed',
            'exe' => 'application/x-msdownload',
            'msi' => 'application/x-msdownload',
            'cab' => 'application/vnd.ms-cab-compressed',
            'tar' => 'application/x-tar',
            'gz' => 'application/gzip',
            'tgz' => 'application/gzip',

            // 音频/视频
            'mp3' => 'audio/mpeg',
            'mp4' => 'video/mp4',
            'qt' => 'video/quicktime',
            'mov' => 'video/quicktime',
            'avi' => 'video/x-msvideo',
            'wmv' => 'video/x-ms-wmv',
            'wav' => 'audio/wav',
            'ogg' => 'audio/ogg',
            'webm' => 'video/webm',

            // Adobe
            'pdf' => 'application/pdf',
            'psd' => 'image/vnd.adobe.photoshop',
            'ai' => 'application/postscript',
            'eps' => 'application/postscript',
            'ps' => 'application/postscript',

            // MS Office
            'doc' => 'application/msword',
            'rtf' => 'application/rtf',
            'xls' => 'application/vnd.ms-excel',
            'ppt' => 'application/vnd.ms-powerpoint',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',

            // Open Office
            'odt' => 'application/vnd.oasis.opendocument.text',
            'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
            'odp' => 'application/vnd.oasis.opendocument.presentation',
            
            // 其他常见格式
            'dmg' => 'application/x-apple-diskimage',
            'pkg' => 'application/x-newton-compatible-pkg',
            'deb' => 'application/x-debian-package',
            'rpm' => 'application/x-rpm',
            'apk' => 'application/vnd.android.package-archive',
        );

        // 获取文件扩展名
        $path_parts = pathinfo($filename);
        $ext = isset($path_parts['extension']) ? strtolower($path_parts['extension']) : '';
        
        // 检查扩展名是否在已知MIME类型列表中
        if (array_key_exists($ext, $mime_types)) {
            return $mime_types[$ext];
        }
        // 尝试使用finfo扩展
        elseif (function_exists('finfo_open')) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimetype = finfo_file($finfo, $filename);
            finfo_close($finfo);
            return $mimetype;
        }
        // 如果无法确定MIME类型，返回默认值
        else {
            return 'application/octet-stream';
        }
    }
}
?>
