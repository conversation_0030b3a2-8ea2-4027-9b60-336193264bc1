<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 启动输出缓冲区
ob_start();

// 包含公共API验证文件
require_once dirname(__DIR__) . DS . 'public.php';

// 包含支付函数库
require_once dirname(__DIR__) . DS . 'payment.php';

// 获取软件ID、下载令牌和时间戳
$id = get_param('id', null);
$token = get_param('token', null);
$timestamp = get_param('ts', null);
$orderNo = get_param('order_no', null);
$urlIndex = get_param('url_index', 1); // 默认使用第一个下载URL

// 验证参数
if (empty($id) || !is_numeric($id)) {
    json_response(['success' => false, 'message' => '无效的软件ID'], 400);
}

// 验证URL索引
if (!is_numeric($urlIndex) || $urlIndex < 1 || $urlIndex > 5) {
    $urlIndex = 1; // 默认使用第一个下载URL
}

// 获取数据库连接
$db = get_db_connection();

// 查询软件信息，包含所有下载URL字段
$stmt = $db->prepare('
    SELECT id, name, price,
           download_url_1, download_url_2, download_url_3, download_url_4, download_url_5
    FROM softwares WHERE id = :id
');
$stmt->bindValue(':id', $id, SQLITE3_INTEGER);
$result = $stmt->execute();
$software = $result->fetchArray(SQLITE3_ASSOC);

if (!$software) {
    json_response(['success' => false, 'message' => '软件不存在'], 404);
}

// 获取指定索引的下载URL
$downloadUrlField = "download_url_{$urlIndex}";
$downloadUrl = $software[$downloadUrlField] ?? null;

// 如果指定的URL为空，尝试查找第一个可用的下载URL
if (empty($downloadUrl)) {
    for ($i = 1; $i <= 5; $i++) {
        $field = "download_url_{$i}";
        if (!empty($software[$field])) {
            $downloadUrl = $software[$field];
            $urlIndex = $i;
            break;
        }
    }
}

// 如果仍然没有找到下载URL，返回错误
if (empty($downloadUrl)) {
    json_response(['success' => false, 'message' => '该软件没有配置下载链接'], 404);
}

// 检查软件是否需要付费
$needPayment = !empty($software['price']) && floatval($software['price']) > 0;

// 如果需要付费，检查支付状态
if ($needPayment) {
    // 获取客户端IP
    $clientIp = get_client_ip();

    // 检查是否有支付记录
    $hasPaid = false;

    // 如果提供了订单号，检查订单状态
    if (!empty($orderNo)) {
        $stmt = $db->prepare('
            SELECT * FROM payment_orders
            WHERE software_id = :software_id AND order_no = :order_no AND status = :status
        ');
        $stmt->bindValue(':software_id', $software['id'], SQLITE3_INTEGER);
        $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
        $stmt->bindValue(':status', 'paid', SQLITE3_TEXT);
        $result = $stmt->execute();
        $order = $result->fetchArray(SQLITE3_ASSOC);

        if ($order) {
            $hasPaid = true;
        }
    }

    // 如果没有找到已支付的订单，检查是否有下载权限
    if (!$hasPaid) {
        $stmt = $db->prepare('
            SELECT * FROM download_permissions
            WHERE software_id = :software_id AND ip = :ip
        ');
        $stmt->bindValue(':software_id', $software['id'], SQLITE3_INTEGER);
        $stmt->bindValue(':ip', $clientIp, SQLITE3_TEXT);
        $result = $stmt->execute();
        $permission = $result->fetchArray(SQLITE3_ASSOC);

        if ($permission) {
            $hasPaid = true;
        }
    }

    // 如果没有支付，返回需要支付的信息
    if (!$hasPaid) {
        // 检查支付功能是否启用
        if (!check_payment_config()) {
            json_response(['success' => false, 'message' => '该软件需要付费下载，但支付功能未启用'], 402);
        }

        json_response([
            'success' => false,
            'message' => '该软件需要付费下载',
            'need_payment' => true,
            'software' => [
                'id' => $software['id'],
                'name' => $software['name'],
                'price' => floatval($software['price'])
            ]
        ], 402);
    }
}

// 验证下载令牌和时间戳
if (empty($timestamp) || !is_numeric($timestamp)) {
    json_response(['success' => false, 'message' => '无效的时间戳'], 400);
}

// 检查时间戳是否在有效期内（5分钟内）
$currentTime = time();
$timeDiff = abs($currentTime - $timestamp);
$maxTimeWindow = 300; // 5分钟 = 300秒

// 添加调试日志
error_log("下载验证: 软件ID={$software['id']}, 时间戳={$timestamp}, 当前时间={$currentTime}, 时间差={$timeDiff}秒");

// 检查时间窗口
if ($timeDiff > $maxTimeWindow) {
    json_response(['success' => false, 'message' => '下载链接已过期，请刷新页面重试'], 403);
}

// 验证令牌
$expectedToken = md5($software['id'] . '_' . $timestamp);

// 添加调试日志
error_log("令牌验证: 期望令牌={$expectedToken}, 实际令牌={$token}");

if ($token !== $expectedToken) {
    json_response(['success' => false, 'message' => '下载令牌无效'], 403);
}

// 记录下载次数
$stmt = $db->prepare('UPDATE softwares SET downloads = downloads + 1 WHERE id = :id');
$stmt->bindValue(':id', $id, SQLITE3_INTEGER);
$stmt->execute();

// 记录下载日志 (可选)
$ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

// 创建下载日志表（如果不存在）
$db->exec('
CREATE TABLE IF NOT EXISTS download_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    software_id INTEGER NOT NULL,
    ip TEXT,
    user_agent TEXT,
    created_at INTEGER NOT NULL,
    FOREIGN KEY (software_id) REFERENCES softwares(id)
)');

// 记录日志
$stmt = $db->prepare('
    INSERT INTO download_logs (software_id, ip, user_agent, created_at)
    VALUES (:software_id, :ip, :user_agent, :created_at)
');
$stmt->bindValue(':software_id', $id, SQLITE3_INTEGER);
$stmt->bindValue(':ip', $ip, SQLITE3_TEXT);
$stmt->bindValue(':user_agent', $userAgent, SQLITE3_TEXT);
$stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
$stmt->execute();

// 检查下载URL是否为本地文件
$config = get_config();

// 检查是否为本地文件路径（以/开头）
if (strpos($downloadUrl, '/') === 0) {
    // 获取文件路径
    $filePath = dirname(dirname(__DIR__)) . $downloadUrl;

    // 检查文件是否存在
    if (!file_exists($filePath)) {
        json_response(['success' => false, 'message' => '文件不存在'], 404);
    }

    // 获取文件信息
    $fileInfo = pathinfo($filePath);
    $fileSize = filesize($filePath);

    require_once '../common/mime_helper.php';
    $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';

    // 使用软件名称作为下载文件名（如果可用）
    $fileName = $software['name'];
    if (!empty($fileName)) {
        // 添加原始文件扩展名
        $fileName .= '.' . $fileInfo['extension'];
    } else {
        // 如果没有软件名称，使用原始文件名
        $fileName = $fileInfo['basename'];
    }

    // 设置响应头
    header('Content-Description: File Transfer');
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . $fileName . '"');
    header('Content-Length: ' . $fileSize);
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    // 清除输出缓冲区（如果存在）
    if (ob_get_level()) {
        ob_clean();
    } else {
        // 如果没有活动的输出缓冲区，启动一个新的
        ob_start();
    }
    flush();

    // 读取文件并输出
    readfile($filePath);
    exit;
} else {
    // 重定向到外部URL
    header('Location: ' . $downloadUrl);
    exit;
}
