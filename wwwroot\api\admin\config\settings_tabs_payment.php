<?php
/**
 * 管理后台支付设置选项卡配置
 *
 * 该文件定义了管理后台支付设置页面的选项卡、分组和字段结构
 * 仅负责UI展示和交互，不包含具体参数的默认值
 */

return [
    // 支付设置选项卡
    [
        'name' => 'payment',
        'title' => '支付设置',
        'icon' => 'fas fa-credit-card',
        'groups' => [
            // 支付设置组
            [
                'name' => 'payment_settings',
                'title' => '支付设置',
                'settings_path' => 'payment', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'payment.enabled',
                        'label' => '启用支付功能',
                        'type' => 'radio',
                        'options' => [
                            ['value' => 'true', 'label' => '启用'],
                            ['value' => 'false', 'label' => '禁用']
                        ],
                        'valueType' => 'boolean' // 指示前端将值转换为布尔值
                    ],
                    // 微信支付设置
                    [
                        'name' => 'payment.wechat_pay.enabled',
                        'label' => '启用微信支付',
                        'type' => 'checkbox',
                        'condition' => 'payment.enabled'
                    ],
                    [
                        'name' => 'payment.wechat_pay.mch_id',
                        'label' => '商户ID (mch_id)',
                        'type' => 'text',
                        'condition' => 'payment.wechat_pay.enabled'
                    ],
                    [
                        'name' => 'payment.wechat_pay.app_id',
                        'label' => '应用ID (app_id)',
                        'type' => 'text',
                        'condition' => 'payment.wechat_pay.enabled'
                    ],
                    [
                        'name' => 'payment.wechat_pay.key',
                        'label' => 'API密钥 (key)',
                        'type' => 'password',
                        'condition' => 'payment.wechat_pay.enabled'
                    ],
                    [
                        'name' => 'payment.wechat_pay.cert_path',
                        'label' => 'API证书路径',
                        'type' => 'text',
                        'condition' => 'payment.wechat_pay.enabled',
                        'description' => '相对于网站根目录的路径'
                    ],
                    [
                        'name' => 'payment.wechat_pay.key_path',
                        'label' => 'API证书密钥路径',
                        'type' => 'text',
                        'condition' => 'payment.wechat_pay.enabled',
                        'description' => '相对于网站根目录的路径'
                    ],
                    [
                        'name' => 'payment.wechat_pay.notify_url',
                        'label' => '支付通知URL',
                        'type' => 'text',
                        'condition' => 'payment.wechat_pay.enabled'
                    ],
                    // 支付宝设置
                    [
                        'name' => 'payment.alipay.enabled',
                        'label' => '启用支付宝',
                        'type' => 'checkbox',
                        'condition' => 'payment.enabled'
                    ],
                    [
                        'name' => 'payment.alipay.app_id',
                        'label' => '应用ID (app_id)',
                        'type' => 'text',
                        'condition' => 'payment.alipay.enabled'
                    ],
                    [
                        'name' => 'payment.alipay.private_key',
                        'label' => '商户私钥',
                        'type' => 'textarea',
                        'condition' => 'payment.alipay.enabled'
                    ],
                    [
                        'name' => 'payment.alipay.public_key',
                        'label' => '支付宝公钥',
                        'type' => 'textarea',
                        'condition' => 'payment.alipay.enabled'
                    ],
                    [
                        'name' => 'payment.alipay.notify_url',
                        'label' => '支付通知URL',
                        'type' => 'text',
                        'condition' => 'payment.alipay.enabled'
                    ],
                    [
                        'name' => 'payment.alipay.gateway',
                        'label' => '支付宝网关',
                        'type' => 'select',
                        'condition' => 'payment.alipay.enabled',
                        'options' => [
                            ['value' => 'https://openapi.alipay.com/gateway.do', 'label' => '正式环境'],
                            ['value' => 'https://openapi.alipaydev.com/gateway.do', 'label' => '沙箱环境']
                        ]
                    ]
                ]
            ]
        ]
    ]
];
