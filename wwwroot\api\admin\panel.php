<?php
/**
 * 管理员面板验证文件
 * 此文件应被所有管理面板下的API文件包含，用于验证管理员登录状态
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 定义API根目录常量
if (!defined('__API_ROOT__')) {
    define('__API_ROOT__', dirname(__DIR__));
}

// 包含公共函数库
require_once (__DIR__) . DS . 'common.php';

// 检查是否已安装，如果未安装则会自动重定向或返回JSON响应
check_installed(true);

// 启动会话
session_start();

// 验证管理员登录状态
if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
    // 未登录，返回401状态码
    header('HTTP/1.1 401 Unauthorized');
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => '未登录或会话已过期', 'code' => 'unauthorized']);
    exit;
}

// 登录时间超过24小时，需要重新登录
if (time() - $_SESSION['admin_login_time'] > 86400) {
    // 清除会话
    $_SESSION = [];
    session_destroy();

    // 返回401状态码
    header('HTTP/1.1 401 Unauthorized');
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => '会话已过期，请重新登录', 'code' => 'session_expired']);
    exit;
}

// 更新登录时间
$_SESSION['admin_login_time'] = time();

// 获取当前管理员ID和用户名，供API使用
$adminId = $_SESSION['admin_id'];
$adminUsername = $_SESSION['admin_username'];

/**
 * 检查当前管理员是否具有指定权限
 *
 * @param string|array $requiredPermission 所需的权限，可以是字符串或数组
 * @param bool $anyPermission 是否只需满足其中任一权限，默认为false（需满足所有权限）
 * @return bool 是否具有权限
 */
function check_permission($requiredPermission, $anyPermission = false) {
    global $adminId;

    // 获取数据库连接
    $db = get_db_connection();

    // 在救援模式下，视为超级管理员
    if (isset($_SESSION['admin_rescue_mode']) && $_SESSION['admin_rescue_mode']) {
        return true;
    }

    // 获取管理员角色
    $stmt = $db->prepare('
        SELECT r.permissions
        FROM admins a
        JOIN admin_roles r ON a.role_id = r.id
        WHERE a.id = :admin_id
    ');
    $stmt->bindValue(':admin_id', $adminId, SQLITE3_INTEGER);
    $result = $stmt->execute();
    $roleData = $result->fetchArray(SQLITE3_ASSOC);

    if (!$roleData) {
        return false; // 找不到角色数据
    }

    // 解析权限
    $permissions = json_decode($roleData['permissions'], true);

    // 如果具有all权限，直接返回true
    if (in_array('all', $permissions)) {
        return true;
    }

    // 将单个权限转换为数组
    if (!is_array($requiredPermission)) {
        $requiredPermission = [$requiredPermission];
    }

    // 检查是否具有所需权限
    if ($anyPermission) {
        // 只需满足其中任一权限
        foreach ($requiredPermission as $permission) {
            if (in_array($permission, $permissions)) {
                return true;
            }
        }
        return false;
    } else {
        // 需满足所有权限
        foreach ($requiredPermission as $permission) {
            if (!in_array($permission, $permissions)) {
                return false;
            }
        }
        return true;
    }
}

/**
 * 验证当前管理员是否具有所需权限，如果没有则终止请求
 *
 * @param string|array $requiredPermission 所需的权限，可以是字符串或数组
 * @param bool $anyPermission 是否只需满足其中任一权限，默认为false（需满足所有权限）
 */
function require_permission($requiredPermission, $anyPermission = false) {
    if (!check_permission($requiredPermission, $anyPermission)) {
        // 没有权限，返回403状态码
        header('HTTP/1.1 403 Forbidden');
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => '没有执行此操作的权限', 'code' => 'permission_denied']);
        exit;
    }
}