/**
 * 通用弹窗管理器
 */

class UniversalModal {
    constructor() {
        this.modal = document.getElementById('universalModal');
        this.content = document.getElementById('universalContent');
        this.currentType = null;
        this.currentData = null;

        // 检查元素是否存在
        if (!this.modal) {
            console.error('universalModal 元素未找到');
            return;
        }

        if (!this.content) {
            console.error('universalContent 元素未找到');
            return;
        }

        console.log('UniversalModal 构造函数完成，modal:', this.modal, 'content:', this.content);

        // 绑定点击外部关闭事件
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });
    }

    /**
     * 显示弹窗
     * @param {string} type 弹窗类型
     * @param {object} data 数据
     */
    show(type, data = {}) {
        console.log('UniversalModal.show 被调用，类型:', type, '数据:', data);

        if (!this.modal || !this.content) {
            console.error('弹窗元素未找到，无法显示');
            return;
        }

        this.currentType = type;
        this.currentData = data;

        // 根据类型生成内容
        const content = this.generateContent(type, data);
        console.log('生成的内容长度:', content.length);

        // 对于支付选择弹窗，内容已经包含完整的弹窗结构
        if (type === 'payment-options') {
            this.content.innerHTML = content;
            // 移除默认的弹窗内容样式，让新样式生效
            this.content.style.background = 'transparent';
            this.content.style.border = 'none';
            this.content.style.borderRadius = '0';
            this.content.style.padding = '0';
            this.content.style.boxShadow = 'none';
            this.content.style.animation = 'none'; // 移除默认的pulse动画
            this.content.style.width = 'auto'; // 让宽度自适应内容
            this.content.style.maxWidth = 'none'; // 移除最大宽度限制
        } else {
            this.content.innerHTML = content;
            // 恢复其他弹窗的默认样式
            this.content.style.background = 'linear-gradient(135deg, #001133, #003366)';
            this.content.style.border = '1px solid #00aaff';
            this.content.style.borderRadius = '10px';
            this.content.style.padding = '1.5rem';
            this.content.style.boxShadow = '0 0 30px rgba(0, 170, 255, 0.5)';
            this.content.style.animation = 'pulse 2s infinite'; // 恢复pulse动画
            this.content.style.width = '500px'; // 恢复固定宽度
            this.content.style.maxWidth = '90%'; // 恢复最大宽度限制
        }

        // 显示弹窗
        this.modal.style.display = 'flex';
        setTimeout(() => {
            this.modal.style.opacity = '1';
        }, 10);

        // 绑定事件
        this.bindEvents(type, data);

        console.log('弹窗显示完成');
    }

    /**
     * 隐藏弹窗
     */
    hide() {
        this.modal.style.opacity = '0';
        setTimeout(() => {
            this.modal.style.display = 'none';
            this.content.innerHTML = '';
            this.currentType = null;
            this.currentData = null;
        }, 300);
    }

    /**
     * 根据类型生成内容
     * @param {string} type 弹窗类型
     * @param {object} data 数据
     * @returns {string} HTML内容
     */
    generateContent(type, data) {
        switch (type) {
            case 'download':
                return this.generateDownloadContent(data);
            case 'payment-options':
                return this.generatePaymentOptionsContent(data);
            case 'payment-qr':
                return this.generatePaymentQRContent(data);
            case 'announcement':
                return this.generateAnnouncementContent(data);
            default:
                return '<p>未知的弹窗类型</p>';
        }
    }

    /**
     * 生成下载选项内容
     */
    generateDownloadContent(data) {
        const { software } = data;

        return `
            <span class="modal-close" id="modalClose" style="position: absolute; top: 10px; right: 15px; color: #aaccff; cursor: pointer; font-size: 1.5rem; font-weight: bold; line-height: 1;">&times;</span>

            <h3 style="color: #00ffff; font-size: 1.3rem; margin-bottom: 1rem; text-shadow: 0 0 10px #00aaff; margin-top: 0;">选择下载方式</h3>

            <div style="color: #aaccff; font-size: 0.9rem; margin: 1rem 0; padding: 0.8rem; background: rgba(0, 50, 100, 0.3); border-radius: 6px; border-left: 3px solid #00aaff; text-align: left;">
                <p style="margin: 0 0 0.5rem 0;"><strong>下载说明：</strong></p>
                <p style="margin: 0 0 0.3rem 0;">1. 请根据您的网络环境选择合适的下载方式</p>
                <p style="margin: 0 0 0.3rem 0;">2. 下载过程中请勿关闭页面</p>
                <p style="margin: 0 0 0.3rem 0;">3. 如遇下载失败，请尝试其他下载方式</p>
                <p style="margin: 0;">4. 下载完成后请检查文件完整性</p>
            </div>

            <div id="downloadOptions" style="display: flex; flex-direction: column; gap: 0.8rem; margin-bottom: 1.5rem;">
                <!-- 下载选项将在这里动态添加 -->
            </div>

            <button id="modalCancel" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">取消</button>
        `;
    }

    /**
     * 生成支付选择内容
     */
    generatePaymentOptionsContent(data) {
        const { software, availablePayments } = data;

        // 获取支付按钮布局配置
        const buttonLayout = (typeof paymentConfig !== 'undefined' && paymentConfig.button_layout)
            ? paymentConfig.button_layout
            : 'horizontal'; // 默认使用水平布局

        // 根据布局决定容器样式（使用Flexbox兼容早期Chrome，避免CSS Grid兼容性问题）
        const containerStyle = buttonLayout === 'horizontal'
            ? 'display: flex; flex-direction: row; margin-top: 1.5rem;'
            : 'display: flex; flex-direction: column; margin-top: 1.5rem;';

        // 根据布局决定按钮样式
        const wechatButtonStyle = buttonLayout === 'horizontal'
            ? 'flex: 1; margin-right: 0.5rem;'
            : 'margin-bottom: 0.5rem;';

        const alipayButtonStyle = buttonLayout === 'horizontal'
            ? 'flex: 1; margin-left: 0.5rem;'
            : 'margin-bottom: 0.5rem;';

        return `
            <div style="
                background-color: #111827;
                border-radius: 8px;
                box-shadow:
                    0 10px 15px -3px rgba(0, 0, 0, 0.1),
                    0 4px 6px -2px rgba(0, 0, 0, 0.05),
                    0 0 30px rgba(0, 170, 255, 0.3),
                    0 0 60px rgba(0, 170, 255, 0.1);
                border: 1px solid rgba(0, 170, 255, 0.3);
                width: 100%;
                max-width: 700px;
                padding: 1.5rem;
                position: relative;
                margin: 0 auto;
                animation: paymentPulse 3s ease-in-out infinite;
            ">
                <button id="modalClose" style="
                    position: absolute;
                    top: 0.75rem;
                    right: 0.75rem;
                    color: #9ca3af;
                    background: none;
                    border: none;
                    cursor: pointer;
                    font-size: 1.25rem;
                    transition: color 0.2s ease;
                ">
                    <i class="fas fa-times"></i>
                </button>

                <div style="text-align: center; margin-bottom: 1rem; width: 400px;">
                    <h3 style="
                        font-size: 1.25rem;
                        color: #93c5fd;
                        font-weight: 600;
                        margin: 0 0 0.5rem 0;
                    ">选择支付方式</h3>
                    <p style="
                        margin: 0.5rem 0;
                        color: #d1d5db;
                        font-size: 0.9rem;
                    ">软件: ${software.name}</p>
                    <p style="
                        color: #fbbf24;
                        font-weight: 500;
                        margin: 0;
                        font-size: 1rem;
                    ">价格: ${software.price || (software.price_value ? software.price_value.toFixed(2) + '元' : '免费')}</p>
                </div>

                <div style="${containerStyle}">
                    ${availablePayments.includes('wechat_pay') ? `
                        <button id="wechat-pay-btn" style="
                            background-color: #059669;
                            color: white;
                            padding: 1rem 2rem;
                            border-radius: 6px;
                            border: none;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: background-color 0.2s ease;
                            font-size: 0.9rem;
                            font-weight: 500;
                            ${wechatButtonStyle}
                        ">
                            <i class="fab fa-weixin" style="font-size: 1.5rem; margin-right: 0.5rem;"></i>
                            <span>微信支付</span>
                        </button>
                    ` : ''}

                    ${availablePayments.includes('alipay') ? `
                        <button id="alipay-btn" style="
                            background-color: #2563eb;
                            color: white;
                            padding: 1rem 2rem;
                            border-radius: 6px;
                            border: none;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: background-color 0.2s ease;
                            font-size: 0.9rem;
                            font-weight: 500;
                            ${alipayButtonStyle}
                        ">
                            <i class="fab fa-alipay" style="font-size: 1.5rem; margin-right: 0.5rem;"></i>
                            <span>支付宝</span>
                        </button>
                    ` : ''}
                </div>

                <div style="
                    margin-top: 1.5rem;
                    text-align: center;
                    color: #9ca3af;
                    font-size: 0.875rem;
                ">
                    <p style="margin: 0;">支付完成后将自动显示下载页面</p>
                </div>

                <div style="margin-top: 1rem; text-align: center;">
                    <button id="modalCancel" style="
                        color: #9ca3af;
                        background: none;
                        border: none;
                        cursor: pointer;
                        font-size: 0.875rem;
                        transition: color 0.2s ease;
                    ">取消</button>
                </div>
            </div>
        `;
    }

    /**
     * 生成支付二维码内容
     */
    generatePaymentQRContent(data) {
        const { orderData } = data;

        return `
            <span class="modal-close" id="modalClose" style="position: absolute; top: 10px; right: 15px; color: #aaccff; cursor: pointer; font-size: 1.5rem; font-weight: bold; line-height: 1;">&times;</span>

            <h3 style="color: #00ffff; font-size: 1.3rem; margin-bottom: 1rem; text-shadow: 0 0 10px #00aaff; margin-top: 0;">请扫码支付</h3>

            <div style="color: #aaccff; font-size: 0.9rem; margin: 1rem 0; padding: 0.8rem; background: rgba(0, 50, 100, 0.3); border-radius: 6px; border-left: 3px solid #00aaff; text-align: left;">
                <p style="margin: 0 0 0.5rem 0;"><strong>订单号:</strong> ${orderData.out_trade_no}</p>
                <p style="margin: 0 0 0.5rem 0;"><strong>支付方式:</strong> ${orderData.payment_type === 'wechat_pay' ? '微信支付' : '支付宝'}</p>
                <p style="margin: 0;"><strong>说明:</strong> 请使用${orderData.payment_type === 'wechat_pay' ? '微信' : '支付宝'}扫描二维码完成支付</p>
            </div>

            <div style="display: flex; justify-content: center; margin: 1.5rem 0;">
                <div id="qrcode-container" style="background: white; padding: 10px; border-radius: 8px; border: 2px solid #00aaff; box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);"></div>
            </div>

            <div id="payment-status" style="margin: 1rem 0; text-align: center; display: none;">
                <div style="display: inline-block; padding: 0.5rem 1rem; border-radius: 20px; background: rgba(0, 100, 255, 0.2); color: #00ccff; border: 1px solid #334477;">
                    <i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i> 检查支付状态...
                </div>
            </div>

            <div style="display: flex; gap: 1rem; justify-content: center;">
                <button id="payment-back" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">返回</button>
                <button id="modalCancel" style="background: rgba(255, 100, 100, 0.3); color: #ffaaaa; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">取消</button>
            </div>
        `;
    }

    /**
     * 生成公告内容
     */
    generateAnnouncementContent(data) {
        const { announcement } = data;

        return `
            <span class="modal-close" id="modalClose" style="position: absolute; top: 10px; right: 15px; color: #aaccff; cursor: pointer; font-size: 1.5rem; font-weight: bold; line-height: 1;">&times;</span>

            <h3 style="color: #00ffff; font-size: 1.3rem; margin-bottom: 1rem; text-shadow: 0 0 10px #00aaff; margin-top: 0;">${announcement.title}</h3>

            <div style="color: #aaccff; font-size: 0.9rem; line-height: 1.4; margin-bottom: 1.2rem; text-align: left;">
                ${announcement.content}
            </div>

            <button id="modalConfirm" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">确定</button>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents(type, data) {
        // 通用关闭按钮
        const closeBtn = document.getElementById('modalClose');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hide());
            this.addHoverEffect(closeBtn, '#ffffff', '#aaccff');
        }

        // 通用取消按钮
        const cancelBtn = document.getElementById('modalCancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hide());
            this.addHoverEffect(cancelBtn, 'rgba(0, 170, 255, 0.5)', 'rgba(0, 170, 255, 0.3)');
        }

        // 根据类型绑定特定事件
        switch (type) {
            case 'download':
                this.bindDownloadEvents(data);
                break;
            case 'payment-options':
                this.bindPaymentOptionsEvents(data);
                break;
            case 'payment-qr':
                this.bindPaymentQREvents(data);
                break;
            case 'announcement':
                this.bindAnnouncementEvents(data);
                break;
        }
    }

    /**
     * 添加hover效果
     */
    addHoverEffect(element, hoverValue, normalValue, property = 'color') {
        element.addEventListener('mouseenter', () => {
            element.style[property] = hoverValue;
        });
        element.addEventListener('mouseleave', () => {
            element.style[property] = normalValue;
        });
    }

    /**
     * 绑定下载事件
     */
    bindDownloadEvents(data) {
        const { software } = data;

        // 生成下载选项
        this.generateDownloadOptions(software);
    }

    /**
     * 绑定支付选择事件
     */
    bindPaymentOptionsEvents(data) {
        const { software } = data;

        // 关闭按钮hover效果
        const closeBtn = document.getElementById('modalClose');
        if (closeBtn) {
            closeBtn.addEventListener('mouseenter', () => {
                closeBtn.style.color = '#ffffff';
            });
            closeBtn.addEventListener('mouseleave', () => {
                closeBtn.style.color = '#9ca3af';
            });
        }

        // 取消按钮hover效果
        const cancelBtn = document.getElementById('modalCancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('mouseenter', () => {
                cancelBtn.style.color = '#ffffff';
            });
            cancelBtn.addEventListener('mouseleave', () => {
                cancelBtn.style.color = '#9ca3af';
            });
        }

        // 微信支付按钮
        const wechatPayBtn = document.getElementById('wechat-pay-btn');
        if (wechatPayBtn) {
            // 微信支付按钮的hover效果
            wechatPayBtn.addEventListener('mouseenter', () => {
                wechatPayBtn.style.backgroundColor = '#047857';
            });
            wechatPayBtn.addEventListener('mouseleave', () => {
                wechatPayBtn.style.backgroundColor = '#059669';
            });
            wechatPayBtn.addEventListener('click', async () => {
                if (typeof paymentHandler !== 'undefined') {
                    await paymentHandler.createOrder(software.id, 'wechat_pay');
                }
            });
        }

        // 支付宝按钮
        const alipayBtn = document.getElementById('alipay-btn');
        if (alipayBtn) {
            // 支付宝按钮的hover效果
            alipayBtn.addEventListener('mouseenter', () => {
                alipayBtn.style.backgroundColor = '#1d4ed8';
            });
            alipayBtn.addEventListener('mouseleave', () => {
                alipayBtn.style.backgroundColor = '#2563eb';
            });
            alipayBtn.addEventListener('click', async () => {
                if (typeof paymentHandler !== 'undefined') {
                    // 显示加载动画
                    this.showAlipayLoadingAnimation(alipayBtn);

                    // 延迟一下再跳转，让用户看到动画
                    setTimeout(async () => {
                        await paymentHandler.createOrder(software.id, 'alipay');
                    }, 800);
                }
            });
        }
    }

    /**
     * 绑定支付二维码事件
     */
    bindPaymentQREvents(data) {
        const { orderData } = data;

        // 返回按钮
        const backBtn = document.getElementById('payment-back');
        if (backBtn) {
            this.addHoverEffect(backBtn, 'rgba(0, 170, 255, 0.5)', 'rgba(0, 170, 255, 0.3)', 'background');
            backBtn.addEventListener('click', () => {
                // 停止支付状态检查
                if (paymentHandler) {
                    paymentHandler.stopCheckStatus();
                }

                // 返回支付选择界面
                if (this.currentData && this.currentData.software) {
                    const availablePayments = paymentHandler.getAvailablePayments();
                    this.show('payment-options', {
                        software: this.currentData.software,
                        availablePayments: availablePayments
                    });
                }
            });
        }

        // 生成二维码
        this.generateQRCode(orderData);

        // 显示支付状态
        setTimeout(() => {
            const statusEl = document.getElementById('payment-status');
            if (statusEl) {
                statusEl.style.display = 'block';
            }
        }, 3000);
    }

    /**
     * 绑定公告事件
     */
    bindAnnouncementEvents(data) {
        const { timestamp } = data;

        // 确定按钮
        const confirmBtn = document.getElementById('modalConfirm');
        if (confirmBtn) {
            this.addHoverEffect(confirmBtn, 'rgba(0, 170, 255, 0.5)', 'rgba(0, 170, 255, 0.3)', 'background');
            confirmBtn.addEventListener('click', () => {
                this.hide();
                // 记录显示时间
                if (timestamp) {
                    localStorage.setItem('announcement_last_shown', timestamp);
                }
            });
        }
    }

    /**
     * 下载选项配置
     */
    getDownloadConfig() {
        // 默认配置
        const defaultConfig = {
            1: {
                name: '本地下载',
                icon: 'fas fa-download',
                description: '从本地服务器下载最新版本',
                useDownloadUrl: true // 使用getDownloadUrl处理
            },
            2: {
                name: '备用下载',
                icon: 'fas fa-cloud-download-alt',
                description: '备用下载服务器，网络不佳时推荐',
                useDownloadUrl: false
            },
            3: {
                name: '百度网盘',
                icon: 'fas fa-cloud-download-alt',
                description: '适合大文件下载，需登录百度账号',
                useDownloadUrl: false
            },
            4: {
                name: '下载方式4',
                icon: 'fas fa-link',
                description: '扩展下载方式',
                useDownloadUrl: false
            },
            5: {
                name: '下载方式5',
                icon: 'fas fa-external-link-alt',
                description: '扩展下载方式',
                useDownloadUrl: false
            }
        };

        // 检查是否有外部配置覆盖
        if (typeof window.downloadConfig !== 'undefined' && window.downloadConfig) {
            // 合并外部配置，外部配置优先
            for (let i = 1; i <= 5; i++) {
                if (window.downloadConfig[i]) {
                    // 合并配置，保留默认值但允许覆盖
                    defaultConfig[i] = {
                        ...defaultConfig[i],
                        ...window.downloadConfig[i]
                    };
                }
            }
        }

        return defaultConfig;
    }

    /**
     * 生成下载选项
     */
    generateDownloadOptions(software) {
        const container = document.getElementById('downloadOptions');
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        const downloadConfig = this.getDownloadConfig();

        // 按顺序检查每个下载URL
        for (let i = 1; i <= 5; i++) {
            const urlField = `download_url_${i}`;
            const url = software[urlField];

            if (url && url.trim()) {
                const config = downloadConfig[i];
                const finalUrl = config.useDownloadUrl ? this.getDownloadUrl(software.id, i) : url;

                this.addDownloadOption(
                    container,
                    config.name,
                    finalUrl,
                    config.icon,
                    config.description
                );
            }
        }

        // 保持向后兼容性，检查旧字段名
        if (!container.children.length) {
            // 如果没有找到新字段，尝试旧字段
            if (software.download_url) {
                const downloadUrl = this.getDownloadUrl(software.id);
                this.addDownloadOption(
                    container,
                    '本地下载',
                    downloadUrl,
                    'fas fa-download',
                    '从本地服务器下载最新版本'
                );
            }

            if (software.backup_download_url) {
                this.addDownloadOption(
                    container,
                    '备用下载',
                    software.backup_download_url,
                    'fas fa-cloud-download-alt',
                    '备用下载服务器，网络不佳时推荐'
                );
            }

            if (software.baidu_url) {
                this.addDownloadOption(
                    container,
                    '百度网盘',
                    software.baidu_url,
                    'fab fa-baidu',
                    '适合大文件下载，需登录百度账号'
                );
            }
        }
    }

    /**
     * 获取下载URL
     */
    getDownloadUrl(softwareId, urlIndex = 1) {
        // 使用当前时间戳（秒级）
        const timestamp = Math.floor(Date.now() / 1000);

        // 构建令牌字符串：软件ID_时间戳
        const tokenStr = `${softwareId}_${timestamp}`;

        // 使用MD5生成令牌
        let token = '';
        try {
            // 使用JavaScript的MD5库，如果可用的话
            if (typeof md5 === 'function') {
                token = md5(tokenStr);
            } else if (typeof window.md5 === 'function') {
                token = window.md5(tokenStr);
            } else {
                // 简单替代方案，非生产环境使用
                console.error('MD5函数不可用，使用替代方案');
                token = btoa(tokenStr);
            }
        } catch (e) {
            console.error('生成令牌出错:', e);
            // 出错情况下的简单替代方案
            token = btoa(tokenStr);
        }

        return `/api/public/download.php?id=${softwareId}&token=${token}&ts=${timestamp}&url_index=${urlIndex}`;
    }

    /**
     * 获取完整的URL（包含frontend_url）
     */
    getFullUrl(relativeUrl) {
        // 如果已经是完整URL，直接返回
        if (relativeUrl.startsWith('http://') || relativeUrl.startsWith('https://') || relativeUrl.startsWith('//')) {
            return relativeUrl;
        }

        // 获取frontend_url配置
        let frontendUrl = '';
        if (window.siteConfig && window.siteConfig.frontendUrl) {
            frontendUrl = window.siteConfig.frontendUrl;
        } else {
            // 如果没有配置，使用当前域名
            frontendUrl = window.location.origin;
        }

        // 确保frontendUrl不以/结尾，relativeUrl以/开头
        frontendUrl = frontendUrl.replace(/\/$/, '');
        if (!relativeUrl.startsWith('/')) {
            relativeUrl = '/' + relativeUrl;
        }

        return frontendUrl + relativeUrl;
    }

    /**
     * 添加下载选项
     */
    addDownloadOption(container, name, url, iconClass, description) {
        const option = document.createElement('div');
        option.style.cssText = `
            display: flex;
            align-items: center;
            padding: 0.8rem;
            background: rgba(0, 100, 255, 0.2);
            border-radius: 6px;
            border: 1px solid #334477;
            transition: all 0.3s ease;
            cursor: pointer;
        `;

        const iconHtml = `
            <div style="width: 30px; height: 30px; margin-right: 1rem; display: flex; align-items: center; justify-content: center; background: rgba(0, 170, 255, 0.2); border-radius: 50%; color: #00aaff;">
                <i class="${iconClass}"></i>
            </div>
        `;

        const textHtml = `
            <div style="flex: 1; text-align: left;">
                <div style="color: #00ccff; font-weight: bold; margin-bottom: 0.2rem;">${name}</div>
                <div style="color: #aaccff; font-size: 0.8rem;">${description}</div>
            </div>
        `;

        const buttonsHtml = `
            <div style="display: flex; gap: 0.5rem;">
                <button class="download-copy-btn" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.4rem 0.8rem; cursor: pointer; font-size: 0.8rem;">复制链接</button>
                <a href="${url}" class="download-now-btn" target="_blank" style="background: rgba(0, 170, 255, 0.5); color: #ffffff; border: none; border-radius: 4px; padding: 0.4rem 0.8rem; text-decoration: none; font-size: 0.8rem;">立即下载</a>
            </div>
        `;

        option.innerHTML = iconHtml + textHtml + buttonsHtml;

        // 添加hover效果
        option.addEventListener('mouseenter', () => {
            option.style.background = 'rgba(0, 170, 255, 0.3)';
            option.style.borderColor = '#00aaff';
        });
        option.addEventListener('mouseleave', () => {
            option.style.background = 'rgba(0, 100, 255, 0.2)';
            option.style.borderColor = '#334477';
        });

        // 添加复制按钮事件
        const copyBtn = option.querySelector('.download-copy-btn');
        copyBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            // 复制完整的URL
            const fullUrl = this.getFullUrl(url);
            this.copyToClipboard(fullUrl);
            // 临时改变按钮文字
            const originalText = copyBtn.textContent;
            copyBtn.textContent = '已复制';
            setTimeout(() => {
                copyBtn.textContent = originalText;
            }, 2000);
        });

        // 添加复制按钮hover效果
        this.addHoverEffect(copyBtn, 'rgba(0, 170, 255, 0.5)', 'rgba(0, 170, 255, 0.3)', 'background');

        container.appendChild(option);
    }

    /**
     * 显示支付宝加载动画
     */
    showAlipayLoadingAnimation(button) {
        // 禁用按钮
        button.disabled = true;
        button.style.cursor = 'not-allowed';
        button.style.opacity = '0.8';

        // 显示加载动画
        button.innerHTML = `
            <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; margin-right: 0.5rem;"></i>
            <span>正在跳转...</span>
        `;

        // 添加脉冲效果
        button.style.animation = 'alipayPulse 1s ease-in-out infinite';
    }

    /**
     * 复制文本到剪贴板
     */
    copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代API
            navigator.clipboard.writeText(text);
        } else {
            // 使用传统方法
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }
    }

    /**
     * 生成二维码
     */
    generateQRCode(orderData) {
        // 加载QRCode.js
        const script = document.createElement('script');
        script.src = '/assets/libs/qrcode/qrcode.min.js';
        script.onload = () => {
            const container = document.getElementById('qrcode-container');
            if (container) {
                // 清空容器
                container.innerHTML = '';

                // 获取二维码内容
                const qrContent = orderData.payment_type === 'wechat_pay' ?
                    orderData.code_url : orderData.pay_url;

                // 创建Canvas元素
                const canvas = document.createElement('canvas');
                container.appendChild(canvas);

                // 生成二维码
                QRCode.toCanvas(canvas, qrContent, {
                    width: 200,
                    margin: 1,
                    color: {
                        dark: '#000000',
                        light: '#ffffff'
                    }
                }, (error) => {
                    if (error) {
                        console.error('生成二维码出错:', error);
                        container.innerHTML = '<div style="color: #ff6666;">生成二维码失败</div>';
                    }
                });
            }
        };
        document.head.appendChild(script);
    }
}

// 等待DOM加载完成后创建全局实例
let universalModal;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        universalModal = new UniversalModal();
        console.log('UniversalModal 已初始化 (DOMContentLoaded):', universalModal);
    });
} else {
    // DOM已经加载完成
    universalModal = new UniversalModal();
    console.log('UniversalModal 已初始化 (DOM已就绪):', universalModal);
}
