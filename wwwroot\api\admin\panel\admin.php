<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

try {
    // 根据请求方法处理不同操作
    switch ($method) {
        case 'GET':
            // 检查查看管理员的权限
            require_permission(['admin.view', 'admin.edit', 'admin.delete'], true);

            // 列出所有管理员或获取单个管理员详情
            if (isset($_GET['id'])) {
                // 获取单个管理员详情
                $id = intval($_GET['id']);

                // 只能查看自己或其他管理员的基本信息（不包含密码和盐值）
                if ($id === $adminId) {
                    $stmt = $db->prepare('SELECT a.id, a.username, a.last_login, a.created_at, a.role_id, r.name as role_name FROM admins a LEFT JOIN admin_roles r ON a.role_id = r.id WHERE a.id = :id');
                } else {
                    $stmt = $db->prepare('SELECT a.id, a.username, a.last_login, a.created_at, a.role_id, r.name as role_name FROM admins a LEFT JOIN admin_roles r ON a.role_id = r.id WHERE a.id = :id');
                }

                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $result = $stmt->execute();
                $admin = $result->fetchArray(SQLITE3_ASSOC);

                if (!$admin) {
                    json_response(['success' => false, 'message' => '管理员不存在'], 404);
                }

                json_response(['success' => true, 'data' => $admin]);
            } else {
                // 列出所有管理员（不包含密码和盐值）
                $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
                $pageSize = isset($_GET['pageSize']) ? min(50, max(1, intval($_GET['pageSize']))) : 10;
                $offset = ($page - 1) * $pageSize;
                $search = isset($_GET['search']) ? trim($_GET['search']) : '';

                // 构建查询条件
                $whereClause = '';
                $params = [];

                if (!empty($search)) {
                    $whereClause = ' WHERE a.username LIKE :search';
                    $params[':search'] = '%' . $search . '%';
                }

                // 获取总记录数
                $countQuery = "SELECT COUNT(*) FROM admins a $whereClause";
                $stmt = $db->prepare($countQuery);

                foreach ($params as $param => $value) {
                    $stmt->bindValue($param, $value, SQLITE3_TEXT);
                }

                $result = $stmt->execute();
                $totalItems = $result->fetchArray()[0];
                $totalPages = ceil($totalItems / $pageSize);

                // 获取分页数据
                $query = "SELECT a.id, a.username, a.last_login, a.created_at, a.role_id, r.name as role_name
                          FROM admins a
                          LEFT JOIN admin_roles r ON a.role_id = r.id
                          $whereClause
                          ORDER BY a.id ASC
                          LIMIT :limit OFFSET :offset";

                $stmt = $db->prepare($query);
                $stmt->bindValue(':limit', $pageSize, SQLITE3_INTEGER);
                $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

                foreach ($params as $param => $value) {
                    $stmt->bindValue($param, $value, SQLITE3_TEXT);
                }

                $result = $stmt->execute();

                $admins = [];
                while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                    $admins[] = $row;
                }

                // 构建分页信息
                $pagination = [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalItems' => $totalItems,
                    'totalPages' => $totalPages
                ];

                json_response([
                    'success' => true,
                    'data' => [
                        'items' => $admins,
                        'pagination' => $pagination
                    ]
                ]);
            }
            break;

        case 'POST':
            // Action can be 'add', 'update', or 'delete'
            $action = isset($_POST['action']) ? $_POST['action'] : '';

            if ($action === 'update') {
                // 检查编辑管理员的权限
                require_permission('admin.edit');

                // 更新管理员
                if (empty($_POST['id'])) {
                    json_response(['success' => false, 'message' => '缺少管理员ID'], 400);
                }

                $id = intval($_POST['id']);

                // 检查管理员是否存在
                $stmt = $db->prepare('SELECT id, role_id FROM admins WHERE id = :id');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $result = $stmt->execute();
                $admin = $result->fetchArray(SQLITE3_ASSOC);

                if (!$admin) {
                    json_response(['success' => false, 'message' => '管理员不存在'], 404);
                }

                // 更新字段
                $updateFields = [];
                $bindValues = [
                    ':id' => [$id, SQLITE3_INTEGER]
                ];

                // 更新角色
                if (isset($_POST['role_id']) && !empty($_POST['role_id'])) {
                    $roleId = intval($_POST['role_id']);

                    // 不能更改自己的角色为非超级管理员（防止自己失去权限）
                    if ($id === $adminId && $roleId !== 1) {
                        json_response(['success' => false, 'message' => '不能降低自己的权限'], 400);
                    }

                    // 验证角色是否存在
                    $stmt = $db->prepare('SELECT id FROM admin_roles WHERE id = :id');
                    $stmt->bindValue(':id', $roleId, SQLITE3_INTEGER);
                    $result = $stmt->execute();
                    if (!$result->fetchArray(SQLITE3_ASSOC)) {
                        json_response(['success' => false, 'message' => '指定的角色不存在'], 400);
                    }

                    $updateFields[] = 'role_id = :role_id';
                    $bindValues[':role_id'] = [$roleId, SQLITE3_INTEGER];
                }

                // 重置密码
                if (!empty($_POST['reset_password'])) {
                    // 生成新的随机密码，长度为8字符
                    $newPassword = bin2hex(random_bytes(4));

                    // 生成新的盐值
                    $salt = bin2hex(random_bytes(16));

                    // 哈希新密码
                    $passwordHash = password_hash($newPassword . $salt, PASSWORD_DEFAULT);

                    $updateFields[] = 'password = :password';
                    $updateFields[] = 'salt = :salt';
                    $bindValues[':password'] = [$passwordHash, SQLITE3_TEXT];
                    $bindValues[':salt'] = [$salt, SQLITE3_TEXT];
                }

                if (empty($updateFields)) {
                    json_response(['success' => false, 'message' => '没有要更新的内容'], 400);
                }

                // 执行更新
                $sql = 'UPDATE admins SET ' . implode(', ', $updateFields) . ' WHERE id = :id';
                $stmt = $db->prepare($sql);

                foreach ($bindValues as $key => $value) {
                    $stmt->bindValue($key, $value[0], $value[1]);
                }

                $stmt->execute();

                $response = ['success' => true, 'message' => '管理员更新成功'];

                // 如果重置了密码，返回新密码
                if (!empty($_POST['reset_password'])) {
                    $response['new_password'] = $newPassword;
                }

                json_response($response);
            } else if ($action === 'delete') {
                // 检查删除管理员的权限
                require_permission('admin.delete');

                // 删除管理员
                if (empty($_POST['id'])) {
                    json_response(['success' => false, 'message' => '缺少管理员ID'], 400);
                }

                $id = intval($_POST['id']);

                // 不能删除自己
                if ($id === $adminId) {
                    json_response(['success' => false, 'message' => '不能删除自己的账号'], 400);
                }

                // 检查管理员是否存在
                $stmt = $db->prepare('SELECT id, username FROM admins WHERE id = :id');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $result = $stmt->execute();
                $admin = $result->fetchArray(SQLITE3_ASSOC);

                if (!$admin) {
                    json_response(['success' => false, 'message' => '管理员不存在'], 404);
                }

                // 检查是否是最后一个管理员
                $totalAdmins = $db->querySingle('SELECT COUNT(*) FROM admins');
                if ($totalAdmins <= 1) {
                    json_response(['success' => false, 'message' => '不能删除唯一的管理员账号'], 400);
                }

                // 删除管理员
                $stmt = $db->prepare('DELETE FROM admins WHERE id = :id');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $stmt->execute();

                json_response(['success' => true, 'message' => '管理员删除成功']);
            } else {
                // 检查创建管理员的权限
                require_permission('admin.add');

                // 创建新管理员
                $json = file_get_contents('php://input');
                $data = json_decode($json, true);

                // 验证必填字段
                if (empty($data['username']) || empty($data['password'])) {
                    json_response(['success' => false, 'message' => '用户名和密码不能为空'], 400);
                }

                // 检查用户名是否已存在
                $stmt = $db->prepare('SELECT id FROM admins WHERE username = :username');
                $stmt->bindValue(':username', $data['username'], SQLITE3_TEXT);
                $result = $stmt->execute();
                if ($result->fetchArray(SQLITE3_ASSOC)) {
                    json_response(['success' => false, 'message' => '用户名已存在'], 400);
                }

                // 生成随机盐值
                $salt = bin2hex(random_bytes(16));

                // 哈希密码
                $passwordHash = password_hash($data['password'] . $salt, PASSWORD_DEFAULT);

                // 验证角色ID是否存在（如果提供了角色ID）
                $roleId = isset($data['role_id']) ? intval($data['role_id']) : 1; // 默认为角色1（超级管理员）

                if ($roleId > 1) {
                    $stmt = $db->prepare('SELECT id FROM admin_roles WHERE id = :id');
                    $stmt->bindValue(':id', $roleId, SQLITE3_INTEGER);
                    $result = $stmt->execute();
                    if (!$result->fetchArray(SQLITE3_ASSOC)) {
                        json_response(['success' => false, 'message' => '指定的角色不存在'], 400);
                    }
                }

                // 插入数据
                $stmt = $db->prepare('
                    INSERT INTO admins (username, password, salt, role_id, created_at)
                    VALUES (:username, :password, :salt, :role_id, :created_at)
                ');

                $stmt->bindValue(':username', $data['username'], SQLITE3_TEXT);
                $stmt->bindValue(':password', $passwordHash, SQLITE3_TEXT);
                $stmt->bindValue(':salt', $salt, SQLITE3_TEXT);
                $stmt->bindValue(':role_id', $roleId, SQLITE3_INTEGER);
                $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
                $stmt->execute();

                $newId = $db->lastInsertRowID();

                json_response([
                    'success' => true,
                    'message' => '管理员添加成功',
                    'data' => ['id' => $newId]
                ], 201);
            }
            break;

        case 'PUT':
            // 更新管理员
            if (!isset($_GET['id'])) {
                json_response(['success' => false, 'message' => '缺少管理员ID'], 400);
            }

            $id = intval($_GET['id']);

            // 检查编辑管理员的权限，但对修改自己的密码放宽限制
            $isPasswordChange = ($id === $adminId && !empty(json_decode(file_get_contents('php://input'), true)['password']));
            if (!$isPasswordChange) {
                require_permission('admin.edit');
            }

            // 检查管理员是否存在
            $stmt = $db->prepare('SELECT id, role_id FROM admins WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $admin = $result->fetchArray(SQLITE3_ASSOC);

            if (!$admin) {
                json_response(['success' => false, 'message' => '管理员不存在'], 404);
            }

            // 获取请求数据
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);

            // 更新密码或角色
            $updateFields = [];
            $bindValues = [
                ':id' => [$id, SQLITE3_INTEGER]
            ];

            // 更新角色（只有超级管理员可以更改角色）
            if (isset($data['role_id']) && !empty($data['role_id'])) {
                // 检查编辑管理员的权限
                require_permission('admin.edit');

                $roleId = intval($data['role_id']);

                // 不能更改自己的角色为非超级管理员（防止自己失去权限）
                if ($id === $adminId && $roleId !== 1) {
                    json_response(['success' => false, 'message' => '不能降低自己的权限'], 400);
                }

                // 验证角色是否存在
                $stmt = $db->prepare('SELECT id FROM admin_roles WHERE id = :id');
                $stmt->bindValue(':id', $roleId, SQLITE3_INTEGER);
                $result = $stmt->execute();
                if (!$result->fetchArray(SQLITE3_ASSOC)) {
                    json_response(['success' => false, 'message' => '指定的角色不存在'], 400);
                }

                $updateFields[] = 'role_id = :role_id';
                $bindValues[':role_id'] = [$roleId, SQLITE3_INTEGER];
            }

            // 如果是修改自己的密码
            if ($id === $adminId && !empty($data['password'])) {
                // 验证旧密码
                if (empty($data['oldPassword'])) {
                    json_response(['success' => false, 'message' => '请提供旧密码'], 400);
                }

                // 检查旧密码是否正确
                $stmt = $db->prepare('SELECT password, salt FROM admins WHERE id = :id');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $result = $stmt->execute();
                $userData = $result->fetchArray(SQLITE3_ASSOC);

                if (!password_verify($data['oldPassword'] . $userData['salt'], $userData['password'])) {
                    json_response(['success' => false, 'message' => '旧密码不正确'], 400);
                }

                // 生成新的盐值
                $salt = bin2hex(random_bytes(16));

                // 哈希新密码
                $passwordHash = password_hash($data['password'] . $salt, PASSWORD_DEFAULT);

                $updateFields[] = 'password = :password';
                $updateFields[] = 'salt = :salt';
                $bindValues[':password'] = [$passwordHash, SQLITE3_TEXT];
                $bindValues[':salt'] = [$salt, SQLITE3_TEXT];
            }

            if (empty($updateFields)) {
                json_response(['success' => false, 'message' => '没有要更新的内容'], 400);
            }

            // 执行更新
            $sql = 'UPDATE admins SET ' . implode(', ', $updateFields) . ' WHERE id = :id';
            $stmt = $db->prepare($sql);

            foreach ($bindValues as $key => $value) {
                $stmt->bindValue($key, $value[0], $value[1]);
            }

            $stmt->execute();

            json_response(['success' => true, 'message' => '管理员更新成功']);
            break;

        case 'DELETE':
            // 检查删除管理员的权限
            require_permission('admin.delete');

            // 删除管理员
            if (!isset($_GET['id'])) {
                json_response(['success' => false, 'message' => '缺少管理员ID'], 400);
            }

            $id = intval($_GET['id']);

            // 不能删除自己
            if ($id === $adminId) {
                json_response(['success' => false, 'message' => '不能删除自己的账号'], 400);
            }

            // 检查管理员是否存在
            $stmt = $db->prepare('SELECT id FROM admins WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $admin = $result->fetchArray(SQLITE3_ASSOC);

            if (!$admin) {
                json_response(['success' => false, 'message' => '管理员不存在'], 404);
            }

            // 检查是否是最后一个管理员
            $totalAdmins = $db->querySingle('SELECT COUNT(*) FROM admins');
            if ($totalAdmins <= 1) {
                json_response(['success' => false, 'message' => '不能删除唯一的管理员账号'], 400);
            }

            // 删除管理员
            $stmt = $db->prepare('DELETE FROM admins WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $stmt->execute();

            json_response(['success' => true, 'message' => '管理员删除成功']);
            break;

        case 'PATCH':
            // 检查重置密码的权限
            require_permission('admin.edit');

            // 重设管理员密码（不需要验证旧密码）
            if (!isset($_GET['id'])) {
                json_response(['success' => false, 'message' => '缺少管理员ID'], 400);
            }

            $id = intval($_GET['id']);

            // 检查管理员是否存在
            $stmt = $db->prepare('SELECT id, username FROM admins WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $admin = $result->fetchArray(SQLITE3_ASSOC);

            if (!$admin) {
                json_response(['success' => false, 'message' => '管理员不存在'], 404);
            }

            // 获取请求数据
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);

            // 验证新密码
            if (empty($data['password'])) {
                json_response(['success' => false, 'message' => '密码不能为空'], 400);
            }

            // 密码长度检查
            if (strlen($data['password']) < 3) {
                json_response(['success' => false, 'message' => '密码长度太短，不安全'], 400);
            }

            // 生成新的盐值
            $salt = bin2hex(random_bytes(16));

            // 哈希新密码
            $passwordHash = password_hash($data['password'] . $salt, PASSWORD_DEFAULT);

            // 更新密码
            $stmt = $db->prepare('UPDATE admins SET password = :password, salt = :salt WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $stmt->bindValue(':password', $passwordHash, SQLITE3_TEXT);
            $stmt->bindValue(':salt', $salt, SQLITE3_TEXT);
            $stmt->execute();

            json_response(['success' => true, 'message' => '密码重置成功']);
            break;

        default:
            json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}