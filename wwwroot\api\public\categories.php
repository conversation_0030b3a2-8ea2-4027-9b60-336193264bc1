<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
/**
 * 公共分类API
 */

// 包含公共API验证文件
require_once dirname(__DIR__) . DS . 'public.php';

// 获取数据库连接
$db = get_db_connection();

try {
    // 获取分类列表（树状结构）
    $query = "SELECT c.*, COUNT(s.id) as software_count,
              (SELECT COUNT(*) FROM categories WHERE parent_id = c.id) as child_count
              FROM categories c
              LEFT JOIN softwares s ON c.id = s.category
              GROUP BY c.id
              ORDER BY c.level ASC, c.sort ASC, c.id ASC";

    $result = $db->query($query);

    $categories = [];
    $categoryMap = [];

    // 先获取所有分类
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $row['children'] = []; // 添加children数组用于存放子分类
        $categories[] = $row;
        $categoryMap[$row['id']] = &$categories[count($categories) - 1]; // 使用引用，方便后续添加子分类
    }

    // 构建树状结构
    $tree = [];
    foreach ($categories as $category) {
        if ($category['parent_id'] == 0) {
            // 顶级分类直接添加到树中
            $tree[] = &$categoryMap[$category['id']];
        } else {
            // 子分类添加到父分类的children数组中
            if (isset($categoryMap[$category['parent_id']])) {
                $categoryMap[$category['parent_id']]['children'][] = &$categoryMap[$category['id']];
            }
        }
    }

    // 计算每个分类及其子分类的软件总数
    foreach ($categoryMap as &$category) {
        $category['total_software_count'] = calculateTotalSoftwareCount($category);
    }

    json_response(['success' => true, 'data' => $tree]);
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}

/**
 * 递归计算分类及其子分类的软件总数
 *
 * @param array $category 分类数据
 * @return int 软件总数
 */
function calculateTotalSoftwareCount($category) {
    $count = $category['software_count'] ?? 0;

    if (!empty($category['children'])) {
        foreach ($category['children'] as $child) {
            $count += calculateTotalSoftwareCount($child);
        }
    }

    return $count;
}

