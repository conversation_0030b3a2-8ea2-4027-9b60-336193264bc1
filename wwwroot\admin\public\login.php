<?php
/**
 * 管理员登录页面
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 定义根目录常量（如果尚未定义）
if (!defined('__ADMIN_ROOT__')) {
    define('__ADMIN_ROOT__', dirname(__DIR__));
}
if (!defined('__ROOT__')) {
    define('__ROOT__', dirname(dirname(__DIR__)));
}

// 包含公共函数库
require_once __ADMIN_ROOT__ . '/common.php';

// 加载管理后台配置
$adminConfig = include __ADMIN_ROOT__ . '/config.php';

// 获取基础路径配置
$basePath = $adminConfig['base_path'] ?? '/admin/index.php/';
$apiPath = $adminConfig['api_path'] ?? '/api/admin/';

// 加载站点设置
$settings = get_settings();
$siteTitle = $settings['basic']['site_title'] ?? '娜宝贝软件';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>管理员登录 - <?php echo htmlspecialchars($siteTitle); ?>管理系统</title>
    <script src="<?php echo $adminConfig['assets_path']; ?>libs/tailwindcss/tailwind.js"></script>
    <link href="<?php echo $adminConfig['assets_path']; ?>libs/font-awesome/css/all.min.css" rel="stylesheet">
    <script src="<?php echo $adminConfig['assets_path']; ?>libs/vue/vue.min.js"></script>
    <script src="<?php echo $adminConfig['assets_path']; ?>libs/axios/axios.min.js"></script>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #0a0a1a;
            color: #e0e0ff;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 100, 255, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(150, 0, 255, 0.1) 0%, transparent 20%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            background: rgba(20, 30, 60, 0.6);
            border-radius: 8px;
            border: 1px solid #334466;
            padding: 2rem;
            box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);
            backdrop-filter: blur(5px);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            font-size: 1.8rem;
            color: #00ffff;
            text-shadow: 0 0 10px #00aaff, 0 0 20px #0066ff;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #aaccff;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #aaccff;
        }

        .form-input {
            width: 100%;
            padding: 0.8rem;
            background: rgba(10, 20, 50, 0.5);
            border: 1px solid #334466;
            border-radius: 4px;
            color: #e0e0ff;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            border-color: #00aaff;
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.3);
            outline: none;
        }

        .login-btn {
            width: 100%;
            padding: 0.8rem;
            background: linear-gradient(135deg, #0066cc, #0044aa);
            color: #ffffff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #0077dd, #0055bb);
            box-shadow: 0 0 15px rgba(0, 100, 255, 0.5);
        }

        .login-btn:disabled {
            background: #334466;
            cursor: not-allowed;
        }

        .error-message {
            color: #ff5555;
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .back-link {
            text-align: center;
            margin-top: 1.5rem;
        }

        .back-link a {
            color: #00aaff;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-link a:hover {
            color: #00ffff;
            text-shadow: 0 0 5px #00aaff;
        }
    </style>
</head>
<body>
    <div id="app" class="login-container">
        <div class="login-header">
            <h1><?php echo htmlspecialchars($siteTitle); ?></h1>
            <p>管理系统登录</p>
        </div>

        <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
        </div>

        <form @submit.prevent="login">
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <input type="text" id="username" v-model="username" class="form-input" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input type="password" id="password" v-model="password" class="form-input" required>
            </div>

            <button type="submit" class="login-btn" :disabled="loading">
                <span v-if="loading">登录中...</span>
                <span v-else>登录</span>
            </button>
        </form>

        <div class="back-link">
            <a href="/"><i class="fas fa-arrow-left"></i> 返回网站首页</a>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                username: '',
                password: '',
                loading: false,
                errorMessage: ''
            },
            mounted() {
                // 检查是否已登录
                const adminId = localStorage.getItem('admin_id');
                const adminUsername = localStorage.getItem('admin_username');
                const loginTime = localStorage.getItem('admin_login_time');

                if (adminId && adminUsername && loginTime) {
                    // 检查会话是否过期
                    const sessionTimeout = <?php echo $adminConfig['session_timeout'] ?? 86400; ?>;
                    const currentTime = Math.floor(Date.now() / 1000);

                    if (currentTime - loginTime <= sessionTimeout) {
                        // 会话未过期，重定向到管理面板
                        //window.location.href = '<?php echo $basePath; ?>panel/dashboard.html';
                        return;
                    } else {
                        // 会话已过期，清除本地存储
                        localStorage.removeItem('admin_id');
                        localStorage.removeItem('admin_username');
                        localStorage.removeItem('admin_login_time');
                        localStorage.removeItem('admin_permissions');

                        this.errorMessage = '会话已过期，请重新登录';
                    }
                }

                // 检查URL参数
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('expired') === '1') {
                    this.errorMessage = '会话已过期，请重新登录';
                }
            },
            methods: {
                login() {
                    this.loading = true;
                    this.errorMessage = '';

                    // 发送登录请求
                    fetch('<?php echo $apiPath; ?>public/login.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: this.username,
                            password: this.password
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.loading = false;

                        if (data.success) {
                            // 登录成功，将用户信息存储在localStorage中
                            localStorage.setItem('admin_id', data.admin_id || '1');
                            localStorage.setItem('admin_username', data.admin_username || this.username);
                            localStorage.setItem('admin_login_time', Math.floor(Date.now() / 1000).toString());

                            // 存储权限信息（如果有）
                            if (data.permissions) {
                                localStorage.setItem('admin_permissions', JSON.stringify(data.permissions));
                            } else {
                                // 默认权限（仅用于演示）
                                localStorage.setItem('admin_permissions', JSON.stringify(['all']));
                            }

                            // 重定向到管理面板
                            window.location.href = '<?php echo $basePath; ?>panel/dashboard.html';
                        } else {
                            // 登录失败，显示错误信息
                            this.errorMessage = data.message || '登录失败，请检查用户名和密码';
                        }
                    })
                    .catch(error => {
                        this.loading = false;
                        this.errorMessage = '登录请求出错，请重试';
                        console.error('登录请求出错：', error);
                    });
                }
            }
        });
    </script>
</body>
</html>
