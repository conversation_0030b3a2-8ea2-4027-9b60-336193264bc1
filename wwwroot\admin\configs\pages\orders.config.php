<?php
/**
 * 订单管理页面配置
 */

return [
    'title' => '订单管理',
    'apiEndpoint' => '/api/admin/panel/orders.php',
    'activeMenu' => 'orders',
    'permissions' => ['orders.view'],
    'appId' => 'ordersApp',
    
    // 功能开关
    'enableSearch' => true,
    'enableAdd' => false,  // 订单不允许手动添加
    'enableEdit' => true,
    'enableDelete' => true,
    'enableBatchDelete' => false,
    'enableSelection' => false,
    
    // 搜索配置
    'searchPlaceholder' => '搜索订单...',
    'searchFields' => ['order_no', 'software_name'],
    
    // 空数据配置
    'emptyText' => '暂无订单数据',
    'emptyIcon' => 'fas fa-inbox',
    
    // 分页配置
    'defaultPageSize' => 10,
    
    // 表格列配置
    'columns' => [
        [
            'key' => 'id',
            'label' => 'ID',
            'type' => 'number',
            'class' => 'w-16'
        ],
        [
            'key' => 'order_no',
            'label' => '订单号',
            'type' => 'text'
        ],
        [
            'key' => 'software_name',
            'label' => '软件名称',
            'type' => 'custom'
        ],
        [
            'key' => 'amount',
            'label' => '金额',
            'type' => 'custom'
        ],
        [
            'key' => 'payment_type',
            'label' => '支付方式',
            'type' => 'custom'
        ],
        [
            'key' => 'status',
            'label' => '状态',
            'type' => 'custom'
        ],
        [
            'key' => 'user_ip',
            'label' => '用户IP',
            'type' => 'text'
        ],
        [
            'key' => 'created_at',
            'label' => '创建时间',
            'type' => 'datetime'
        ],
        [
            'key' => 'paid_at',
            'label' => '支付时间',
            'type' => 'custom'
        ]
    ],
    
    // 筛选器配置
    'filters' => [
        [
            'key' => 'status',
            'placeholder' => '所有状态',
            'width' => '120px',
            'options' => [
                ['value' => 'pending', 'label' => '待支付'],
                ['value' => 'paid', 'label' => '已支付'],
                ['value' => 'failed', 'label' => '支付失败']
            ]
        ]
    ],
    
    // 操作按钮配置
    'actions' => [
        [
            'key' => 'edit',
            'icon' => 'fas fa-edit',
            'title' => '编辑订单状态',
            'class' => 'text-blue-400 hover:text-blue-300',
            'permission' => 'orders.edit',
            'handler' => 'editOrder'
        ],
        [
            'key' => 'delete',
            'icon' => 'fas fa-trash-alt',
            'title' => '删除订单',
            'class' => 'text-red-400 hover:text-red-300',
            'permission' => 'orders.delete',
            'handler' => 'confirmDelete'
        ]
    ],
    
    // 表单字段配置（用于编辑模态框）
    'fields' => [
        [
            'key' => 'order_no',
            'label' => '订单号',
            'type' => 'text',
            'disabled' => true
        ],
        [
            'key' => 'software_name',
            'label' => '软件名称',
            'type' => 'text',
            'disabled' => true
        ],
        [
            'key' => 'amount',
            'label' => '金额',
            'type' => 'text',
            'disabled' => true,
            'formatter' => 'formatPrice'
        ],
        [
            'key' => 'status',
            'label' => '状态',
            'type' => 'select',
            'required' => true,
            'options' => [
                ['value' => 'pending', 'label' => '待支付'],
                ['value' => 'paid', 'label' => '已支付'],
                ['value' => 'failed', 'label' => '支付失败']
            ]
        ]
    ],
    
    // 删除确认配置
    'deleteConfirmation' => [
        'title' => '确认删除',
        'message' => '您确定要删除订单 "{order_no}" 吗？此操作不可撤销。'
    ],
    
    // 编辑模态框配置
    'editModal' => [
        'title' => '编辑订单状态',
        'submitText' => '保存修改'
    ]
];
