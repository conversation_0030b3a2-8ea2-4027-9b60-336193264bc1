<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

try {
    // 获取软件总数
    $softwareCount = $db->querySingle('SELECT COUNT(*) FROM softwares');

    // 获取分类总数
    $categoryCount = $db->querySingle('SELECT COUNT(*) FROM categories');

    // 获取管理员总数
    $adminCount = $db->querySingle('SELECT COUNT(*) FROM admins');

    // 获取总下载量
    $totalDownloads = $db->querySingle('SELECT COALESCE(SUM(downloads), 0) FROM softwares');

    // 返回统计数据
    $response = [
        'success' => true,
        'stats' => [
            'softwareCount' => $softwareCount,
            'categoryCount' => $categoryCount,
            'adminCount' => $adminCount,
            'totalDownloads' => $totalDownloads
        ]
    ];

    json_response($response);

} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '获取统计数据失败: ' . $e->getMessage()
    ], 500);
}
