<?php
/**
 * 管理后台列表设置选项卡配置
 *
 * 该文件定义了管理后台列表设置页面的选项卡、分组和字段结构
 * 仅负责UI展示和交互，不包含具体参数的默认值
 */

return [
    // 列表设置选项卡
    [
        'name' => 'list',
        'title' => '列表设置',
        'icon' => 'fas fa-list',
        'groups' => [
            // 搜索设置组
            [
                'name' => 'search',
                'title' => '搜索设置',
                'settings_path' => 'search', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'search.mode',
                        'label' => '搜索模式',
                        'type' => 'radio',
                        'options' => [
                            ['value' => 'click', 'label' => '点击搜索 - 用户需要点击搜索按钮或按回车键才会执行搜索'],
                            ['value' => 'instant', 'label' => '即时搜索 - 用户输入时自动执行搜索']
                        ]
                    ],
                    [
                        'name' => 'search.delay',
                        'label' => '搜索延迟（毫秒）',
                        'type' => 'number',
                        'condition' => 'search.mode === "instant"',
                        'min' => 0,
                        'max' => 2000,
                        'step' => 1,
                        'description' => '用户停止输入后等待多长时间执行搜索，0表示立即搜索，建议设置为300-500毫秒'
                    ],
                    [
                        'name' => 'search.min_length',
                        'label' => '最小搜索长度',
                        'type' => 'number',
                        'min' => 1,
                        'max' => 10,
                        'step' => 1,
                        'description' => '触发搜索的最小字符数'
                    ]
                ]
            ],
            // 分页设置组
            [
                'name' => 'pagination',
                'title' => '分页设置',
                'settings_path' => 'pagination', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'pagination.default_page_size',
                        'label' => '默认每页显示数量',
                        'type' => 'number',
                        'min' => 1,
                        'max' => 100,
                        'step' => 1,
                        'default' => 20,
                        'description' => '软件列表默认每页显示的软件数量（最小值为1，方便测试）'
                    ],
                    [
                        'name' => 'pagination.max_page_size',
                        'label' => '最大每页显示数量',
                        'type' => 'number',
                        'min' => 1,
                        'max' => 500,
                        'step' => 1,
                        'default' => 100,
                        'description' => '软件列表允许的最大每页显示数量'
                    ],
                    [
                        'name' => 'pagination.show_total',
                        'label' => '显示总数',
                        'type' => 'checkbox',
                        'default' => true,
                        'description' => '是否在分页中显示总数量'
                    ]
                ]
            ]
        ]
    ]
];
