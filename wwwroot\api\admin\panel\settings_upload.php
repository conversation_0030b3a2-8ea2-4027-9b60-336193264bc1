<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
/**
 * 管理面板 - 文件上传处理API
 */

// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 检查权限
require_permission('admin.settings');

// 处理文件上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    $file = $_FILES['file'];
    $fieldName = $_POST['field_name'] ?? '';
    $type = $_POST['type'] ?? 'site_image';
    $subPath = $_POST['sub_path'] ?? '';
    $fileName = $_POST['file_name'] ?? '';

    // 验证文件上传是否成功
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => '文件大小超过了php.ini中upload_max_filesize指令限制',
            UPLOAD_ERR_FORM_SIZE => '文件大小超过了HTML表单中MAX_FILE_SIZE指令限制',
            UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
            UPLOAD_ERR_NO_FILE => '没有文件被上传',
            UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
            UPLOAD_ERR_CANT_WRITE => '文件写入失败',
            UPLOAD_ERR_EXTENSION => '文件上传被PHP扩展停止'
        ];

        $errorMessage = $errorMessages[$file['error']] ?? '未知上传错误';
        api_error($errorMessage);
    }

    // 获取上传设置
    $uploadSettings = Settings::get('upload', []);

    // 根据上传类型确定目标路径和限制
    $uploadPath = '';
    $maxSize = 0;
    $allowedExtensions = [];

    switch ($type) {
        case 'software':
            $uploadPath = $uploadSettings['software_package_path'] ?? 'uploads/software/package/';
            $maxSize = $uploadSettings['max_size_software'] ?? 524288000; // 默认500MB
            $allowedExtensions = explode(',', $uploadSettings['allowed_extensions_software'] ?? 'zip,rar,7z,exe');
            break;

        case 'icon':
            $uploadPath = $uploadSettings['software_icon_path'] ?? 'uploads/software/icon/';
            $maxSize = $uploadSettings['max_size_icon'] ?? 2097152; // 默认2MB
            $allowedExtensions = explode(',', $uploadSettings['allowed_extensions_icon'] ?? 'jpg,jpeg,png,gif,webp');
            break;

        case 'site_image':
        default:
            $uploadPath = $uploadSettings['site_images_path'] ?? 'uploads/site/';
            $maxSize = $uploadSettings['max_size_site'] ?? 5242880; // 默认5MB
            $allowedExtensions = explode(',', $uploadSettings['allowed_extensions_site'] ?? 'jpg,jpeg,png,gif,webp');
            break;
    }

    // 添加子路径（如果有）
    if (!empty($subPath)) {
        $uploadPath .= rtrim($subPath, '/') . '/';
    }

    // 确保上传目录存在
    $fullUploadPath = __WWWROOT__ . DS . str_replace('/', DS, $uploadPath);
    if (!is_dir($fullUploadPath)) {
        if (!mkdir($fullUploadPath, 0755, true)) {
            api_error('无法创建上传目录: ' . $uploadPath);
        }
    }

    // 验证文件大小
    if ($file['size'] > $maxSize) {
        api_error('文件大小超过限制: ' . formatFileSize($maxSize));
    }

    // 获取文件扩展名
    $fileInfo = pathinfo($file['name']);
    $extension = strtolower($fileInfo['extension']);

    // 验证文件类型
    if (!in_array($extension, $allowedExtensions)) {
        api_error('不允许的文件类型: ' . $extension . '。允许的类型: ' . implode(', ', $allowedExtensions));
    }

    // 生成文件名
    if (empty($fileName)) {
        // 使用随机文件名
        $fileName = md5(uniqid(mt_rand(), true)) . '.' . $extension;
    } else {
        // 使用指定的文件名，确保有正确的扩展名
        $fileName = $fileName . '.' . $extension;
    }

    // 完整的文件路径
    $filePath = $fullUploadPath . $fileName;

    // 上传文件
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        api_error('文件上传失败');
    }

    // 返回成功响应
    api_success([
        'file_url' => $uploadPath . $fileName,
        'file_name' => $fileName,
        'file_size' => $file['size'],
        'field_name' => $fieldName
    ]);
} else {
    api_error('无效的请求');
}

/**
 * 格式化文件大小
 * @param int $bytes 字节数
 * @return string 格式化后的大小
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}
