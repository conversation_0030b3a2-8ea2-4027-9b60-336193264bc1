<?php
/**
 * 支付处理函数库
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含公共函数库
require_once __DIR__ . DS . 'common.php';

// 包含支付签名验证类
require_once __DIR__ . DS . 'includes' . DS . 'PaymentSignature.php';

/**
 * 检查支付配置是否有效
 *
 * @return bool 支付配置是否有效
 */
function check_payment_config() {
    $config = get_settings();

    // 检查支付功能是否启用
    if (!isset($config['payment']) || !isset($config['payment']['enabled']) || !$config['payment']['enabled']) {
        return false;
    }

    // 检查是否至少有一种支付方式启用
    $wechatEnabled = isset($config['payment']['wechat_pay']['enabled']) && $config['payment']['wechat_pay']['enabled'];
    $alipayEnabled = isset($config['payment']['alipay']['enabled']) && $config['payment']['alipay']['enabled'];

    return $wechatEnabled || $alipayEnabled;
}

/**
 * 获取支付配置
 *
 * @param string $type 支付类型（wechat_pay 或 alipay）
 * @return array|null 支付配置
 */
function get_payment_config($type = null) {
    // 调用 Settings 类的静态方法
    return Settings::getPaymentConfig($type);
}

/**
 * 创建微信支付订单
 *
 * @param array $orderData 订单数据
 * @return array 支付信息
 */
function create_wechat_pay_order($orderData) {
    // 获取微信支付配置
    $config = get_payment_config('wechat_pay');
    if (!$config) {
        return ['success' => false, 'message' => '微信支付未启用'];
    }

    // 检查必要的配置项
    if (empty($config['app_id']) || empty($config['mch_id']) || empty($config['key'])) {
        return ['success' => false, 'message' => '微信支付配置不完整'];
    }

    // 检查订单数据
    if (empty($orderData['out_trade_no']) || empty($orderData['total_fee']) || empty($orderData['body'])) {
        return ['success' => false, 'message' => '订单数据不完整'];
    }

    // 判断是否为移动设备
    $isMobile = is_mobile_device();

    // 根据设备类型确定交易类型
    $tradeType = $isMobile ? 'MWEB' : 'NATIVE';

    // 构建统一下单参数
    $params = [
        'appid' => $config['app_id'],
        'mch_id' => $config['mch_id'],
        'nonce_str' => generate_random_string(32),
        'body' => $orderData['body'],
        'out_trade_no' => $orderData['out_trade_no'],
        'total_fee' => intval($orderData['total_fee'] * 100), // 转换为分
        'spbill_create_ip' => get_client_ip(),
        'notify_url' => $config['notify_url'],
        'trade_type' => $tradeType
    ];

    // 生成签名
    $params['sign'] = PaymentSignature::generateWechatSign($params, $config['key']);

    // 将参数转换为XML
    $xml = array_to_xml($params);

    // 发送请求到微信支付API
    $url = 'https://api.mch.weixin.qq.com/pay/unifiedorder';
    $response = http_post($url, $xml);

    // 解析响应
    $result = xml_to_array($response);

    // 检查响应是否成功
    if ($result['return_code'] !== 'SUCCESS' || $result['result_code'] !== 'SUCCESS') {
        $errorMsg = isset($result['err_code_des']) ? $result['err_code_des'] : (isset($result['return_msg']) ? $result['return_msg'] : '未知错误');
        return ['success' => false, 'message' => '微信支付下单失败：' . $errorMsg];
    }

    // 根据设备类型返回不同的支付信息
    if ($isMobile) {
        // 移动设备返回支付链接，用于跳转到微信支付页面
        return [
            'success' => true,
            'payment_type' => 'wechat_pay',
            'device_type' => 'mobile',
            'mweb_url' => $result['mweb_url'],
            'out_trade_no' => $orderData['out_trade_no']
        ];
    } else {
        // 桌面设备返回二维码链接，用于生成支付二维码
        return [
            'success' => true,
            'payment_type' => 'wechat_pay',
            'device_type' => 'desktop',
            'code_url' => $result['code_url'],
            'out_trade_no' => $orderData['out_trade_no']
        ];
    }
}

/**
 * 创建支付宝订单
 *
 * @param array $orderData 订单数据
 * @return array 支付信息
 */
function create_alipay_order($orderData) {
    // 获取支付宝配置
    $config = get_payment_config('alipay');
    if (!$config) {
        return ['success' => false, 'message' => '支付宝支付未启用'];
    }

    // 检查必要的配置项
    if (empty($config['app_id']) || empty($config['private_key']) || empty($config['public_key'])) {
        return ['success' => false, 'message' => '支付宝支付配置不完整'];
    }

    // 检查订单数据
    if (empty($orderData['out_trade_no']) || empty($orderData['total_amount']) || empty($orderData['subject'])) {
        return ['success' => false, 'message' => '订单数据不完整'];
    }

    // 判断是否为移动设备
    $isMobile = is_mobile_device();

    // 根据设备类型确定接口类型
    $method = $isMobile ? 'alipay.trade.wap.pay' : 'alipay.trade.page.pay';

    // 构建请求参数
    $bizContent = [
        'out_trade_no' => $orderData['out_trade_no'],
        'total_amount' => number_format($orderData['total_amount'], 2, '.', ''),
        'subject' => $orderData['subject'],
        'product_code' => $isMobile ? 'QUICK_WAP_WAY' : 'FAST_INSTANT_TRADE_PAY'
    ];

    // 获取前台URL用于构建返回地址
    $frontendUrl = '';
    $siteConfig = get_settings();
    if (isset($siteConfig['basic']['frontend_url'])) {
        $frontendUrl = rtrim($siteConfig['basic']['frontend_url'], '/');
    }

    // 构建返回URL
    $returnUrl = $frontendUrl . '/payment_return.php?order_no=' . $orderData['out_trade_no'];

    $params = [
        'app_id' => $config['app_id'],
        'method' => $method,
        'charset' => 'utf-8',
        'sign_type' => 'RSA2',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0',
        'notify_url' => $config['notify_url'],
        'return_url' => $returnUrl,
        'biz_content' => json_encode($bizContent)
    ];

    // 生成签名
    $params['sign'] = PaymentSignature::generateAlipaySign($params, $config['private_key']);

    // 构建支付URL
    $url = $config['gateway'] . '?' . http_build_query($params);

    // 根据设备类型返回不同的支付信息
    if ($isMobile) {
        // 移动设备返回支付链接，用于跳转到支付宝支付页面
        return [
            'success' => true,
            'payment_type' => 'alipay',
            'device_type' => 'mobile',
            'pay_url' => $url,
            'out_trade_no' => $orderData['out_trade_no']
        ];
    } else {
        // 桌面设备返回支付表单，用于提交到支付宝
        return [
            'success' => true,
            'payment_type' => 'alipay',
            'device_type' => 'desktop',
            'pay_url' => $url,
            'out_trade_no' => $orderData['out_trade_no']
        ];
    }
}

// 注意：签名生成和验证函数已迁移到 PaymentSignature 类中
// 请使用 PaymentSignature::generateWechatSign() 和 PaymentSignature::generateAlipaySign()

/**
 * 数组转XML
 *
 * @param array $arr 数组
 * @return string XML
 */
function array_to_xml($arr) {
    $xml = "<xml>";
    foreach ($arr as $key => $val) {
        if (is_numeric($val)) {
            $xml .= "<" . $key . ">" . $val . "</" . $key . ">";
        } else {
            $xml .= "<" . $key . "><![CDATA[" . $val . "]]></" . $key . ">";
        }
    }
    $xml .= "</xml>";
    return $xml;
}

/**
 * XML转数组
 *
 * @param string $xml XML
 * @return array 数组
 */
function xml_to_array($xml) {
    if (PHP_VERSION_ID < 80000) {
        libxml_disable_entity_loader(true);
    }
    $obj = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
    return json_decode(json_encode($obj), true);
}

/**
 * 判断是否为移动设备
 *
 * @return bool 是否为移动设备
 */
function is_mobile_device() {
    $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    return preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $userAgent) || preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i', substr($userAgent, 0, 4));
}

/**
 * 生成随机字符串
 *
 * @param int $length 长度
 * @return string 随机字符串
 */
function generate_random_string($length = 16) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= $chars[mt_rand(0, strlen($chars) - 1)];
    }
    return $str;
}

/**
 * 发送HTTP POST请求
 *
 * @param string $url URL
 * @param string $data 数据
 * @return string 响应
 */
function http_post($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $response = curl_exec($ch);
    curl_close($ch);
    return $response;
}
