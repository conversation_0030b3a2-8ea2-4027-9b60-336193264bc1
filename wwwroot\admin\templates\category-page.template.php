<?php
/**
 * 分类管理页面模板
 *
 * 基于list-page模板，增加分类管理的特殊功能
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 验证配置
if (!isset($pageConfig) || !is_array($pageConfig)) {
    throw new Exception('页面配置未定义或格式错误');
}

// 设置默认值
$config = array_merge([
    'title' => '分类管理',
    'apiEndpoint' => '',
    'permissions' => [],
    'activeMenu' => '',
    'enableSearch' => true,
    'enableAdd' => true,
    'enableEdit' => true,
    'enableDelete' => true,
    'enablePagination' => false,
    'searchPlaceholder' => '搜索...',
    'addButtonText' => '添加',
    'emptyText' => '暂无数据',
    'emptyIcon' => 'fas fa-inbox',
    'columns' => [],
    'actions' => [],
    'fields' => []
], $pageConfig);

// 渲染头部
$requiredPermissions = !empty($config['permissions']) ? $config['permissions'] : null;
render_header($config['title'], $config['activeMenu'], $requiredPermissions);
?>

<!-- 页面内容开始 -->
<div id="<?php echo $config['appId'] ?? 'categoryApp'; ?>">
    <!-- 顶部操作栏 -->
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center">
            <?php if ($config['enableSearch']): ?>
            <div class="relative">
                <input type="text" v-model="searchQuery" @input="debounceSearch" placeholder="<?php echo $config['searchPlaceholder']; ?>" class="form-input pr-10">
                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            </div>
            <?php endif; ?>
        </div>

        <div>
            <?php if ($config['enableAdd']): ?>
            <button v-if="checkPermission('<?php echo $config['addPermission'] ?? 'add'; ?>')" @click="showAddForm" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i> <?php echo $config['addButtonText']; ?>
            </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- 分类列表 -->
    <div class="content-card">
        <h2><?php echo $config['title']; ?></h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else-if="items.length === 0" class="text-center py-8 text-gray-400">
            <i class="<?php echo $config['emptyIcon']; ?> text-4xl mb-3"></i>
            <p><?php echo $config['emptyText']; ?></p>
        </div>

        <div v-else>
            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <?php foreach ($config['columns'] as $column): ?>
                            <th <?php echo !empty($column['class']) ? 'class="' . $column['class'] . '"' : ''; ?>>
                                <?php echo $column['label']; ?>
                            </th>
                            <?php endforeach; ?>
                            <?php if (!empty($config['actions'])): ?>
                            <th>操作</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in items" :key="item.id">
                            <?php foreach ($config['columns'] as $column): ?>
                            <td>
                                <?php if (!empty($config['customColumns'][$column['key']])): ?>
                                    <?php echo $config['customColumns'][$column['key']]; ?>
                                <?php elseif (isset($column['type']) && $column['type'] === 'datetime'): ?>
                                    {{ formatDateTime(item.<?php echo $column['key']; ?>) }}
                                <?php elseif (isset($column['type']) && $column['type'] === 'number'): ?>
                                    {{ item.<?php echo $column['key']; ?> || 0 }}
                                <?php elseif ($column['key'] === 'parent_name'): ?>
                                    {{ item.<?php echo $column['key']; ?> || '无' }}
                                <?php else: ?>
                                    {{ item.<?php echo $column['key']; ?> || '-' }}
                                <?php endif; ?>
                            </td>
                            <?php endforeach; ?>
                            <?php if (!empty($config['actions'])): ?>
                            <td>
                                <div class="flex space-x-2">
                                    <?php foreach ($config['actions'] as $action): ?>
                                    <button
                                        v-if="<?php echo !empty($action['permission']) ? 'checkPermission(\'' . $action['permission'] . '\')' : 'true'; ?><?php echo !empty($action['condition']) ? ' && (' . $action['condition'] . ')' : ''; ?>"
                                        @click="<?php echo $action['handler']; ?>(item)"
                                        class="<?php echo $action['class']; ?><?php echo !empty($action['condition']) ? '' : (isset($action['disabledClass']) ? ' ' . $action['disabledClass'] : ''); ?>"
                                        title="<?php echo $action['title']; ?>"
                                        <?php if (!empty($action['condition'])): ?>
                                        :disabled="!(<?php echo $action['condition']; ?>)"
                                        :class="{ '<?php echo $action['disabledClass'] ?? 'opacity-50'; ?>': !(<?php echo $action['condition']; ?>) }"
                                        <?php endif; ?>
                                    >
                                        <i class="<?php echo $action['icon']; ?>"></i>
                                    </button>
                                    <?php endforeach; ?>
                                </div>
                            </td>
                            <?php endif; ?>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 显示总记录数 -->
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-400">
                    共 {{ items.length }} 条记录
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑分类模态框 -->
    <div v-if="showAddModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl text-blue-300 font-semibold">{{ showEditModal ? '编辑分类' : '添加分类' }}</h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form @submit.prevent="submitForm">
                    <?php foreach ($config['fields'] as $field): ?>
                    <div class="form-group">
                        <label class="form-label">
                            <?php echo $field['label']; ?>
                            <?php if (!empty($field['required'])): ?>
                            <span class="text-red-500">*</span>
                            <?php endif; ?>
                        </label>

                        <?php if ($field['type'] === 'text'): ?>
                        <input
                            type="text"
                            v-model="formData.<?php echo $field['key']; ?>"
                            class="form-input"
                            <?php echo !empty($field['required']) ? 'required' : ''; ?>
                            <?php echo !empty($field['placeholder']) ? 'placeholder="' . $field['placeholder'] . '"' : ''; ?>
                        >

                        <?php elseif ($field['type'] === 'number'): ?>
                        <input
                            type="number"
                            v-model="formData.<?php echo $field['key']; ?>"
                            class="form-input"
                            <?php echo !empty($field['required']) ? 'required' : ''; ?>
                        >

                        <?php elseif ($field['type'] === 'category_selector'): ?>
                        <category-selector
                            v-model="formData.<?php echo $field['key']; ?>"
                            :categories="availableParentCategories"
                            placeholder="<?php echo $field['placeholder'] ?? '选择分类'; ?>"
                            noneOptionText="<?php echo $field['noneOptionText'] ?? '无'; ?>"
                            width="100%"
                            maxHeight="300px">
                        </category-selector>
                        <?php endif; ?>

                        <?php if (!empty($field['help'])): ?>
                        <div class="text-xs text-gray-400 mt-1"><?php echo $field['help']; ?></div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" @click="closeModal" class="btn bg-gray-700 text-white">取消</button>
                        <button type="submit" class="btn btn-primary" :disabled="submitting">
                            <span v-if="submitting"><i class="fas fa-spinner fa-spin mr-2"></i> 提交中...</span>
                            <span v-else>{{ showEditModal ? '保存修改' : '添加分类' }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
            <div class="p-6">
                <div class="text-center mb-6">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-5xl mb-4"></i>
                    <h3 class="text-xl text-red-300 font-semibold"><?php echo $config['deleteConfirmation']['title'] ?? '确认删除'; ?></h3>
                    <p class="mt-2 text-gray-300">{{ getDeleteMessage() }}</p>

                    <!-- 删除限制提示 -->
                    <?php if (!empty($config['deleteConfirmation']['restrictions'])): ?>
                    <?php foreach ($config['deleteConfirmation']['restrictions'] as $restriction): ?>
                    <p v-if="<?php echo str_replace(['software_count', 'child_count'], ['itemToDelete.software_count', 'itemToDelete.child_count'], $restriction['condition']); ?>" class="mt-2 text-red-400">
                        <i class="fas fa-exclamation-circle mr-1"></i>
                        <?php echo str_replace(['{software_count}', '{child_count}'], ['{{ itemToDelete.software_count }}', '{{ itemToDelete.child_count }}'], $restriction['message']); ?>
                    </p>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="flex justify-center space-x-3">
                    <button @click="showDeleteModal = false" class="btn bg-gray-700 text-white">取消</button>
                    <button @click="deleteItem" class="btn btn-danger" :disabled="deleting || !canDelete()">
                        <span v-if="deleting"><i class="fas fa-spinner fa-spin mr-2"></i> 删除中...</span>
                        <span v-else>确认删除</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#<?php echo $config['appId'] ?? 'categoryApp'; ?>',
        data: {
            loading: true,
            items: [],
            searchQuery: '',
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            formData: {
                id: null,
                <?php foreach ($config['fields'] as $field): ?>
                <?php echo $field['key']; ?>: <?php echo json_encode($field['default'] ?? ($field['type'] === 'number' ? 0 : '')); ?>,
                <?php endforeach; ?>
            },
            availableParentCategories: [],
            itemToDelete: {},
            submitting: false,
            deleting: false,
            searchTimeout: null
        },
        mounted() {
            this.loadItems();
        },
        methods: {
            // 检查权限
            checkPermission(permission) {
                return AdminUtils.checkPermission(permission);
            },

            // 格式化日期时间
            formatDateTime(timestamp) {
                return AdminUtils.formatDateTime(timestamp);
            },

            // 防抖搜索
            debounceSearch() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.loadItems();
                }, 300);
            },

            // 加载数据
            async loadItems() {
                this.loading = true;

                let url = `<?php echo $config['apiEndpoint']; ?>`;
                if (this.searchQuery) {
                    url += `?search=${encodeURIComponent(this.searchQuery)}`;
                }

                try {
                    const data = await apiGet(url, {}, {}, {
                        loadingMessage: '正在加载数据...'
                    });

                    if (data && data.success) {
                        this.items = data.data.items || [];
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }

                    this.loading = false;
                } catch (error) {
                    console.error('加载数据出错：', error);
                    this.loading = false;
                }
            },

            // 加载可用的父分类
            async loadAvailableParentCategories(excludeId = null) {
                try {
                    const data = await apiGet(`<?php echo $config['apiEndpoint']; ?>?pageSize=1000`, {}, {}, {
                        showLoading: false
                    });

                    if (data && data.success) {
                        if (excludeId) {
                            // 排除当前分类及其子分类
                            const childIds = [];
                            const findChildIds = (categories, parentId) => {
                                categories.forEach(cat => {
                                    if (cat.parent_id === parentId) {
                                        childIds.push(cat.id);
                                        findChildIds(categories, cat.id);
                                    }
                                });
                            };

                            findChildIds(data.data.items, excludeId);
                            childIds.push(excludeId);

                            this.availableParentCategories = data.data.items.filter(cat => !childIds.includes(cat.id));
                        } else {
                            this.availableParentCategories = data.data.items;
                        }
                    }
                } catch (error) {
                    console.error('加载父分类数据出错：', error);
                }
            },

            // 显示添加表单
            async showAddForm() {
                await this.loadAvailableParentCategories();
                this.resetFormData();
                this.showAddModal = true;
            },

            // 添加子分类
            async addSubCategory(parentCategory) {
                await this.loadAvailableParentCategories();
                this.resetFormData();
                this.formData.parent_id = parentCategory.id;
                this.showAddModal = true;
            },

            // 编辑分类
            async editCategory(category) {
                await this.loadAvailableParentCategories(category.id);
                this.formData = { ...category };
                this.showEditModal = true;
            },

            // 确认删除
            confirmDelete(category) {
                this.itemToDelete = category;
                this.showDeleteModal = true;
            },

            // 获取删除消息
            getDeleteMessage() {
                const message = `<?php echo $config['deleteConfirmation']['message'] ?? '您确定要删除 "{name}" 吗？此操作不可撤销。'; ?>`;
                return message.replace('{name}', this.itemToDelete.name || '');
            },

            // 检查是否可以删除
            canDelete() {
                <?php if (!empty($config['deleteConfirmation']['restrictions'])): ?>
                const restrictions = <?php echo json_encode($config['deleteConfirmation']['restrictions']); ?>;
                for (const restriction of restrictions) {
                    // 只替换字母开头的标识符，不替换数字
                    const condition = restriction.condition.replace(/\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g, 'this.itemToDelete.$1');
                    if (eval(condition)) {
                        return false;
                    }
                }
                <?php endif; ?>
                return true;
            },

            // 删除项目
            async deleteItem() {
                if (!this.canDelete()) {
                    return;
                }

                this.deleting = true;

                try {
                    const data = await apiDelete(
                        `<?php echo $config['apiEndpoint']; ?>?id=${this.itemToDelete.id}`,
                        {},
                        {},
                        {
                            showSuccess: true,
                            successMessage: '删除成功',
                            operation: '删除',
                            loadingMessage: '正在删除...'
                        }
                    );

                    this.deleting = false;
                    this.showDeleteModal = false;

                    if (data && data.success) {
                        this.loadItems();
                    }
                } catch (error) {
                    this.deleting = false;
                    this.showDeleteModal = false;
                    console.error('删除出错：', error);
                }
            },

            // 提交表单
            async submitForm() {
                // 基本验证
                <?php foreach ($config['fields'] as $field): ?>
                <?php if (!empty($field['required'])): ?>
                if (!this.formData.<?php echo $field['key']; ?>) {
                    alert('<?php echo $field['label']; ?>不能为空');
                    return;
                }
                <?php endif; ?>
                <?php endforeach; ?>

                this.submitting = true;

                try {
                    let data;
                    const operation = this.showEditModal ? '更新' : '添加';
                    const successMessage = this.showEditModal ? '更新成功' : '添加成功';

                    if (this.showEditModal) {
                        data = await apiPut(
                            `<?php echo $config['apiEndpoint']; ?>?id=${this.formData.id}`,
                            this.formData,
                            {},
                            {
                                showSuccess: true,
                                successMessage,
                                operation,
                                loadingMessage: '正在更新...'
                            }
                        );
                    } else {
                        data = await apiPost(
                            '<?php echo $config['apiEndpoint']; ?>',
                            this.formData,
                            {},
                            {
                                showSuccess: true,
                                successMessage,
                                operation,
                                loadingMessage: '正在添加...'
                            }
                        );
                    }

                    this.submitting = false;

                    if (data && data.success) {
                        this.closeModal();
                        this.loadItems();
                    }
                } catch (error) {
                    this.submitting = false;
                    console.error((this.showEditModal ? '更新' : '添加') + '出错：', error);
                }
            },

            // 重置表单数据
            resetFormData() {
                this.formData = {
                    id: null,
                    <?php foreach ($config['fields'] as $field): ?>
                    <?php echo $field['key']; ?>: <?php echo json_encode($field['default'] ?? ($field['type'] === 'number' ? 0 : '')); ?>,
                    <?php endforeach; ?>
                };
            },

            // 关闭模态框
            closeModal() {
                this.showAddModal = false;
                this.showEditModal = false;
                this.resetFormData();
                this.submitting = false;
                this.availableParentCategories = [];
            }
        }
    });
</script>

<?php
// 渲染页面底部
render_footer();
