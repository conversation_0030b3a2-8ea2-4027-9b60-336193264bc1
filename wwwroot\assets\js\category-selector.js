/**
 * 通用分类选择器组件
 * 
 * 使用方法:
 * 1. 在HTML中引入此JS文件
 * 2. 在页面中添加分类选择器模板
 * 3. 在Vue实例中注册组件
 */

// 分类树选择组件
Vue.component('category-tree-select', {
    template: '#category-tree-select-template',
    props: {
        categories: {
            type: Array,
            required: true
        },
        selectedId: {
            type: [Number, String, null],
            default: null
        },
        expandedCategories: {
            type: Array,
            default: () => []
        }
    },
    methods: {
        isExpanded(categoryId) {
            return this.expandedCategories.includes(categoryId);
        },
        toggleExpand(categoryId) {
            this.$emit('toggle-expand', categoryId);
        }
    }
});

// 分类选择器组件
Vue.component('category-selector', {
    template: '#category-selector-template',
    props: {
        // 分类数据
        categories: {
            type: Array,
            required: true
        },
        // 当前选中的分类ID
        value: {
            type: [Number, String, null],
            default: null
        },
        // 占位符文本
        placeholder: {
            type: String,
            default: '选择分类'
        },
        // 是否显示"无分类"选项
        showNoneOption: {
            type: Boolean,
            default: true
        },
        // "无分类"选项的文本
        noneOptionText: {
            type: String,
            default: '无分类'
        },
        // 按钮宽度
        width: {
            type: String,
            default: 'auto'
        },
        // 下拉菜单宽度
        dropdownWidth: {
            type: String,
            default: '100%'
        },
        // 下拉菜单最大高度
        maxHeight: {
            type: String,
            default: '300px'
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            showDropdown: false,
            expandedCategories: [],
            selectedName: ''
        };
    },
    watch: {
        value: {
            immediate: true,
            handler(newVal) {
                this.updateSelectedName();
            }
        },
        categories: {
            handler() {
                this.updateSelectedName();
                // 默认展开所有顶级分类
                this.expandTopLevelCategories();
            },
            deep: true
        }
    },
    mounted() {
        // 默认展开所有顶级分类
        this.expandTopLevelCategories();
        this.updateSelectedName();
        
        // 添加点击事件监听器，点击外部关闭下拉菜单
        document.addEventListener('click', this.handleClickOutside);
    },
    beforeDestroy() {
        // 移除事件监听器
        document.removeEventListener('click', this.handleClickOutside);
    },
    methods: {
        // 切换下拉菜单显示/隐藏
        toggleDropdown() {
            if (!this.disabled) {
                this.showDropdown = !this.showDropdown;
            }
        },
        
        // 选择分类
        selectCategory(categoryId) {
            this.$emit('input', categoryId);
            this.showDropdown = false;
            this.updateSelectedName();
        },
        
        // 切换分类展开/折叠状态
        toggleExpand(categoryId) {
            const index = this.expandedCategories.indexOf(categoryId);
            if (index === -1) {
                this.expandedCategories.push(categoryId);
            } else {
                this.expandedCategories.splice(index, 1);
            }
        },
        
        // 处理点击外部事件
        handleClickOutside(e) {
            if (this.$el && !this.$el.contains(e.target)) {
                this.showDropdown = false;
            }
        },
        
        // 更新选中的分类名称
        updateSelectedName() {
            if (!this.value) {
                this.selectedName = '';
                return;
            }
            
            // 递归查找分类名称
            const findCategoryName = (categories, id) => {
                for (const category of categories) {
                    if (category.id == id) {
                        return category.name;
                    }
                    
                    if (category.children && category.children.length > 0) {
                        const name = findCategoryName(category.children, id);
                        if (name) return name;
                    }
                }
                return null;
            };
            
            this.selectedName = findCategoryName(this.categories, this.value) || '';
        },
        
        // 默认展开所有顶级分类
        expandTopLevelCategories() {
            if (this.categories && this.categories.length > 0) {
                this.categories.forEach(category => {
                    if (category.level === 0 && category.child_count > 0) {
                        if (!this.expandedCategories.includes(category.id)) {
                            this.expandedCategories.push(category.id);
                        }
                    }
                });
            }
        }
    }
});
