<?php
/**
 * 分类管理页面配置
 */

return [
    'title' => '分类管理',
    'apiEndpoint' => '/api/admin/panel/category.php',
    'activeMenu' => 'category',
    'permissions' => ['category.view'],
    'appId' => 'categoryApp',

    // 功能开关
    'enableSearch' => true,
    'enableAdd' => true,
    'enableEdit' => true,
    'enableDelete' => true,
    'enableBatchDelete' => false, // 分类管理不支持批量删除
    'enableSelection' => false,   // 分类管理不需要选择功能
    'enablePagination' => false,  // 分类管理显示所有数据，不分页

    // 搜索配置
    'searchPlaceholder' => '搜索分类...',
    'searchFields' => ['name'],

    // 空数据配置
    'emptyText' => '暂无分类数据',
    'emptyIcon' => 'fas fa-folder-open',

    // 添加按钮配置
    'addButtonText' => '添加分类',
    'addPermission' => 'category.add',

    // 表格列配置
    'columns' => [
        [
            'key' => 'id',
            'label' => 'ID',
            'class' => 'w-16'
        ],
        [
            'key' => 'name',
            'label' => '分类名称',
            'type' => 'custom'
        ],
        [
            'key' => 'parent_name',
            'label' => '父分类',
            'type' => 'text'
        ],
        [
            'key' => 'level',
            'label' => '层级',
            'type' => 'number'
        ],
        [
            'key' => 'child_count',
            'label' => '子分类数',
            'type' => 'number'
        ],
        [
            'key' => 'software_count',
            'label' => '软件数量',
            'type' => 'number'
        ],
        [
            'key' => 'sort',
            'label' => '排序',
            'type' => 'number'
        ],
        [
            'key' => 'created_at',
            'label' => '创建时间',
            'type' => 'datetime'
        ]
    ],

    // 自定义列模板
    'customColumns' => [
        'name' => '
            <div class="flex items-center">
                <!-- 根据层级添加缩进 -->
                <div v-if="item._level > 0" :style="{ width: (item._level * 20) + \'px\' }" class="flex-shrink-0"></div>
                <!-- 显示层级标识 -->
                <i v-if="item.level > 0" class="fas fa-level-down-alt fa-rotate-90 mr-2 text-blue-400"></i>
                <!-- 分类名称 -->
                <span>{{ item.name }}</span>
                <!-- 显示子分类数量 -->
                <span v-if="item.child_count > 0" class="ml-2 text-xs text-blue-400">({{ item.child_count }}个子分类)</span>
            </div>
        '
    ],

    // 操作按钮配置
    'actions' => [
        [
            'key' => 'addSub',
            'icon' => 'fas fa-plus-circle',
            'title' => '添加子分类',
            'class' => 'text-green-400 hover:text-green-300',
            'permission' => 'category.add',
            'handler' => 'addSubCategory'
        ],
        [
            'key' => 'edit',
            'icon' => 'fas fa-edit',
            'title' => '编辑分类',
            'class' => 'text-blue-400 hover:text-blue-300',
            'permission' => 'category.edit',
            'handler' => 'editCategory'
        ],
        [
            'key' => 'delete',
            'icon' => 'fas fa-trash-alt',
            'title' => '删除分类',
            'class' => 'text-red-400 hover:text-red-300',
            'permission' => 'category.delete',
            'condition' => 'item.software_count === 0 && item.child_count === 0',
            'disabledClass' => 'opacity-50',
            'handler' => 'confirmDelete'
        ]
    ],

    // 表单字段配置
    'fields' => [
        [
            'key' => 'name',
            'label' => '分类名称',
            'type' => 'text',
            'required' => true,
            'placeholder' => '请输入分类名称'
        ],
        [
            'key' => 'parent_id',
            'label' => '父分类',
            'type' => 'category_selector',
            'required' => false,
            'placeholder' => '选择父分类',
            'noneOptionText' => '无 (顶级分类)',
            'help' => '选择此分类的父分类，不选则为顶级分类'
        ],
        [
            'key' => 'sort',
            'label' => '排序',
            'type' => 'number',
            'required' => false,
            'default' => 0,
            'help' => '数字越大排序越靠前'
        ]
    ],

    // 删除确认配置
    'deleteConfirmation' => [
        'title' => '确认删除',
        'message' => '您确定要删除分类 "{name}" 吗？此操作不可撤销。',
        'restrictions' => [
            [
                'condition' => 'software_count > 0',
                'message' => '该分类下有 {software_count} 个软件，无法删除。请先移除或重新分类这些软件。'
            ],
            [
                'condition' => 'child_count > 0',
                'message' => '该分类下有 {child_count} 个子分类，无法删除。请先删除所有子分类。'
            ]
        ]
    ],

    // 特殊配置
    'specialFeatures' => [
        'hierarchical' => true,        // 支持层级结构
        'categorySelector' => true,    // 使用分类选择器
        'noParentOption' => true,      // 支持无父分类选项
        'preventCircularReference' => true  // 防止循环引用
    ]
];
