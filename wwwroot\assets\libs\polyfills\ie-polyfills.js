/**
 * IE浏览器兼容性polyfills
 * 为IE浏览器提供现代JavaScript功能的兼容性支持
 */

// Promise polyfill for IE
if (typeof Promise === 'undefined') {
    window.Promise = function(executor) {
        var self = this;
        self.state = 'pending';
        self.value = undefined;
        self.handlers = [];

        function resolve(result) {
            if (self.state === 'pending') {
                self.state = 'fulfilled';
                self.value = result;
                self.handlers.forEach(handle);
                self.handlers = null;
            }
        }

        function reject(error) {
            if (self.state === 'pending') {
                self.state = 'rejected';
                self.value = error;
                self.handlers.forEach(handle);
                self.handlers = null;
            }
        }

        function handle(handler) {
            if (self.state === 'pending') {
                self.handlers.push(handler);
            } else {
                if (self.state === 'fulfilled' && typeof handler.onFulfilled === 'function') {
                    handler.onFulfilled(self.value);
                }
                if (self.state === 'rejected' && typeof handler.onRejected === 'function') {
                    handler.onRejected(self.value);
                }
            }
        }

        this.then = function(onFulfilled, onRejected) {
            return new Promise(function(resolve, reject) {
                handle({
                    onFulfilled: function(result) {
                        try {
                            if (typeof onFulfilled === 'function') {
                                resolve(onFulfilled(result));
                            } else {
                                resolve(result);
                            }
                        } catch (ex) {
                            reject(ex);
                        }
                    },
                    onRejected: function(error) {
                        try {
                            if (typeof onRejected === 'function') {
                                resolve(onRejected(error));
                            } else {
                                reject(error);
                            }
                        } catch (ex) {
                            reject(ex);
                        }
                    }
                });
            });
        };

        this.catch = function(onRejected) {
            return this.then(null, onRejected);
        };

        try {
            executor(resolve, reject);
        } catch (ex) {
            reject(ex);
        }
    };

    Promise.resolve = function(value) {
        return new Promise(function(resolve) {
            resolve(value);
        });
    };

    Promise.reject = function(error) {
        return new Promise(function(resolve, reject) {
            reject(error);
        });
    };
}

// Object.assign polyfill for IE
if (typeof Object.assign !== 'function') {
    Object.assign = function(target) {
        if (target == null) {
            throw new TypeError('Cannot convert undefined or null to object');
        }

        var to = Object(target);

        for (var index = 1; index < arguments.length; index++) {
            var nextSource = arguments[index];

            if (nextSource != null) {
                for (var nextKey in nextSource) {
                    if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                        to[nextKey] = nextSource[nextKey];
                    }
                }
            }
        }
        return to;
    };
}

// Array.includes polyfill for IE
if (!Array.prototype.includes) {
    Array.prototype.includes = function(searchElement, fromIndex) {
        if (this == null) {
            throw new TypeError('"this" is null or not defined');
        }

        var o = Object(this);
        var len = parseInt(o.length) || 0;
        if (len === 0) {
            return false;
        }

        var n = parseInt(fromIndex) || 0;
        var k;
        if (n >= 0) {
            k = n;
        } else {
            k = len + n;
            if (k < 0) {
                k = 0;
            }
        }

        function sameValueZero(x, y) {
            return x === y || (typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y));
        }

        while (k < len) {
            if (sameValueZero(o[k], searchElement)) {
                return true;
            }
            k++;
        }

        return false;
    };
}

// Array.find polyfill for IE
if (!Array.prototype.find) {
    Array.prototype.find = function(predicate) {
        if (this == null) {
            throw new TypeError('"this" is null or not defined');
        }

        var o = Object(this);
        var len = parseInt(o.length) || 0;
        if (typeof predicate !== 'function') {
            throw new TypeError('predicate must be a function');
        }

        var thisArg = arguments[1];
        var k = 0;

        while (k < len) {
            var kValue = o[k];
            if (predicate.call(thisArg, kValue, k, o)) {
                return kValue;
            }
            k++;
        }

        return undefined;
    };
}

// String.includes polyfill for IE
if (!String.prototype.includes) {
    String.prototype.includes = function(search, start) {
        if (typeof start !== 'number') {
            start = 0;
        }

        if (start + search.length > this.length) {
            return false;
        } else {
            return this.indexOf(search, start) !== -1;
        }
    };
}

// URLSearchParams polyfill for IE
if (typeof URLSearchParams === 'undefined') {
    window.URLSearchParams = function(init) {
        var self = this;
        self.params = {};

        if (typeof init === 'string') {
            if (init.charAt(0) === '?') {
                init = init.slice(1);
            }
            var pairs = init.split('&');
            for (var i = 0; i < pairs.length; i++) {
                var pair = pairs[i].split('=');
                if (pair.length === 2) {
                    var key = decodeURIComponent(pair[0]);
                    var value = decodeURIComponent(pair[1]);
                    if (self.params[key]) {
                        if (Array.isArray(self.params[key])) {
                            self.params[key].push(value);
                        } else {
                            self.params[key] = [self.params[key], value];
                        }
                    } else {
                        self.params[key] = value;
                    }
                }
            }
        }

        this.append = function(name, value) {
            if (self.params[name]) {
                if (Array.isArray(self.params[name])) {
                    self.params[name].push(value);
                } else {
                    self.params[name] = [self.params[name], value];
                }
            } else {
                self.params[name] = value;
            }
        };

        this.delete = function(name) {
            delete self.params[name];
        };

        this.get = function(name) {
            var value = self.params[name];
            return Array.isArray(value) ? value[0] : value;
        };

        this.getAll = function(name) {
            var value = self.params[name];
            return Array.isArray(value) ? value : (value ? [value] : []);
        };

        this.has = function(name) {
            return name in self.params;
        };

        this.set = function(name, value) {
            self.params[name] = value;
        };

        this.toString = function() {
            var result = [];
            for (var key in self.params) {
                if (self.params.hasOwnProperty(key)) {
                    var value = self.params[key];
                    if (Array.isArray(value)) {
                        for (var i = 0; i < value.length; i++) {
                            result.push(encodeURIComponent(key) + '=' + encodeURIComponent(value[i]));
                        }
                    } else {
                        result.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));
                    }
                }
            }
            return result.join('&');
        };
    };
}

// 简单的fetch polyfill for IE (基于XMLHttpRequest)
if (typeof fetch === 'undefined') {
    window.fetch = function(url, options) {
        return new Promise(function(resolve, reject) {
            var xhr = new XMLHttpRequest();
            var method = (options && options.method) || 'GET';
            var headers = (options && options.headers) || {};
            var body = options && options.body;

            xhr.open(method, url, true);

            // 设置请求头
            for (var key in headers) {
                if (headers.hasOwnProperty(key)) {
                    xhr.setRequestHeader(key, headers[key]);
                }
            }

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    var response = {
                        ok: xhr.status >= 200 && xhr.status < 300,
                        status: xhr.status,
                        statusText: xhr.statusText,
                        json: function() {
                            return Promise.resolve(JSON.parse(xhr.responseText));
                        },
                        text: function() {
                            return Promise.resolve(xhr.responseText);
                        }
                    };

                    if (response.ok) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                }
            };

            xhr.onerror = function() {
                reject(new Error('Network error'));
            };

            xhr.send(body);
        });
    };
}

console.log('IE兼容性polyfills已加载');
