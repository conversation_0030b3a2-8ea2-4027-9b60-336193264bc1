/**
 * 支付处理模块
 */

// 支付处理类
class PaymentHandler {
    constructor() {
        // 支付配置
        this.config = {
            enabled: false,
            available_payments: []
        };

        // 当前设备类型
        this.isMobile = false;

        // 当前订单信息
        this.currentOrder = null;

        // 支付状态检查定时器
        this.checkStatusTimer = null;

        // 初始化
        this.init();
    }

    /**
     * 初始化支付处理器
     */
    init() {
        // 使用页面中定义的支付配置
        if (typeof paymentConfig !== 'undefined') {
            this.config = paymentConfig;
            this.isMobile = paymentConfig.is_mobile;
            console.log('支付配置加载成功:', this.config);
        } else {
            console.error('支付配置未定义');
            // 使用默认配置
            this.config = {
                enabled: false,
                available_payments: []
            };
            this.isMobile = false;
        }
    }

    /**
     * 检查支付是否可用
     *
     * @returns {boolean} 支付是否可用
     */
    isPaymentEnabled() {
        return this.config.enabled && this.config.available_payments.length > 0;
    }

    /**
     * 获取可用的支付方式
     *
     * @returns {Array} 可用的支付方式
     */
    getAvailablePayments() {
        return this.config.available_payments || [];
    }

    /**
     * 创建支付订单
     *
     * @param {number} softwareId 软件ID
     * @param {string} paymentType 支付类型
     * @returns {Promise} 支付结果
     */
    async createOrder(softwareId, paymentType) {
        try {
            // 检查支付类型是否可用
            if (!this.config.available_payments.includes(paymentType)) {
                return { success: false, message: '不支持的支付方式' };
            }

            // 创建订单
            const data = await apiPost(
                '/api/public/payment.php',
                {
                    software_id: softwareId,
                    payment_type: paymentType
                },
                {},
                { operation: '创建订单' }
            );

            if (data.success) {
                // 保存当前订单信息和软件ID
                this.currentOrder = data;
                this.currentOrder.software_id = softwareId;

                // 根据设备类型和支付方式处理支付
                if (data.device_type === 'mobile') {
                    // 移动设备：跳转到支付页面
                    if (data.payment_type === 'wechat_pay') {
                        window.location.href = data.mweb_url;
                    } else if (data.payment_type === 'alipay') {
                        window.location.href = data.pay_url;
                    }
                } else {
                    // 桌面设备处理
                    if (data.payment_type === 'wechat_pay') {
                        // 微信支付：显示二维码
                        this.showQRCode(data);
                        // 开始检查支付状态
                        this.startCheckStatus(data.out_trade_no);
                    } else if (data.payment_type === 'alipay') {
                        // 支付宝：直接跳转到支付页面
                        window.location.href = data.pay_url;
                    }
                }
            }

            return data;
        } catch (error) {
            console.error('创建订单出错:', error);
            return { success: false, message: '创建订单出错，请重试' };
        }
    }

    /**
     * 显示支付二维码
     *
     * @param {Object} orderData 订单数据
     */
    showQRCode(orderData) {
        // 保存软件信息以便返回时使用
        if (this.currentOrder && this.currentOrder.software_id) {
            // 查找软件信息
            if (window.vueApp && window.vueApp.software) {
                const software = window.vueApp.software.find(s => s.id == this.currentOrder.software_id);
                if (software) {
                    // 使用通用弹窗显示二维码界面
                    universalModal.show('payment-qr', {
                        orderData: orderData,
                        software: software
                    });

                    // 开始检查支付状态
                    this.startCheckStatus(orderData.out_trade_no);
                    return;
                }
            }
        }

        // 如果没有找到软件信息，使用独立弹窗（保持兼容性）
        this.createStandaloneQRModal(orderData);
    }

    /**
     * 添加二维码界面的事件监听器
     *
     * @param {Element} modal 弹窗元素
     */
    addQRCodeEventListeners(modal) {
        // 关闭按钮
        const closeBtn = document.getElementById('close-payment-qr');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.remove();
                this.stopCheckStatus();
            });

            // 添加hover效果
            closeBtn.addEventListener('mouseenter', () => {
                closeBtn.style.color = '#ffffff';
            });
            closeBtn.addEventListener('mouseleave', () => {
                closeBtn.style.color = '#aaccff';
            });
        }

        // 返回按钮
        const backBtn = document.getElementById('payment-back');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                // 停止检查支付状态
                this.stopCheckStatus();

                // 重新显示支付选择界面
                this.showPaymentOptions();
            });

            // 添加hover效果
            backBtn.addEventListener('mouseenter', () => {
                backBtn.style.background = 'rgba(0, 170, 255, 0.5)';
            });
            backBtn.addEventListener('mouseleave', () => {
                backBtn.style.background = 'rgba(0, 170, 255, 0.3)';
            });
        }

        // 取消按钮
        const cancelBtn = document.getElementById('payment-cancel-qr');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                modal.remove();
                this.stopCheckStatus();
            });

            // 添加hover效果
            cancelBtn.addEventListener('mouseenter', () => {
                cancelBtn.style.background = 'rgba(255, 100, 100, 0.5)';
            });
            cancelBtn.addEventListener('mouseleave', () => {
                cancelBtn.style.background = 'rgba(255, 100, 100, 0.3)';
            });
        }
    }

    /**
     * 显示支付选择界面（返回功能）
     */
    showPaymentOptions() {
        // 这个方法需要与 software.js 中的 showPaymentOptions 函数配合
        // 由于我们在同一个弹窗中操作，需要重新生成支付选择内容
        const modal = document.getElementById('payment-options-modal');
        if (modal && this.currentOrder) {
            // 触发重新显示支付选择的事件
            const event = new CustomEvent('payment:showOptions', {
                detail: {
                    softwareId: this.currentOrder.software_id
                }
            });
            document.dispatchEvent(event);
        }
    }

    /**
     * 创建独立的二维码弹窗（兼容性方法）
     *
     * @param {Object} orderData 订单数据
     */
    createStandaloneQRModal(orderData) {
        // 创建支付弹窗（使用统一的科技风格）
        const modalHtml = `
            <div id="payment-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 30, 0.8); backdrop-filter: blur(5px); z-index: 150; display: flex; justify-content: center; align-items: center; opacity: 1;">
                <div style="background: linear-gradient(135deg, #001133, #003366); padding: 1.5rem; border-radius: 10px; border: 1px solid #00aaff; box-shadow: 0 0 30px rgba(0, 170, 255, 0.5); text-align: center; position: relative; max-width: 90%; width: 500px; animation: pulse 2s infinite;">
                    <span id="close-payment-modal" style="position: absolute; top: 10px; right: 15px; color: #aaccff; cursor: pointer; font-size: 1.5rem; font-weight: bold; line-height: 1; background: none; border: none;">&times;</span>

                    <h3 style="color: #00ffff; font-size: 1.3rem; margin-bottom: 1rem; text-shadow: 0 0 10px #00aaff; margin-top: 0;">请扫码支付</h3>

                    <div style="color: #aaccff; font-size: 0.9rem; margin: 1rem 0; padding: 0.8rem; background: rgba(0, 50, 100, 0.3); border-radius: 6px; border-left: 3px solid #00aaff; text-align: left;">
                        <p style="margin: 0 0 0.5rem 0;"><strong>订单号:</strong> ${orderData.out_trade_no}</p>
                        <p style="margin: 0 0 0.5rem 0;"><strong>支付方式:</strong> ${orderData.payment_type === 'wechat_pay' ? '微信支付' : '支付宝'}</p>
                        <p style="margin: 0;"><strong>说明:</strong> 请使用${orderData.payment_type === 'wechat_pay' ? '微信' : '支付宝'}扫描二维码完成支付</p>
                    </div>

                    <div style="display: flex; justify-content: center; margin: 1.5rem 0;">
                        <div id="qrcode-container" style="background: white; padding: 10px; border-radius: 8px; border: 2px solid #00aaff; box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);"></div>
                    </div>

                    <div id="payment-status" style="margin: 1rem 0; text-align: center; display: none;">
                        <div style="display: inline-block; padding: 0.5rem 1rem; border-radius: 20px; background: rgba(0, 100, 255, 0.2); color: #00ccff; border: 1px solid #334477;">
                            <i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i> 检查支付状态...
                        </div>
                    </div>

                    <button id="payment-cancel-standalone" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">取消</button>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 添加关闭事件
        document.getElementById('close-payment-modal').addEventListener('click', () => {
            this.closePaymentModal();
        });

        // 添加取消事件
        document.getElementById('payment-cancel-standalone').addEventListener('click', () => {
            this.closePaymentModal();
        });

        // 生成二维码
        this.generateQRCode(orderData);

        // 显示支付状态
        setTimeout(() => {
            const statusEl = document.getElementById('payment-status');
            if (statusEl) {
                statusEl.style.display = 'block';
            }
        }, 3000);
    }

    /**
     * 生成二维码
     *
     * @param {Object} orderData 订单数据
     */
    generateQRCode(orderData) {
        // 加载QRCode.js
        const script = document.createElement('script');
        script.src = '/assets/libs/qrcode/qrcode.min.js';
        script.onload = () => {
            const container = document.getElementById('qrcode-container');
            if (container) {
                // 清空容器
                container.innerHTML = '';

                // 获取二维码内容
                const qrContent = orderData.payment_type === 'wechat_pay' ?
                    orderData.code_url : orderData.pay_url;

                // 创建Canvas元素
                const canvas = document.createElement('canvas');
                container.appendChild(canvas);

                // 生成二维码
                QRCode.toCanvas(canvas, qrContent, {
                    width: 200,
                    margin: 1,
                    color: {
                        dark: '#000000',
                        light: '#ffffff'
                    }
                }, (error) => {
                    if (error) {
                        console.error('生成二维码出错:', error);
                        container.innerHTML = '<div class="text-red-500">生成二维码失败</div>';
                    }
                });
            }
        };
        document.head.appendChild(script);
    }

    /**
     * 关闭支付弹窗
     */
    closePaymentModal() {
        const modal = document.getElementById('payment-modal');
        if (modal) {
            modal.remove();
        }

        // 停止检查支付状态
        this.stopCheckStatus();
    }

    /**
     * 开始检查支付状态
     *
     * @param {string} orderNo 订单号
     */
    startCheckStatus(orderNo) {
        // 停止之前的检查
        this.stopCheckStatus();

        // 开始新的检查
        this.checkStatusTimer = setInterval(async () => {
            try {
                const data = await apiGet(`/api/public/payment.php?order_no=${orderNo}`);

                if (data && data.success && data.data.status === 'paid') {
                    // 支付成功，停止检查
                    this.stopCheckStatus();

                    // 关闭支付弹窗
                    this.closePaymentModal();

                    // 显示支付成功提示
                    this.showPaymentSuccess();

                    // 触发支付成功事件
                    this.triggerPaymentSuccess(orderNo);
                }
            } catch (error) {
                console.error('检查支付状态出错:', error);
            }
        }, 3000); // 每3秒检查一次
    }

    /**
     * 停止检查支付状态
     */
    stopCheckStatus() {
        if (this.checkStatusTimer) {
            clearInterval(this.checkStatusTimer);
            this.checkStatusTimer = null;
        }
    }

    /**
     * 显示支付成功提示
     */
    showPaymentSuccess() {
        // 创建成功提示
        const successHtml = `
            <div id="payment-success" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
                <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md p-6 text-center">
                    <div class="text-green-400 text-5xl mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="text-xl text-green-300 font-semibold mb-2">支付成功</h3>
                    <p class="text-gray-300 mb-4">您的支付已完成，软件将自动下载</p>
                    <button id="close-success" class="btn bg-green-700 text-white hover:bg-green-600 px-4 py-2 rounded">
                        确定
                    </button>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', successHtml);

        // 添加关闭事件
        document.getElementById('close-success').addEventListener('click', () => {
            const successEl = document.getElementById('payment-success');
            if (successEl) {
                successEl.remove();
            }
        });

        // 自动关闭
        setTimeout(() => {
            const successEl = document.getElementById('payment-success');
            if (successEl) {
                successEl.remove();
            }
        }, 5000);
    }

    /**
     * 触发支付成功事件
     *
     * @param {string} orderNo 订单号
     */
    triggerPaymentSuccess(orderNo) {
        // 获取软件ID
        const softwareId = this.currentOrder ? this.currentOrder.software_id : null;

        // 创建自定义事件
        const event = new CustomEvent('payment:success', {
            detail: {
                orderNo: orderNo,
                softwareId: softwareId
            }
        });

        // 触发事件
        document.dispatchEvent(event);

        // 微信支付和支付宝支付都跳转到软件页面显示下载选项弹窗
        if (softwareId && this.currentOrder) {
            setTimeout(() => {
                // 跳转到软件页面显示下载选项弹窗
                const softwareUrl = `/#/software/${softwareId}`;
                window.location.href = softwareUrl;
            }, 1000); // 延迟1秒后跳转
        }
    }
}

// 创建支付处理器实例
const paymentHandler = new PaymentHandler();