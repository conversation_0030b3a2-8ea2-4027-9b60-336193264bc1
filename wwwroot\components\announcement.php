<?php
/**
 * 公告弹窗组件
 * 使用方法：在需要显示公告的页面引入此文件
 * require_once 'components/announcement.php';
 */

// 确保Settings类已加载
if (!class_exists('Settings')) {
    require_once dirname(__DIR__) . '/includes/Settings.php';
}

// 加载前台配置
$frontendConfig = include dirname(__DIR__) . '/config.php';

// 获取站点设置文件路径
if (!isset($frontendConfig['site_settings_path'])) {
    throw new RuntimeException('no site_settings_path in frontendConfig?');
}

// 设置Settings类的路径
Settings::setSettingsPath($frontendConfig['site_settings_path']);

// 获取站点设置
$settings = Settings::load();

// 检查是否启用了公告
$announcementEnabled = isset($settings['announcement']) && isset($settings['announcement']['enabled']) && $settings['announcement']['enabled'];

// 如果未启用公告，直接返回
if (!$announcementEnabled) {
    return;
}

// 获取公告内容
$announcementTitle = $settings['announcement']['title'] ?? '网站公告';
$announcementContent = $settings['announcement']['content'] ?? '';
$announcementStyle = $settings['announcement']['style'] ?? 'default';

// 根据样式设置颜色
$styleClasses = [
    'default' => [
        'bg' => 'bg-gray-800',
        'border' => 'border-gray-700',
        'title' => 'text-white',
        'close' => 'text-gray-400 hover:text-white'
    ],
    'info' => [
        'bg' => 'bg-blue-900',
        'border' => 'border-blue-800',
        'title' => 'text-blue-300',
        'close' => 'text-blue-400 hover:text-white'
    ],
    'success' => [
        'bg' => 'bg-green-900',
        'border' => 'border-green-800',
        'title' => 'text-green-300',
        'close' => 'text-green-400 hover:text-white'
    ],
    'warning' => [
        'bg' => 'bg-yellow-900',
        'border' => 'border-yellow-800',
        'title' => 'text-yellow-300',
        'close' => 'text-yellow-400 hover:text-white'
    ],
    'danger' => [
        'bg' => 'bg-red-900',
        'border' => 'border-red-800',
        'title' => 'text-red-300',
        'close' => 'text-red-400 hover:text-white'
    ]
];

// 使用默认样式作为后备
$currentStyle = $styleClasses[$announcementStyle] ?? $styleClasses['default'];
?>

<!-- 公告弹窗 -->
<div id="announcement-modal" class="fixed inset-0 z-50 flex items-center justify-center p-4 hidden">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black bg-opacity-70 backdrop-blur-sm"></div>

    <!-- 弹窗内容 -->
    <div class="relative w-full max-w-md mx-auto <?php echo $currentStyle['bg']; ?> border <?php echo $currentStyle['border']; ?> rounded-lg shadow-xl">
        <!-- 关闭按钮 -->
        <button type="button" class="absolute top-3 right-3 <?php echo $currentStyle['close']; ?> text-xl" onclick="closeAnnouncement()">
            &times;
        </button>

        <!-- 标题 -->
        <div class="p-5">
            <h3 class="text-xl font-bold mb-4 <?php echo $currentStyle['title']; ?>">
                <?php echo htmlspecialchars($announcementTitle); ?>
            </h3>

            <!-- 公告内容 -->
            <div class="text-gray-300 space-y-2">
                <?php echo $announcementContent; ?>
            </div>

            <!-- 底部按钮 -->
            <div class="mt-6 flex justify-end">
                <button
                    type="button"
                    class="text-white bg-blue-600 hover:bg-blue-700 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
                    onclick="closeAnnouncement()"
                >
                    我知道了
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 检查Cookie中是否已经关闭过公告
function checkAnnouncementCookie() {
    // 生成唯一的cookie名称 (基于公告内容的哈希)
    const content = "<?php echo base64_encode($announcementContent . $announcementTitle); ?>";
    const cookieName = 'announcement_viewed_' + content.substring(0, 10);

    // 返回cookie是否存在
    return document.cookie.indexOf(cookieName + '=1') !== -1;
}

// 显示公告弹窗
function showAnnouncement() {
    // 如果已经关闭过，不再显示
    if (checkAnnouncementCookie()) {
        return;
    }

    const modal = document.getElementById('announcement-modal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

// 关闭公告弹窗
function closeAnnouncement() {
    const modal = document.getElementById('announcement-modal');
    if (modal) {
        modal.classList.add('hidden');

        // 设置cookie，记录已经查看过公告
        const content = "<?php echo base64_encode($announcementContent . $announcementTitle); ?>";
        const cookieName = 'announcement_viewed_' + content.substring(0, 10);

        // 设置cookie有效期为7天
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 7);
        document.cookie = cookieName + '=1; expires=' + expirationDate.toUTCString() + '; path=/';
    }
}

// 页面加载后显示公告
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(showAnnouncement, 1000); // 延迟1秒显示公告
});
</script>