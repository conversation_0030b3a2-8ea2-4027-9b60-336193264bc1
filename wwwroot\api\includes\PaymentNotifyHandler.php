<?php
/**
 * 支付通知处理类
 *
 * 统一处理微信支付和支付宝的通知验证和订单更新
 * 支持实时通知处理和后台手动验证
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 确保包含必要的依赖
if (!function_exists('get_payment_config')) {
    require_once dirname(__DIR__) . DS . 'payment.php';
}

class PaymentNotifyHandler {

    private $db;
    private $config;

    /**
     * 构造函数
     *
     * @param SQLite3 $db 数据库连接
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * 自动检测支付类型
     *
     * @param string $rawData 原始通知数据
     * @param array $postData POST数据
     * @return string 支付类型 (wechat_pay, alipay, 或空字符串)
     */
    public function detectPaymentType($rawData, $postData = []) {
        // 检查是否为微信支付通知（XML格式）
        if (!empty($rawData) && (strpos($rawData, '<xml>') !== false || strpos($rawData, '<?xml') !== false)) {
            try {
                $data = $this->parseXml($rawData);
                // 检查微信支付特有字段
                if (isset($data['appid']) && isset($data['mch_id']) && isset($data['sign'])) {
                    return 'wechat_pay';
                }
            } catch (Exception $e) {
                // XML解析失败，继续检查其他类型
            }
        }

        // 检查是否为支付宝通知（表单格式）
        if (!empty($postData)) {
            // 检查支付宝特有字段
            if (isset($postData['app_id']) && isset($postData['sign']) &&
                (isset($postData['trade_status']) || isset($postData['out_trade_no']))) {
                return 'alipay';
            }
        }

        return '';
    }

    /**
     * 解析通知数据
     *
     * @param string $paymentType 支付类型
     * @param string $rawData 原始数据
     * @param array $postData POST数据
     * @return array 解析后的数据
     * @throws Exception 解析失败时抛出异常
     */
    public function parseNotifyData($paymentType, $rawData, $postData = []) {
        switch ($paymentType) {
            case 'wechat_pay':
                if (empty($rawData)) {
                    throw new Exception('微信支付通知数据为空');
                }
                return $this->parseXml($rawData);

            case 'alipay':
                // 如果没有POST数据但有raw_data，尝试从raw_data解析
                if (empty($postData) && !empty($rawData)) {
                    // 先尝试标准的parse_str
                    parse_str($rawData, $postData);

                    // 如果解析失败或结果为空，尝试手动解析
                    if (empty($postData)) {
                        $postData = $this->parseAlipayRawData($rawData);
                    }
                }

                if (empty($postData)) {
                    throw new Exception('支付宝通知数据为空，raw_data: ' . substr($rawData, 0, 200));
                }
                return $postData;

            default:
                throw new Exception('不支持的支付类型: ' . $paymentType);
        }
    }

    /**
     * 验证支付通知签名
     *
     * @param string $paymentType 支付类型
     * @param array $data 通知数据
     * @return array 验证结果 ['valid' => bool, 'error' => string]
     */
    public function verifySignature($paymentType, $data) {
        try {
            // 获取支付配置
            $config = get_payment_config($paymentType);
            if (!$config) {
                return ['valid' => false, 'error' => $paymentType . '支付未启用'];
            }

            $sign = $data['sign'] ?? '';
            if (empty($sign)) {
                return ['valid' => false, 'error' => '签名字段为空'];
            }

            switch ($paymentType) {
                case 'wechat_pay':
                    return PaymentSignature::verifyWechatSign($data, $config['key'], $sign);

                case 'alipay':
                    return PaymentSignature::verifyAlipaySign($data, $config['public_key'], $sign);

                default:
                    return ['valid' => false, 'error' => '不支持的支付类型'];
            }
        } catch (Exception $e) {
            return ['valid' => false, 'error' => '验证签名时发生错误: ' . $e->getMessage()];
        }
    }

    /**
     * 验证支付结果状态
     *
     * @param string $paymentType 支付类型
     * @param array $data 通知数据
     * @return array 验证结果 ['valid' => bool, 'error' => string]
     */
    public function verifyPaymentStatus($paymentType, $data) {
        switch ($paymentType) {
            case 'wechat_pay':
                if (($data['return_code'] ?? '') !== 'SUCCESS') {
                    return ['valid' => false, 'error' => '微信返回失败: ' . ($data['return_msg'] ?? '未知错误')];
                }
                if (($data['result_code'] ?? '') !== 'SUCCESS') {
                    return ['valid' => false, 'error' => '微信支付失败: ' . ($data['err_code_des'] ?? $data['return_msg'] ?? '未知错误')];
                }
                return ['valid' => true, 'error' => null];

            case 'alipay':
                $tradeStatus = $data['trade_status'] ?? '';
                if ($tradeStatus !== 'TRADE_SUCCESS' && $tradeStatus !== 'TRADE_FINISHED') {
                    return ['valid' => false, 'error' => '支付宝交易未成功，状态: ' . $tradeStatus];
                }
                return ['valid' => true, 'error' => null];

            default:
                return ['valid' => false, 'error' => '不支持的支付类型'];
        }
    }

    /**
     * 更新订单状态
     *
     * @param string $paymentType 支付类型
     * @param array $data 通知数据
     * @return array 处理结果 ['success' => bool, 'message' => string]
     */
    public function updateOrderStatus($paymentType, $data) {
        try {
            // 获取订单号和交易ID
            $orderNo = $data['out_trade_no'] ?? '';
            if (empty($orderNo)) {
                return ['success' => false, 'message' => '订单号为空'];
            }

            $transactionId = '';
            switch ($paymentType) {
                case 'wechat_pay':
                    $transactionId = $data['transaction_id'] ?? '';
                    break;
                case 'alipay':
                    $transactionId = $data['trade_no'] ?? '';
                    break;
            }

            // 查询订单
            $stmt = $this->db->prepare('SELECT * FROM payment_orders WHERE order_no = :order_no');
            $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
            $result = $stmt->execute();
            $order = $result->fetchArray(SQLITE3_ASSOC);

            if (!$order) {
                return ['success' => false, 'message' => '订单不存在: ' . $orderNo];
            }

            // 如果订单已经是已支付状态，直接返回成功
            if ($order['status'] === 'paid') {
                return ['success' => true, 'message' => '订单已经是已支付状态'];
            }

            // 更新订单状态
            $stmt = $this->db->prepare('
                UPDATE payment_orders
                SET status = :status, transaction_id = :transaction_id, paid_at = :paid_at
                WHERE order_no = :order_no
            ');

            $stmt->bindValue(':status', 'paid', SQLITE3_TEXT);
            $stmt->bindValue(':transaction_id', $transactionId, SQLITE3_TEXT);
            $stmt->bindValue(':paid_at', time(), SQLITE3_INTEGER);
            $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
            $stmt->execute();

            // 记录下载权限
            $stmt = $this->db->prepare('
                INSERT INTO download_permissions (
                    software_id, ip, order_no, created_at
                ) VALUES (
                    :software_id, :ip, :order_no, :created_at
                )
            ');

            $stmt->bindValue(':software_id', $order['software_id'], SQLITE3_INTEGER);
            $stmt->bindValue(':ip', $order['user_ip'], SQLITE3_TEXT);
            $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
            $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
            $stmt->execute();

            return ['success' => true, 'message' => '订单状态更新成功'];

        } catch (Exception $e) {
            return ['success' => false, 'message' => '更新订单状态失败: ' . $e->getMessage()];
        }
    }

    /**
     * 解析支付宝原始数据
     *
     * @param string $rawData 原始数据
     * @return array 解析后的数组
     */
    private function parseAlipayRawData($rawData) {
        $result = [];

        // 尝试多种解析方式

        // 方式1: 标准URL编码格式
        parse_str($rawData, $result);
        if (!empty($result)) {
            return $result;
        }

        // 方式2: 手动解析，处理特殊字符
        $pairs = explode('&', $rawData);
        foreach ($pairs as $pair) {
            if (strpos($pair, '=') !== false) {
                list($key, $value) = explode('=', $pair, 2);
                $result[urldecode($key)] = urldecode($value);
            }
        }

        if (!empty($result)) {
            return $result;
        }

        // 方式3: 处理可能的JSON格式
        $jsonData = json_decode($rawData, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
            return $jsonData;
        }

        // 方式4: 尝试处理print_r格式的数据（从你的输出看起来像这种格式）
        if (strpos($rawData, 'Array') === 0 || strpos($rawData, '[') !== false) {
            // 这种情况下，数据可能是从print_r输出复制的
            // 处理类似这样的格式: Array([key1]=>value1[key2]=>value2...)

            // 先移除 "Array(" 开头
            $cleanData = preg_replace('/^Array\s*\(\s*/', '', $rawData);
            $cleanData = rtrim($cleanData, ')');

            // 使用正则表达式提取键值对
            // 匹配 [key]=>value 的模式，value可能包含空格和特殊字符
            preg_match_all('/\[([^\]]+)\]\s*=>\s*([^\[]*?)(?=\[|$)/', $cleanData, $matches, PREG_SET_ORDER);

            foreach ($matches as $match) {
                $key = trim($match[1]);
                $value = trim($match[2]);

                // 清理值中的多余空格和换行
                $value = preg_replace('/\s+/', ' ', $value);
                $value = trim($value);

                if ($key !== '') {
                    $result[$key] = $value;
                }
            }
        }

        return $result;
    }

    /**
     * 解析XML数据
     *
     * @param string $xml XML字符串
     * @return array 解析后的数组
     * @throws Exception 解析失败时抛出异常
     */
    private function parseXml($xml) {
        if (empty($xml)) {
            throw new Exception('XML数据为空');
        }

        if (PHP_VERSION_ID < 80000) {
            libxml_disable_entity_loader(true);
        }

        $obj = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        if ($obj === false) {
            throw new Exception('XML解析失败');
        }

        $result = json_decode(json_encode($obj), true);
        if ($result === null) {
            throw new Exception('XML转换为数组失败');
        }

        return $result;
    }

    /**
     * 完整处理支付通知
     *
     * @param string $paymentType 支付类型（可选，如果为空则自动检测）
     * @param string $rawData 原始通知数据
     * @param array $postData POST数据
     * @param bool $updateOrder 是否更新订单状态（默认true）
     * @return array 处理结果
     */
    public function processNotify($paymentType = '', $rawData = '', $postData = [], $updateOrder = true) {
        $result = [
            'success' => false,
            'payment_type' => '',
            'parsed_data' => null,
            'order_no' => '',
            'signature_valid' => false,
            'signature_error' => '',
            'payment_status_valid' => false,
            'payment_status_error' => '',
            'order_updated' => false,
            'order_update_message' => '',
            'message' => ''
        ];

        try {
            // 自动检测支付类型（如果未指定）
            if (empty($paymentType)) {
                $paymentType = $this->detectPaymentType($rawData, $postData);
                if (empty($paymentType)) {
                    $result['message'] = '无法识别支付类型';
                    return $result;
                }
            }

            $result['payment_type'] = $paymentType;

            // 解析通知数据
            $parsedData = $this->parseNotifyData($paymentType, $rawData, $postData);
            $result['parsed_data'] = $parsedData;
            $result['order_no'] = $parsedData['out_trade_no'] ?? '';

            // 验证签名
            $signatureResult = $this->verifySignature($paymentType, $parsedData);
            $result['signature_valid'] = $signatureResult['valid'];
            $result['signature_error'] = $signatureResult['error'] ?? '';
            $result['signature_debug'] = $signatureResult['debug_info'] ?? null;

            if (!$signatureResult['valid']) {
                $result['message'] = '签名验证失败: ' . $result['signature_error'];
                return $result;
            }

            // 验证支付状态
            $statusResult = $this->verifyPaymentStatus($paymentType, $parsedData);
            $result['payment_status_valid'] = $statusResult['valid'];
            $result['payment_status_error'] = $statusResult['error'] ?? '';

            if (!$statusResult['valid']) {
                $result['message'] = '支付状态验证失败: ' . $result['payment_status_error'];
                return $result;
            }

            // 更新订单状态（如果需要）
            if ($updateOrder) {
                $orderResult = $this->updateOrderStatus($paymentType, $parsedData);
                $result['order_updated'] = $orderResult['success'];
                $result['order_update_message'] = $orderResult['message'];

                if (!$orderResult['success']) {
                    $result['message'] = '订单更新失败: ' . $result['order_update_message'];
                    return $result;
                }
            }

            $result['success'] = true;
            $result['message'] = '处理成功';

        } catch (Exception $e) {
            $result['message'] = '处理异常: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 生成支付平台响应
     *
     * @param string $paymentType 支付类型
     * @param bool $success 是否成功
     * @param string $message 消息
     * @return string 响应内容
     */
    public function generateResponse($paymentType, $success, $message = '') {
        switch ($paymentType) {
            case 'wechat_pay':
                if ($success) {
                    return '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
                } else {
                    return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[' . $message . ']]></return_msg></xml>';
                }

            case 'alipay':
                return $success ? 'success' : 'fail';

            default:
                return $success ? 'success' : 'fail';
        }
    }

    /**
     * 保存通知日志到数据库
     *
     * @param array $logData 日志数据
     * @return bool 是否保存成功
     */
    public function saveNotifyLog($logData) {
        try {
            $stmt = $this->db->prepare('
                INSERT INTO payment_notify_logs (
                    payment_type, raw_data, order_no,
                    signature_valid, signature_error, processing_result, processing_error,
                    ip_address, request_line, request_headers, server_info,
                    created_at
                ) VALUES (
                    :payment_type, :raw_data, :order_no,
                    :signature_valid, :signature_error, :processing_result, :processing_error,
                    :ip_address, :request_line, :request_headers, :server_info,
                    :created_at
                )
            ');

            $stmt->bindValue(':payment_type', $logData['payment_type'] ?? '', SQLITE3_TEXT);
            $stmt->bindValue(':raw_data', $logData['raw_data'] ?? '', SQLITE3_TEXT);
            $stmt->bindValue(':order_no', $logData['order_no'] ?? null,
                            $logData['order_no'] ? SQLITE3_TEXT : SQLITE3_NULL);
            $stmt->bindValue(':signature_valid', $logData['signature_valid'] ?? -1, SQLITE3_INTEGER);
            $stmt->bindValue(':signature_error', $logData['signature_error'] ?? null,
                            $logData['signature_error'] ? SQLITE3_TEXT : SQLITE3_NULL);
            $stmt->bindValue(':processing_result', $logData['processing_result'] ?? 'error', SQLITE3_TEXT);
            $stmt->bindValue(':processing_error', $logData['processing_error'] ?? null,
                            $logData['processing_error'] ? SQLITE3_TEXT : SQLITE3_NULL);
            $stmt->bindValue(':ip_address', $logData['ip_address'] ?? '', SQLITE3_TEXT);
            $stmt->bindValue(':request_line', $logData['request_line'] ?? '', SQLITE3_TEXT);
            $stmt->bindValue(':request_headers', $logData['request_headers'] ?? '', SQLITE3_TEXT);
            $stmt->bindValue(':server_info', $logData['server_info'] ?? '', SQLITE3_TEXT);
            $stmt->bindValue(':created_at', $logData['created_at'] ?? time(), SQLITE3_INTEGER);

            return $stmt->execute() !== false;

        } catch (Exception $e) {
            error_log('Failed to save payment notify log: ' . $e->getMessage());
            return false;
        }
    }
}
