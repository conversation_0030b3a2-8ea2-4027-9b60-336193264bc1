<?php
/**
 * 管理面板 - 角色管理
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 设置页面标题和活动菜单
$pageTitle = '角色管理';
$activeMenu = 'roles';

// 渲染头部（需要'role.view'权限）
render_header($pageTitle, $activeMenu, 'role.view');
?>

<!-- 页面内容开始 -->
<div id="rolesApp">
    <!-- 顶部操作栏 -->
    <div class="flex flex-wrap justify-between items-center mb-4 gap-4">
        <div class="flex flex-wrap items-center gap-4">
            <div class="relative">
                <input type="text" v-model="searchQuery" @input="debounceSearch" placeholder="搜索角色..." class="form-input pr-10">
                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            </div>
        </div>

        <div>
            <button v-if="checkPermission('role.add')" @click="showAddModal = true" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i> 添加角色
            </button>
        </div>
    </div>

    <!-- 角色列表 -->
    <div class="content-card">
        <h2>角色列表</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else-if="roleList.length === 0" class="text-center py-8 text-gray-400">
            <i class="fas fa-user-shield text-4xl mb-3"></i>
            <p>暂无角色数据</p>
        </div>

        <div v-else>
            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="w-16">ID</th>
                            <th>角色名称</th>
                            <th>描述</th>
                            <th>管理员数量</th>
                            <th>创建时间</th>
                            <th class="w-32">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="role in roleList" :key="role.id">
                            <td>{{ role.id }}</td>
                            <td>{{ role.name }}</td>
                            <td>{{ role.description || '无描述' }}</td>
                            <td>{{ role.admin_count || 0 }}</td>
                            <td>{{ formatTime(role.created_at) }}</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button v-if="checkPermission('role.edit')" @click="editRole(role)" class="btn-icon text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button v-if="checkPermission('role.delete')" @click="confirmDelete(role)" class="btn-icon text-red-400 hover:text-red-300" :disabled="role.admin_count > 0">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-400">
                    共 {{ pagination.totalItems }} 条记录，第 {{ pagination.page }}/{{ pagination.totalPages }} 页
                </div>
                <div class="flex space-x-2">
                    <button @click="changePage(1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button @click="changePage(pagination.page - 1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button @click="changePage(pagination.page + 1)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button @click="changePage(pagination.totalPages)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑角色模态框 -->
    <div v-if="showAddModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-4xl">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl text-blue-300 font-semibold">{{ showEditModal ? '编辑角色' : '添加角色' }}</h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form @submit.prevent="submitForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="form-group">
                                <label class="form-label">角色名称 <span class="text-red-500">*</span></label>
                                <input type="text" v-model="formData.name" class="form-input" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">描述</label>
                                <textarea v-model="formData.description" class="form-input" rows="3"></textarea>
                            </div>
                        </div>

                        <div>
                            <div class="form-group">
                                <label class="form-label">权限设置</label>
                                <div class="bg-gray-800 bg-opacity-50 p-3 rounded-lg border border-gray-700 h-64 overflow-y-auto">
                                    <!-- 超级管理员权限 -->
                                    <div class="mb-4">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" v-model="formData.permissions" value="all" class="form-checkbox">
                                            <span class="ml-2 text-yellow-300 font-medium">所有权限（超级管理员）</span>
                                        </label>
                                        <div class="text-xs text-gray-400 mt-1 ml-6">授予所有权限，包括将来添加的新权限</div>
                                    </div>

                                    <!-- 权限分组 -->
                                    <div v-for="(permissions, group) in permissionGroups" :key="group" class="mb-4">
                                        <div class="text-blue-300 font-medium mb-2">{{ getGroupName(group) }}</div>
                                        <div class="ml-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                                            <label v-for="(desc, perm) in permissions" :key="perm" class="inline-flex items-center">
                                                <input type="checkbox" v-model="formData.permissions" :value="perm" class="form-checkbox" :disabled="formData.permissions.includes('all')">
                                                <span class="ml-2" :class="{ 'text-gray-500': formData.permissions.includes('all') }">{{ desc }}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" @click="closeModal" class="btn bg-gray-700 text-white">取消</button>
                        <button type="submit" class="btn btn-primary" :disabled="submitting">
                            <span v-if="submitting"><i class="fas fa-spinner fa-spin mr-2"></i> 提交中...</span>
                            <span v-else>{{ showEditModal ? '保存修改' : '添加角色' }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
            <div class="p-6">
                <div class="text-center mb-6">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-5xl mb-4"></i>
                    <h3 class="text-xl text-red-300 font-semibold">确认删除</h3>
                    <p class="mt-2 text-gray-300">您确定要删除角色 "{{ roleToDelete.name }}" 吗？此操作不可撤销。</p>
                    <p v-if="roleToDelete.admin_count > 0" class="mt-2 text-red-400">
                        <i class="fas fa-exclamation-circle mr-1"></i>
                        该角色下有 {{ roleToDelete.admin_count }} 个管理员，无法删除。请先移除这些管理员的角色。
                    </p>
                </div>

                <div class="flex justify-center space-x-3">
                    <button @click="showDeleteModal = false" class="btn bg-gray-700 text-white">取消</button>
                    <button @click="deleteRole" class="btn btn-danger" :disabled="deleting || roleToDelete.admin_count > 0">
                        <span v-if="deleting"><i class="fas fa-spinner fa-spin mr-2"></i> 删除中...</span>
                        <span v-else>确认删除</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 页面内容结束 -->

<script>
    new Vue({
        el: '#rolesApp',
        data: {
            loading: true,
            roleList: [],
            searchQuery: '',
            pagination: {
                page: 1,
                pageSize: 10,
                totalItems: 0,
                totalPages: 1
            },
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            formData: {
                id: null,
                name: '',
                description: '',
                permissions: []
            },
            roleToDelete: {},
            submitting: false,
            deleting: false,
            searchTimeout: null,
            permissionGroups: {
                admin: {},
                role: {},
                software: {},
                category: {},
                stats: {},
                system: {}
            }
        },
        mounted() {
            this.loadPermissions();
            this.loadRoleList();
        },
        methods: {
            // 检查权限
            checkPermission(permission) {
                const userPermissions = JSON.parse(localStorage.getItem('admin_permissions') || '[]');
                return userPermissions.includes('all') || userPermissions.includes(permission);
            },

            // 格式化时间戳
            formatTime(timestamp) {
                if (!timestamp) return '未知';
                const date = new Date(timestamp * 1000);
                return date.toLocaleString();
            },

            // 获取权限组名称
            getGroupName(group) {
                const groupNames = {
                    admin: '管理员管理',
                    role: '角色管理',
                    software: '软件管理',
                    category: '分类管理',
                    stats: '统计数据',
                    system: '系统设置'
                };
                return groupNames[group] || group;
            },

            // 加载权限列表
            loadPermissions() {
                fetch('<?php echo $apiPath; ?>panel/permissions.php')
                    .then(response => {
                        if (response.status === 401) {
                            window.location.href = '<?php echo $basePath; ?>public/login.html';
                            return;
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.success) {
                            this.permissionGroups = data.data || {};
                        }
                    })
                    .catch(error => {
                        console.error('加载权限数据出错：', error);
                    });
            },

            // 加载角色列表
            loadRoleList() {
                this.loading = true;

                let url = `<?php echo $apiPath; ?>panel/roles.php?page=${this.pagination.page}&pageSize=${this.pagination.pageSize}`;

                if (this.searchQuery) {
                    url += `&search=${encodeURIComponent(this.searchQuery)}`;
                }

                fetch(url)
                    .then(response => {
                        if (response.status === 401) {
                            window.location.href = '<?php echo $basePath; ?>public/login.html';
                            return;
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.success) {
                            this.roleList = data.data.items || [];
                            this.pagination = data.data.pagination || this.pagination;
                        }
                        this.loading = false;
                    })
                    .catch(error => {
                        console.error('加载角色数据出错：', error);
                        this.loading = false;
                    });
            },

            // 防抖搜索
            debounceSearch() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.pagination.page = 1;
                    this.loadRoleList();
                }, 300);
            },

            // 切换页码
            changePage(page) {
                if (page < 1 || page > this.pagination.totalPages) {
                    return;
                }
                this.pagination.page = page;
                this.loadRoleList();
            },

            // 编辑角色
            editRole(role) {
                // 获取角色详情
                fetch(`<?php echo $apiPath; ?>panel/roles.php?action=get&id=${role.id}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.success && data.role) {
                            const roleData = data.role;
                            this.formData = {
                                id: roleData.id,
                                name: roleData.name,
                                description: roleData.description || '',
                                permissions: JSON.parse(roleData.permissions || '[]')
                            };
                            this.showEditModal = true;
                        } else {
                            alert('获取角色详情失败：' + (data.message || '未知错误'));
                        }
                    })
                    .catch(error => {
                        console.error('获取角色详情出错：', error);
                        alert('获取角色详情请求出错，请重试');
                    });
            },

            // 确认删除
            confirmDelete(role) {
                this.roleToDelete = role;
                this.showDeleteModal = true;
            },

            // 删除角色
            deleteRole() {
                // 如果角色下有管理员，不允许删除
                if (this.roleToDelete.admin_count > 0) {
                    alert('该角色下有管理员，无法删除。请先移除这些管理员的角色。');
                    return;
                }

                this.deleting = true;

                fetch(`<?php echo $apiPath; ?>panel/roles.php?id=${this.roleToDelete.id}`, {
                    method: 'DELETE'
                })
                    .then(response => response.json())
                    .then(data => {
                        this.deleting = false;
                        this.showDeleteModal = false;

                        if (data.success) {
                            // 删除成功，刷新列表
                            this.loadRoleList();
                            alert('角色删除成功');
                        } else {
                            alert('删除失败：' + (data.message || '未知错误'));
                        }
                    })
                    .catch(error => {
                        this.deleting = false;
                        this.showDeleteModal = false;
                        console.error('删除角色出错：', error);
                        alert('删除请求出错，请重试');
                    });
            },

            // 提交表单
            submitForm() {
                // 验证表单
                if (!this.formData.name) {
                    alert('角色名称不能为空');
                    return;
                }

                this.submitting = true;

                if (this.showEditModal) {
                    // 编辑模式
                    fetch(`<?php echo $apiPath; ?>panel/roles.php?id=${this.formData.id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(this.formData)
                    })
                        .then(response => response.json())
                        .then(data => {
                            this.submitting = false;

                            if (data.success) {
                                this.closeModal();
                                this.loadRoleList();
                                alert('角色更新成功');
                            } else {
                                alert('更新失败：' + (data.message || '未知错误'));
                            }
                        })
                        .catch(error => {
                            this.submitting = false;
                            console.error('更新角色出错：', error);
                            alert('更新请求出错，请重试');
                        });
                } else {
                    // 添加模式
                    fetch('<?php echo $apiPath; ?>panel/roles.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(this.formData)
                    })
                        .then(response => response.json())
                        .then(data => {
                            this.submitting = false;

                            if (data.success) {
                                this.closeModal();
                                this.loadRoleList();
                                alert('角色添加成功');
                            } else {
                                alert('添加失败：' + (data.message || '未知错误'));
                            }
                        })
                        .catch(error => {
                            this.submitting = false;
                            console.error('添加角色出错：', error);
                            alert('添加请求出错，请重试');
                        });
                }
            },

            // 关闭模态框
            closeModal() {
                this.showAddModal = false;
                this.showEditModal = false;
                this.formData = {
                    id: null,
                    name: '',
                    description: '',
                    permissions: []
                };
                this.submitting = false;
            }
        }
    });
</script>

<?php
// 渲染页脚
render_footer();
?>
