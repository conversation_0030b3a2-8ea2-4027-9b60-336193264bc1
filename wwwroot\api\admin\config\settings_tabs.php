<?php
/**
 * 管理后台设置选项卡配置加载器
 *
 * 该文件负责加载所有设置选项卡配置文件并合并它们
 * 可以被其他文件包含使用，避免重复代码
 */

if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

/**
 * 加载所有设置选项卡配置
 *
 * @param string $configDir 配置文件目录路径
 * @return array 合并后的选项卡配置数组
 */
function load_settings_tabs($configDir = null) {
    // 如果未提供配置目录，使用当前文件所在目录
    if ($configDir === null) {
        $configDir = __DIR__;
    }

    // 初始化选项卡配置数组
    $tabsConfig = [];

    // 网站设置选项卡
    if (file_exists($configDir . DS . 'settings_tabs_website.php')) {
        $websiteTabsConfig = include $configDir . DS . 'settings_tabs_website.php';
        $tabsConfig = array_merge($tabsConfig, $websiteTabsConfig);
    }

    // 列表设置选项卡
    if (file_exists($configDir . DS . 'settings_tabs_list.php')) {
        $listTabsConfig = include $configDir . DS . 'settings_tabs_list.php';
        $tabsConfig = array_merge($tabsConfig, $listTabsConfig);
    }

    // 系统设置选项卡
    if (file_exists($configDir . DS . 'settings_tabs_system.php')) {
        $systemTabsConfig = include $configDir . DS . 'settings_tabs_system.php';
        $tabsConfig = array_merge($tabsConfig, $systemTabsConfig);
    }

    // 支付设置选项卡
    if (file_exists($configDir . DS . 'settings_tabs_payment.php')) {
        $paymentTabsConfig = include $configDir . DS . 'settings_tabs_payment.php';
        $tabsConfig = array_merge($tabsConfig, $paymentTabsConfig);
    }

    // 上传设置选项卡
    if (file_exists($configDir . DS . 'settings_tabs_upload.php')) {
        $uploadTabsConfig = include $configDir . DS . 'settings_tabs_upload.php';
        $tabsConfig = array_merge($tabsConfig, $uploadTabsConfig);
    }

    // 调试设置选项卡
    if (file_exists($configDir . DS . 'settings_tabs_debug.php')) {
        $debugTabsConfig = include $configDir . DS . 'settings_tabs_debug.php';
        $tabsConfig = array_merge($tabsConfig, $debugTabsConfig);
    }

    return $tabsConfig;
}

// 如果直接访问此文件，则返回所有选项卡配置
if (basename($_SERVER['SCRIPT_FILENAME']) === basename(__FILE__)) {
    return load_settings_tabs();
}
