<?php
/**
 * 支付通知记录页面（使用新的模板系统）
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 加载页面配置
$pageConfig = include dirname(__DIR__) . '/configs/pages/payment-notify.config.php';

// 获取API路径
$apiPath = $adminConfig['api_path'];

// 渲染头部
$requiredPermissions = !empty($pageConfig['permissions']) ? $pageConfig['permissions'] : null;
render_header($pageConfig['title'], $pageConfig['activeMenu'], $requiredPermissions);
?>

<!-- 页面内容开始 -->
<div id="<?php echo $pageConfig['appId']; ?>">
    <!-- 使用AdminListView组件 -->
    <admin-list-view :config="pageConfig" ref="listView">
        <!-- 自定义列插槽 -->
        <template #column-payment_type="{ item, value }">
            <span v-if="item.payment_type === 'wechat_pay'" class="text-green-400">
                <i class="fab fa-weixin mr-1"></i> 微信支付
            </span>
            <span v-else-if="item.payment_type === 'alipay'" class="text-blue-400">
                <i class="fab fa-alipay mr-1"></i> 支付宝
            </span>
            <span v-else class="text-gray-400">{{ item.payment_type }}</span>
        </template>

        <template #column-signature_valid="{ item, value }">
            <span v-if="item.signature_valid === 1" class="text-green-400">
                <i class="fas fa-check-circle mr-1"></i> 验证成功
            </span>
            <span v-else-if="item.signature_valid === 0" class="text-red-400">
                <i class="fas fa-times-circle mr-1"></i> 验证失败
            </span>
            <span v-else class="text-gray-400">
                <i class="fas fa-question-circle mr-1"></i> 未验证
            </span>
        </template>

        <template #column-processing_result="{ item, value }">
            <span v-if="item.processing_result === 'success'" class="text-green-400">
                <i class="fas fa-check mr-1"></i> 成功
            </span>
            <span v-else-if="item.processing_result === 'failed'" class="text-yellow-400">
                <i class="fas fa-exclamation-triangle mr-1"></i> 失败
            </span>
            <span v-else class="text-red-400">
                <i class="fas fa-times mr-1"></i> 错误
            </span>
        </template>

        <!-- 模态框插槽 -->
        <template #modals>
            <!-- 详情模态框 -->
            <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-4xl max-h-screen overflow-y-auto">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl text-blue-300 font-semibold">支付通知详情</h3>
                            <button @click="closeDetailModal" class="text-gray-400 hover:text-white">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div v-if="currentRecord">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- 基本信息 -->
                                <div class="form-group">
                                    <h4 class="text-blue-300 mb-3">基本信息</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">记录ID:</span>
                                            <span>{{ currentRecord.id }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">支付类型:</span>
                                            <span>
                                                <span v-if="currentRecord.payment_type === 'wechat_pay'" class="text-green-400">
                                                    <i class="fab fa-weixin mr-1"></i> 微信支付
                                                </span>
                                                <span v-else-if="currentRecord.payment_type === 'alipay'" class="text-blue-400">
                                                    <i class="fab fa-alipay mr-1"></i> 支付宝
                                                </span>
                                                <span v-else>{{ currentRecord.payment_type }}</span>
                                            </span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">订单号:</span>
                                            <span>{{ currentRecord.order_no || '-' }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">IP地址:</span>
                                            <span>{{ currentRecord.ip_address }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">创建时间:</span>
                                            <span>{{ formatDateTime(currentRecord.created_at) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 处理状态 -->
                                <div class="form-group">
                                    <h4 class="text-blue-300 mb-3">处理状态</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">签名验证:</span>
                                            <span>
                                                <span v-if="currentRecord.signature_valid === 1" class="text-green-400">
                                                    <i class="fas fa-check-circle mr-1"></i> 验证成功
                                                </span>
                                                <span v-else-if="currentRecord.signature_valid === 0" class="text-red-400">
                                                    <i class="fas fa-times-circle mr-1"></i> 验证失败
                                                </span>
                                                <span v-else class="text-gray-400">
                                                    <i class="fas fa-question-circle mr-1"></i> 未验证
                                                </span>
                                            </span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">处理结果:</span>
                                            <span>
                                                <span v-if="currentRecord.processing_result === 'success'" class="text-green-400">
                                                    <i class="fas fa-check mr-1"></i> 成功
                                                </span>
                                                <span v-else-if="currentRecord.processing_result === 'failed'" class="text-yellow-400">
                                                    <i class="fas fa-exclamation-triangle mr-1"></i> 失败
                                                </span>
                                                <span v-else class="text-red-400">
                                                    <i class="fas fa-times mr-1"></i> 错误
                                                </span>
                                            </span>
                                        </div>
                                        <div v-if="currentRecord.signature_error" class="flex flex-col">
                                            <span class="text-gray-400 mb-1">签名错误:</span>
                                            <span class="text-red-400 text-sm break-all">{{ currentRecord.signature_error }}</span>
                                        </div>
                                        <div v-if="currentRecord.processing_error" class="flex flex-col">
                                            <span class="text-gray-400 mb-1">处理错误:</span>
                                            <span class="text-red-400 text-sm break-all">{{ currentRecord.processing_error }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 原始数据 -->
                            <div class="form-group mt-6">
                                <h4 class="text-blue-300 mb-3">原始数据</h4>
                                <textarea class="form-input" rows="4" readonly>{{ currentRecord.raw_data }}</textarea>
                            </div>

                            <!-- 解析数据 -->
                            <div v-if="currentRecord.parsed_data" class="form-group mt-6">
                                <h4 class="text-blue-300 mb-3">解析数据</h4>
                                <pre class="bg-gray-800 p-4 rounded border border-gray-700 text-sm overflow-x-auto"><code>{{ formatJson(currentRecord.parsed_data) }}</code></pre>
                            </div>

                            <!-- HTTP请求行 -->
                            <div class="form-group mt-6">
                                <h4 class="text-blue-300 mb-3">HTTP请求行</h4>
                                <div class="bg-gray-800 p-3 rounded border border-gray-700 text-sm break-all">
                                    {{ currentRecord.request_line || '-' }}
                                </div>
                            </div>

                            <!-- 请求头信息 -->
                            <div v-if="currentRecord.request_headers" class="form-group mt-6">
                                <h4 class="text-blue-300 mb-3">请求头信息</h4>
                                <pre class="bg-gray-800 p-4 rounded border border-gray-700 text-sm overflow-x-auto"><code>{{ formatJson(currentRecord.request_headers) }}</code></pre>
                            </div>

                            <!-- 服务器信息 -->
                            <div v-if="currentRecord.server_info" class="form-group mt-6">
                                <h4 class="text-blue-300 mb-3">服务器信息</h4>
                                <pre class="bg-gray-800 p-4 rounded border border-gray-700 text-sm overflow-x-auto"><code>{{ formatJson(currentRecord.server_info) }}</code></pre>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 mt-6">
                            <button @click="closeDetailModal" class="btn bg-gray-700 text-white">关闭</button>
                            <button v-if="checkPermission('payment_notify.verify') && currentRecord && currentRecord.signature_valid !== 1"
                                    @click="verifySignatureFromModal"
                                    class="btn btn-primary"
                                    :disabled="verifying">
                                <span v-if="verifying"><i class="fas fa-spinner fa-spin mr-2"></i> 验签中...</span>
                                <span v-else><i class="fas fa-shield-alt mr-2"></i> 手动验签</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </admin-list-view>
</div>

<script>
    new Vue({
        el: '#<?php echo $pageConfig['appId']; ?>',
        components: {
            'admin-list-view': AdminListView
        },
        data: {
            pageConfig: <?php echo json_encode($pageConfig, JSON_UNESCAPED_UNICODE); ?>,
            showDetailModal: false,
            currentRecord: null,
            verifying: false
        },
        methods: {
            // 检查权限
            checkPermission(permission) {
                return AdminUtils.checkPermission(permission);
            },

            // 格式化日期时间
            formatDateTime(timestamp) {
                return AdminUtils.formatDateTime(timestamp);
            },

            // 显示详情
            async showDetail(record) {
                try {
                    const response = await apiGet(`<?php echo $apiPath; ?>panel/payment_notify.php?action=get&id=${record.id}`);

                    if (response.success) {
                        this.currentRecord = response.record;
                        this.showDetailModal = true;
                    } else {
                        alert('获取详情失败: ' + response.message);
                    }
                } catch (error) {
                    console.error('获取详情失败:', error);
                    alert('获取详情失败');
                }
            },

            // 关闭详情模态框
            closeDetailModal() {
                this.showDetailModal = false;
                this.currentRecord = null;
            },

            // 手动验签
            async verifySignature(record) {
                if (!confirm('确定要对此记录进行手动验签吗？')) {
                    return;
                }

                try {
                    const response = await apiPost('<?php echo $apiPath; ?>panel/payment_notify.php', {
                        action: 'verify',
                        id: record.id
                    }, {}, {
                        operation: '手动验签'
                    });

                    if (response.success) {
                        alert('验签完成: ' + (response.verify_result.valid ? '验证成功' : '验证失败'));
                        this.$refs.listView.loadItems();
                    } else {
                        alert('验签失败: ' + response.message);
                    }
                } catch (error) {
                    console.error('验签失败:', error);
                    alert('验签失败');
                }
            },

            // 手动验签（从模态框）
            async verifySignatureFromModal() {
                if (!this.currentRecord || !confirm('确定要对此记录进行手动验签吗？')) {
                    return;
                }

                this.verifying = true;

                try {
                    const response = await apiPost('<?php echo $apiPath; ?>panel/payment_notify.php', {
                        action: 'verify',
                        id: this.currentRecord.id
                    }, {}, {
                        operation: '手动验签'
                    });

                    if (response.success) {
                        alert('验签完成: ' + (response.verify_result.valid ? '验证成功' : '验证失败'));

                        // 刷新当前记录详情
                        await this.showDetail(this.currentRecord);

                        // 刷新列表
                        this.$refs.listView.loadItems();
                    } else {
                        alert('验签失败: ' + response.message);
                    }
                } catch (error) {
                    console.error('验签失败:', error);
                    alert('验签失败');
                } finally {
                    this.verifying = false;
                }
            },

            // 格式化JSON
            formatJson(jsonString) {
                try {
                    const obj = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString;
                    return JSON.stringify(obj, null, 2);
                } catch (error) {
                    return jsonString;
                }
            }
        }
    });
</script>

<?php
// 渲染页面底部
render_footer();
?>
