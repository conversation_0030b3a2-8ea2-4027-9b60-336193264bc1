<?php
/**
 * API专用公共函数库
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 定义API根目录常量
if (!defined('__API_ROOT__')) {
    define('__API_ROOT__', __DIR__);
}

// 定义网站根目录常量
if (!defined('__WWWROOT__')) {
    define('__WWWROOT__', dirname(__DIR__));
}

// 定义根目录常量
if (!defined('__ROOT__')) {
    define('__ROOT__', dirname(__WWWROOT__));
}

require_once __WWWROOT__ . DS . 'includes' . DS. '/Settings.php';

/**
 * 获取组件配置信息
 *
 * @return array 组件配置信息
 */
function get_config() {
    // 加载API配置
    return include __API_ROOT__ . DS . 'config.php';
}

/**
 * 获取站点设置信息
 *
 * @return array 站点设置信息
 */
function get_settings() {
    // 加载API配置
    $apiConfig = include __API_ROOT__ . DS . 'config.php';

    // 获取站点设置文件路径
    if (!isset($apiConfig['site_settings_path'])) {
        throw new RuntimeException('no site_settings_path in apiConfig?');
    }

    // 确保Settings类已加载
    if (!class_exists('Settings')) {
        require_once __WWWROOT__ . DS . 'includes' . DS . 'Settings.php';
    }
    Settings::setSettingsPath($apiConfig['site_settings_path']);

    // 加载站点设置
    return Settings::load();
}

/**
 * 获取数据库连接
 *
 * @return SQLite3 数据库连接
 */
function get_db_connection() {
    $settings = get_settings();
    if (!isset($settings['db_path'])) {
        throw new RuntimeException('no db_path in settings?');
    }
    $dbPath = $settings['db_path'];

    try {
        $db = new SQLite3($dbPath);
        $db->enableExceptions(true);
        return $db;
    } catch (Exception $e) {
        header('HTTP/1.1 500 Internal Server Error');
        header('Content-Type: application/json');
        echo json_encode(['error' => '数据库连接失败', 'message' => $e->getMessage()]);
        exit;
    }
}

/**
 * 检查系统是否已安装
 * 如果未安装，根据请求类型返回适当的响应
 *
 * @param bool $redirectOnFail 是否在未安装时重定向
 * @return bool 是否已安装
 */
function check_installed($redirectOnFail = true) {
    // 检查当前脚本，避免重定向循环
    $currentScript = $_SERVER['SCRIPT_NAME'];

    // 如果已在安装页面或安装API，跳过检查
    if (strpos($currentScript, 'install.php') !== false ||
        strpos($currentScript, '/api/public/install.php') !== false) {
        return false;
    }

    // 确保Settings类已加载
    if (!class_exists('Settings')) {
        require_once __WWWROOT__ . DS . 'includes' . DS . 'Settings.php';
    }

    // 使用Settings类检查是否已安装
    $installed = Settings::isInstalled();

    // 如果未安装且需要重定向
    if (!$installed && $redirectOnFail) {
        // 检查请求头中的Accept字段
        $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
        $wantsJson = strpos($acceptHeader, 'application/json') !== false;

        // 检查是否是API请求
        $isApiRequest = strpos($currentScript, '/api/') !== false;

        // 如果是API请求或客户端明确要求JSON响应
        if ($isApiRequest || $wantsJson) {
            // 返回JSON响应
            header('HTTP/1.1 503 Service Unavailable');
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'installed' => false,
                'redirect' => '/install.php',
                'message' => '系统未安装，请先完成安装'
            ]);
            exit;
        } else {
            // 非API请求且未明确要求JSON，直接重定向
            header('Location: /install.php');
            exit;
        }
    }

    return $installed;
}

/**
 * 安全地获取请求参数
 *
 * @param string $param 参数名
 * @param mixed $default 默认值
 * @param bool $sanitize 是否进行清理
 * @return mixed 参数值
 */
function get_param($param, $default = null, $sanitize = true) {
    $value = $_REQUEST[$param] ?? $default;

    if ($sanitize && is_string($value)) {
        $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }

    return $value;
}

/**
 * 安全地获取POST参数
 *
 * @param string $key 参数名
 * @param mixed $default 默认值
 * @param bool $sanitize 是否进行清理
 * @return mixed 参数值
 */
function post_param($key, $default = null, $sanitize = true) {
    $value = $_POST[$key] ?? $default;

    if ($sanitize && is_string($value)) {
        $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }

    return $value;
}

/**
 * 获取JSON请求数据
 *
 * @return array 解析后的JSON数据
 */
function get_json_data() {
    $json = file_get_contents('php://input');
    return json_decode($json, true) ?: [];
}

/**
 * 返回JSON响应
 *
 * @param mixed $data 响应数据
 * @param int $status HTTP状态码
 */
function json_response($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 返回成功的API响应
 *
 * @param mixed $data 响应数据
 * @param string $message 成功消息
 */
function api_success($data = null, $message = '操作成功') {
    $response = [
        'success' => true,
        'message' => $message
    ];

    if ($data !== null) {
        $response['data'] = $data;
    }

    json_response($response);
}

/**
 * 返回失败的API响应
 *
 * @param string $message 错误消息
 * @param mixed $error 详细错误信息
 * @param int $status HTTP状态码
 */
function api_error($message = '操作失败', $error = null, $status = 400) {
    $response = [
        'success' => false,
        'message' => $message
    ];

    if ($error !== null) {
        $response['error'] = $error;
    }

    json_response($response, $status);
}

/**
 * 获取客户端IP
 *
 * @return string 客户端IP
 */
function get_client_ip() {
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ipAddresses = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ipAddresses[0]);
    } else if (isset($_SERVER['HTTP_X_REAL_IP'])) {
        return $_SERVER['HTTP_X_REAL_IP'];
    } else if (isset($_SERVER['REMOTE_ADDR'])) {
        return $_SERVER['REMOTE_ADDR'];
    }
    return '127.0.0.1';
}

/**
 * 自定义错误处理函数
 * 捕获PHP错误并转换为JSON响应
 *
 * @param int $errno 错误级别
 * @param string $errstr 错误信息
 * @param string $errfile 发生错误的文件
 * @param int $errline 发生错误的行号
 * @return bool 是否继续执行PHP的标准错误处理
 */
function api_error_handler($errno, $errstr, $errfile, $errline) {
    // 只处理指定的错误类型
    if (!(error_reporting() & $errno)) {
        return false;
    }

    // 获取错误类型名称
    $error_types = [
        E_ERROR => 'Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Standards',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated',
    ];
    $error_type = $error_types[$errno] ?? 'Unknown Error';

    // 构建错误信息
    $error_message = "$error_type: $errstr in $errfile on line $errline";

    // 记录到错误日志
    error_log($error_message);

    // 如果是API请求，返回JSON格式的错误信息
    if (strpos($_SERVER['SCRIPT_NAME'], '/api/') !== false) {
        $response = [
            'success' => false,
            'message' => '处理请求时发生错误',
            'error' => [
                'type' => $error_type,
                'message' => $errstr,
                'file' => basename($errfile),
                'line' => $errline
            ]
        ];

        http_response_code(500);
        header('Content-Type: application/json');
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 对于非API请求，继续使用PHP的标准错误处理
    return false;
}

/**
 * 自定义异常处理函数
 * 捕获未处理的异常并转换为JSON响应
 *
 * @param Throwable $exception 异常对象
 */
function api_exception_handler($exception) {
    // 构建错误信息
    $error_message = "Uncaught Exception: " . $exception->getMessage() .
                     " in " . $exception->getFile() .
                     " on line " . $exception->getLine();

    // 记录到错误日志
    error_log($error_message);

    // 如果是API请求，返回JSON格式的错误信息
    if (strpos($_SERVER['SCRIPT_NAME'], '/api/') !== false) {
        $response = [
            'success' => false,
            'message' => '发生意外错误',
            'error' => [
                'type' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => basename($exception->getFile()),
                'line' => $exception->getLine()
            ]
        ];

        http_response_code(500);
        header('Content-Type: application/json');
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 对于非API请求，显示标准的异常信息
    echo "<h1>应用程序错误</h1>";
    echo "<p>发生意外错误，请稍后再试。</p>";
    if (ini_get('display_errors')) {
        echo "<p>错误详情: " . htmlspecialchars($exception->getMessage()) . "</p>";
    }
    exit;
}

// 设置自定义错误和异常处理函数
set_error_handler('api_error_handler');
set_exception_handler('api_exception_handler');