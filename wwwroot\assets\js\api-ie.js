/**
 * IE兼容的API请求工具函数
 * 使用ES5语法和XMLHttpRequest替代fetch API
 */

/**
 * 全局加载遮罩管理 (IE兼容版本)
 */
var LoadingMask = {
    element: null,
    counter: 0,

    /**
     * 显示加载遮罩
     * @param {string} message 加载提示文本
     */
    show: function(message) {
        message = message || '加载中...';
        this.counter++;

        if (!this.element) {
            this.element = document.createElement('div');
            this.element.id = 'api-loading-mask';
            this.element.innerHTML = 
                '<div class="loading-backdrop">' +
                    '<div class="loading-content">' +
                        '<div class="loading-spinner"></div>' +
                        '<div class="loading-text">' + message + '</div>' +
                    '</div>' +
                '</div>';

            // 添加样式
            var style = document.createElement('style');
            style.textContent = 
                '#api-loading-mask {' +
                    'position: fixed;' +
                    'top: 0;' +
                    'left: 0;' +
                    'width: 100%;' +
                    'height: 100%;' +
                    'z-index: 9999;' +
                    'display: -ms-flexbox;' +
                    'display: flex;' +
                    '-ms-flex-align: center;' +
                    'align-items: center;' +
                    '-ms-flex-pack: center;' +
                    'justify-content: center;' +
                '}' +
                '#api-loading-mask .loading-backdrop {' +
                    'position: absolute;' +
                    'top: 0;' +
                    'left: 0;' +
                    'width: 100%;' +
                    'height: 100%;' +
                    'background: rgba(0, 0, 0, 0.5);' +
                '}' +
                '#api-loading-mask .loading-content {' +
                    'position: relative;' +
                    'background: rgba(20, 30, 60, 0.9);' +
                    'border: 1px solid #00aaff;' +
                    'border-radius: 8px;' +
                    'padding: 2rem;' +
                    'text-align: center;' +
                    'box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);' +
                    'min-width: 200px;' +
                '}' +
                '#api-loading-mask .loading-spinner {' +
                    'width: 40px;' +
                    'height: 40px;' +
                    'border: 4px solid rgba(0, 170, 255, 0.3);' +
                    'border-top: 4px solid #00aaff;' +
                    'border-radius: 50%;' +
                    'animation: api-loading-spin 1s linear infinite;' +
                    'margin: 0 auto 1rem;' +
                '}' +
                '#api-loading-mask .loading-text {' +
                    'color: #00ccff;' +
                    'font-size: 1rem;' +
                    'font-weight: 500;' +
                '}' +
                '@keyframes api-loading-spin {' +
                    '0% { transform: rotate(0deg); }' +
                    '100% { transform: rotate(360deg); }' +
                '}';

            if (!document.getElementById('api-loading-mask-style')) {
                style.id = 'api-loading-mask-style';
                document.head.appendChild(style);
            }

            document.body.appendChild(this.element);
        } else {
            // 更新加载文本
            var textElement = this.element.querySelector('.loading-text');
            if (textElement) {
                textElement.textContent = message;
            }
            this.element.style.display = 'flex';
            this.element.style.display = '-ms-flexbox'; // IE10+
        }
    },

    /**
     * 隐藏加载遮罩
     */
    hide: function() {
        this.counter = Math.max(0, this.counter - 1);

        if (this.counter === 0 && this.element) {
            this.element.style.display = 'none';
        }
    },

    /**
     * 强制隐藏加载遮罩
     */
    forceHide: function() {
        this.counter = 0;
        if (this.element) {
            this.element.style.display = 'none';
        }
    }
};

/**
 * 显示消息提示
 *
 * @param {Object} data API返回的数据
 * @param {string} successMessage 成功时显示的消息，如果为null则不显示
 * @param {string} operation 操作类型，用于构建默认错误消息
 * @returns {boolean} 操作是否成功
 */
function showMessage(data, successMessage, operation) {
    successMessage = successMessage || null;
    operation = operation || '操作';
    
    if (!data) return false;

    // 如果有消息，显示消息
    if (data.message) {
        if (data.success) {
            // 成功消息
            alert(data.message);
            return true;
        } else {
            // 错误消息
            alert(data.message || (operation + '失败：未知错误'));
            return false;
        }
    } else if (data.success && successMessage) {
        // 如果成功且提供了成功消息，显示成功消息
        alert(successMessage);
        return true;
    } else if (!data.success) {
        // 如果失败但没有消息，显示默认错误消息
        alert(operation + '失败：未知错误');
        return false;
    }

    return data.success || false;
}

/**
 * 构建查询字符串
 * @param {Object} params 参数对象
 * @returns {string} 查询字符串
 */
function buildQueryString(params) {
    var result = [];
    for (var key in params) {
        if (params.hasOwnProperty(key) && params[key] !== undefined && params[key] !== null) {
            result.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
        }
    }
    return result.join('&');
}

/**
 * 发送XMLHttpRequest请求 (IE兼容)
 * @param {string} method HTTP方法
 * @param {string} url 请求URL
 * @param {Object} requestData 请求数据
 * @param {Object} options 额外选项
 * @param {Object} messageOptions 消息选项
 * @returns {Promise} 请求结果
 */
function sendRequest(method, url, requestData, options, messageOptions) {
    requestData = requestData || {};
    options = options || {};
    messageOptions = messageOptions || {};

    // 处理加载遮罩选项
    var showLoading = messageOptions.showLoading !== false; // 默认显示加载遮罩
    var loadingMessage = messageOptions.loadingMessage || (method === 'GET' ? '加载中...' : 
                        method === 'POST' ? '提交中...' : 
                        method === 'PUT' ? '更新中...' : 
                        method === 'DELETE' ? '删除中...' : '处理中...');

    // 显示加载遮罩
    if (showLoading) {
        LoadingMask.show(loadingMessage);
    }

    return new Promise(function(resolve, reject) {
        var xhr = new XMLHttpRequest();
        var finalUrl = url;
        var body = null;

        // 处理GET和DELETE请求的查询参数
        if ((method === 'GET' || method === 'DELETE') && requestData && Object.keys(requestData).length > 0) {
            var queryString = buildQueryString(requestData);
            if (queryString) {
                finalUrl += (url.indexOf('?') !== -1 ? '&' : '?') + queryString;
            }
        }

        // 处理POST和PUT请求的请求体
        if ((method === 'POST' || method === 'PUT') && requestData) {
            body = JSON.stringify(requestData);
        }

        xhr.open(method, finalUrl, true);

        // 设置默认请求头
        xhr.setRequestHeader('Accept', 'application/json');
        if (body) {
            xhr.setRequestHeader('Content-Type', 'application/json');
        }

        // 设置额外的请求头
        if (options.headers) {
            for (var key in options.headers) {
                if (options.headers.hasOwnProperty(key)) {
                    xhr.setRequestHeader(key, options.headers[key]);
                }
            }
        }

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                // 隐藏加载遮罩
                if (showLoading) {
                    LoadingMask.hide();
                }

                try {
                    var responseData = JSON.parse(xhr.responseText);

                    // 检查是否需要重定向（无论状态码如何）
                    if (responseData.redirect) {
                        window.location.href = responseData.redirect;
                        resolve(null);
                        return;
                    }

                    // 检查是否需要重定向到登录页面
                    if (xhr.status === 401) {
                        window.location.href = '/admin/index.php/public/login.html';
                        resolve(null);
                        return;
                    }

                    // 如果响应不成功，但有数据返回
                    if (xhr.status < 200 || xhr.status >= 300) {
                        console.error('API 请求失败: ' + xhr.status, responseData);

                        // 处理服务器错误（500）的情况，显示详细错误信息
                        if (xhr.status === 500 && responseData.error) {
                            // 构建更友好的错误消息
                            var errorMsg = responseData.error.message || '未知服务器错误';
                            responseData.message = (responseData.message || '服务器错误') + ': ' + errorMsg;

                            // 记录详细错误信息到控制台
                            console.error('错误详情: ' + responseData.error.type + ' 在 ' + responseData.error.file + ' 第 ' + responseData.error.line + ' 行');
                        }

                        // 处理消息提示
                        if (messageOptions) {
                            var operation = messageOptions.operation || (method === 'GET' ? '获取' : 
                                           method === 'POST' ? '提交' : 
                                           method === 'PUT' ? '更新' : 
                                           method === 'DELETE' ? '删除' : '操作');
                            showMessage(responseData, null, operation);
                        }

                        resolve(responseData);
                        return;
                    }

                    // 处理消息提示
                    if (messageOptions) {
                        var showSuccess = messageOptions.showSuccess || false;
                        var successMessage = messageOptions.successMessage || null;
                        var operation = messageOptions.operation || (method === 'GET' ? '获取' : 
                                       method === 'POST' ? '提交' : 
                                       method === 'PUT' ? '更新' : 
                                       method === 'DELETE' ? '删除' : '操作');
                        if (showSuccess || !responseData.success) {
                            showMessage(responseData, successMessage, operation);
                        }
                    }

                    resolve(responseData);
                } catch (error) {
                    console.error('解析响应JSON失败:', error);
                    if (messageOptions) {
                        var operation = messageOptions.operation || '操作';
                        alert(operation + '请求出错，请重试');
                    }
                    reject(error);
                }
            }
        };

        xhr.onerror = function() {
            // 隐藏加载遮罩
            if (showLoading) {
                LoadingMask.hide();
            }
            
            console.error('API 请求出错');
            if (messageOptions) {
                var operation = messageOptions.operation || '操作';
                alert(operation + '请求出错，请重试');
            }
            reject(new Error('Network error'));
        };

        xhr.send(body);
    });
}

/**
 * 发送 GET 请求 (IE兼容版本)
 */
function apiGet(url, requestData, options, messageOptions) {
    return sendRequest('GET', url, requestData, options, messageOptions);
}

/**
 * 发送 POST 请求 (IE兼容版本)
 */
function apiPost(url, requestData, options, messageOptions) {
    return sendRequest('POST', url, requestData, options, messageOptions);
}

/**
 * 发送 PUT 请求 (IE兼容版本)
 */
function apiPut(url, requestData, options, messageOptions) {
    // 设置默认的加载消息
    var putMessageOptions = Object.assign({}, messageOptions);
    if (!putMessageOptions.loadingMessage) {
        putMessageOptions.loadingMessage = '更新中...';
    }
    return sendRequest('PUT', url, requestData, options, putMessageOptions);
}

/**
 * 发送 DELETE 请求 (IE兼容版本)
 */
function apiDelete(url, requestData, options, messageOptions) {
    return sendRequest('DELETE', url, requestData, options, messageOptions);
}

// 全局函数别名，保持向后兼容
window.showLoading = LoadingMask.show.bind(LoadingMask);
window.hideLoading = LoadingMask.hide.bind(LoadingMask);
window.forceHideLoading = LoadingMask.forceHide.bind(LoadingMask);

console.log('IE兼容的API工具函数已加载');
