<?php

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含公共函数库
require_once dirname(dirname(__DIR__)) . DS . 'common.php';


// 定义API根目录常量
if (!defined('__API_ROOT__')) {
    define('__API_ROOT__', dirname(dirname(__DIR__)));
}

// 检查是否已安装，如果未安装则会自动重定向或返回JSON响应
check_installed(true);

// 启动会话
session_start();

// 清除会话数据
$_SESSION = [];

// 删除会话 cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// 销毁会话
session_destroy();

// 设置响应类型为JSON
header('Content-Type: application/json');

// 返回成功响应
json_response(['success' => true, 'message' => '已成功退出登录']);