<?php
/**
 * 娜宝贝软件管理系统 - 管理后台入口
 *
 * 这是系统管理前端的单一入口文件，所有管理页面都通过此文件访问
 * 例如：/index.php/public/login.html 访问登录页面
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 定义管理后台根目录常量
define('__ADMIN_ROOT__', __DIR__);

// 定义根目录常量
define('__ROOT__', dirname(__DIR__));

// 加载管理后台配置
$adminConfig = include __ADMIN_ROOT__ . '/config.php';

// 获取基础路径配置
$basePath = $adminConfig['base_path'] ?? '/admin/index.php/';

// 检查是否有路径信息
$pathInfo = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';

// 如果没有路径信息，默认显示登录页面
if (empty($pathInfo)) {
    // 生成客户端重定向脚本
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="0;url=' . $basePath . 'public/login.html">
    <title>重定向中...</title>
</head>
<body>
    <script>
        window.location.href = "' . $basePath . 'public/login.html";
    </script>
    <p>如果页面没有自动跳转，请点击 <a href="' . $basePath . 'public/login.html">这里</a></p>
</body>
</html>';
    exit;
}

// 解析路径
$path = trim($pathInfo, '/');
$segments = explode('/', $path);

// 确保至少有两个段（区域和页面）
if (count($segments) < 2) {
    // 生成客户端重定向脚本
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="0;url=' . $basePath . 'public/login.html">
    <title>重定向中...</title>
</head>
<body>
    <script>
        window.location.href = "' . $basePath . 'public/login.html";
    </script>
    <p>如果页面没有自动跳转，请点击 <a href="' . $basePath . 'public/login.html">这里</a></p>
</body>
</html>';
    exit;
}

// 获取区域和页面名称
$area = $segments[0];
$page = $segments[1];

// 移除.html扩展名（如果有）
$page = str_replace('.html', '', $page);

// 根据区域和页面加载相应的内容
switch ($area) {
    case 'public':
        // 公共区域（无需登录）
        $filePath = __ADMIN_ROOT__ . '/public/' . $page . '.php';
        if (file_exists($filePath)) {
            require_once $filePath;
        } else {
            // 页面不存在，显示404
            echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>404 - 页面不存在</title>
</head>
<body>
    <h1>404 - 页面不存在</h1>
    <p>请求的页面不存在。</p>
    <p><a href="' . $basePath . 'public/login.html">返回登录页面</a></p>
</body>
</html>';
        }
        break;

    case 'panel':
        // 管理面板区域（需要登录）
        // 注意：登录状态检查将在客户端JavaScript中完成
        $filePath = __ADMIN_ROOT__ . '/panel/' . $page . '.php';
        if (file_exists($filePath)) {
            require_once $filePath;
        } else {
            // 页面不存在，显示404
            echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>404 - 页面不存在</title>
</head>
<body>
    <h1>404 - 页面不存在</h1>
    <p>请求的页面不存在。</p>
    <p><a href="' . $basePath . 'public/login.html">返回登录页面</a></p>
</body>
</html>';
        }
        break;

    default:
        // 未知区域，显示登录页面
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="0;url=' . $basePath . 'public/login.html">
    <title>重定向中...</title>
</head>
<body>
    <script>
        window.location.href = "' . $basePath . 'public/login.html";
    </script>
    <p>如果页面没有自动跳转，请点击 <a href="' . $basePath . 'public/login.html">这里</a></p>
</body>
</html>';
        exit;
}
