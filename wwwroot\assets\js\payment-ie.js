/**
 * IE兼容的支付处理模块
 * 使用ES5语法替代ES6
 */

/**
 * 支付处理器构造函数 (IE兼容版本)
 */
function PaymentHandler() {
    this.checkStatusInterval = null;
    this.maxCheckAttempts = 60; // 最多检查60次（5分钟）
    this.currentCheckAttempts = 0;
    
    console.log('PaymentHandler 初始化完成');
}

/**
 * 获取可用的支付方式
 */
PaymentHandler.prototype.getAvailablePayments = function() {
    if (typeof paymentConfig !== 'undefined' && paymentConfig.enabled) {
        return paymentConfig.available_payments || [];
    }
    return [];
};

/**
 * 检查是否为移动设备
 */
PaymentHandler.prototype.isMobile = function() {
    if (typeof paymentConfig !== 'undefined' && typeof paymentConfig.is_mobile !== 'undefined') {
        return paymentConfig.is_mobile;
    }
    
    // 降级检测
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * 创建订单
 */
PaymentHandler.prototype.createOrder = function(softwareId, paymentType) {
    var self = this;
    
    console.log('创建订单:', softwareId, paymentType);
    
    var requestData = {
        software_id: softwareId,
        payment_type: paymentType
    };
    
    return apiPost('api/public/payment.php', requestData, {}, {
        loadingMessage: '正在创建订单...',
        showLoading: true
    }).then(function(data) {
        if (data && data.success) {
            console.log('订单创建成功:', data);
            
            if (paymentType === 'wechat_pay') {
                self.handleWechatPay(data.data);
            } else if (paymentType === 'alipay') {
                self.handleAlipay(data.data);
            }
            
            return data;
        } else {
            console.error('订单创建失败:', data);
            throw new Error(data.message || '订单创建失败');
        }
    }).catch(function(error) {
        console.error('创建订单出错:', error);
        alert('创建订单失败，请重试');
        throw error;
    });
};

/**
 * 处理微信支付
 */
PaymentHandler.prototype.handleWechatPay = function(orderData) {
    console.log('处理微信支付:', orderData);
    
    if (this.isMobile()) {
        // 移动端：跳转到微信H5支付
        if (orderData.mweb_url) {
            window.location.href = orderData.mweb_url;
        } else {
            alert('获取微信支付链接失败');
        }
    } else {
        // 桌面端：显示二维码
        this.showWechatQR(orderData);
    }
};

/**
 * 处理支付宝支付
 */
PaymentHandler.prototype.handleAlipay = function(orderData) {
    console.log('处理支付宝支付:', orderData);
    
    // 支付宝统一跳转到网页支付
    if (orderData.pay_url) {
        window.location.href = orderData.pay_url;
    } else {
        alert('获取支付宝支付链接失败');
    }
};

/**
 * 显示微信二维码
 */
PaymentHandler.prototype.showWechatQR = function(orderData) {
    var self = this;
    
    if (typeof universalModal !== 'undefined') {
        // 保存软件信息到当前数据中，用于返回按钮
        if (universalModal.currentData) {
            universalModal.currentData.software = universalModal.currentData.software;
        }
        
        universalModal.show('payment-qr', {
            orderData: orderData,
            software: universalModal.currentData ? universalModal.currentData.software : null
        });
    } else {
        console.error('universalModal 未定义');
        alert('弹窗组件未加载，无法显示二维码');
    }
};

/**
 * 开始检查支付状态
 */
PaymentHandler.prototype.startCheckStatus = function(orderNo) {
    var self = this;
    
    console.log('开始检查支付状态:', orderNo);
    
    // 清除之前的检查
    this.stopCheckStatus();
    
    // 重置检查次数
    this.currentCheckAttempts = 0;
    
    // 显示检查状态
    var statusElement = document.getElementById('payment-status');
    if (statusElement) {
        statusElement.style.display = 'block';
    }
    
    // 开始定期检查
    this.checkStatusInterval = setInterval(function() {
        self.checkPaymentStatus(orderNo);
    }, 5000); // 每5秒检查一次
    
    // 立即检查一次
    this.checkPaymentStatus(orderNo);
};

/**
 * 停止检查支付状态
 */
PaymentHandler.prototype.stopCheckStatus = function() {
    if (this.checkStatusInterval) {
        clearInterval(this.checkStatusInterval);
        this.checkStatusInterval = null;
        console.log('已停止支付状态检查');
    }
    
    // 隐藏检查状态
    var statusElement = document.getElementById('payment-status');
    if (statusElement) {
        statusElement.style.display = 'none';
    }
};

/**
 * 检查支付状态
 */
PaymentHandler.prototype.checkPaymentStatus = function(orderNo) {
    var self = this;
    
    this.currentCheckAttempts++;
    console.log('检查支付状态 (第' + this.currentCheckAttempts + '次):', orderNo);
    
    // 检查是否超过最大尝试次数
    if (this.currentCheckAttempts > this.maxCheckAttempts) {
        console.log('支付状态检查超时，停止检查');
        this.stopCheckStatus();
        
        var statusElement = document.getElementById('payment-status');
        if (statusElement) {
            statusElement.innerHTML = '<div style="display: inline-block; padding: 0.5rem 1rem; border-radius: 20px; background: rgba(255, 100, 100, 0.2); color: #ffaaaa; border: 1px solid #ff6666;"><i class="fas fa-exclamation-triangle" style="margin-right: 0.5rem;"></i> 支付检查超时，请手动刷新页面</div>';
        }
        return;
    }
    
    apiGet('api/public/payment.php', { order_no: orderNo }, {}, { showLoading: false })
        .then(function(data) {
            if (data && data.success) {
                var order = data.data;
                console.log('支付状态:', order.status);
                
                if (order.status === 'paid') {
                    // 支付成功
                    console.log('支付成功！');
                    self.stopCheckStatus();
                    self.handlePaymentSuccess(order);
                } else if (order.status === 'failed') {
                    // 支付失败
                    console.log('支付失败');
                    self.stopCheckStatus();
                    self.handlePaymentFailed(order);
                }
                // 如果状态是 pending，继续检查
            } else {
                console.error('检查支付状态失败:', data);
            }
        })
        .catch(function(error) {
            console.error('检查支付状态出错:', error);
        });
};

/**
 * 处理支付成功
 */
PaymentHandler.prototype.handlePaymentSuccess = function(order) {
    console.log('处理支付成功:', order);
    
    // 更新状态显示
    var statusElement = document.getElementById('payment-status');
    if (statusElement) {
        statusElement.innerHTML = '<div style="display: inline-block; padding: 0.5rem 1rem; border-radius: 20px; background: rgba(0, 200, 100, 0.2); color: #00ffaa; border: 1px solid #00cc88;"><i class="fas fa-check-circle" style="margin-right: 0.5rem;"></i> 支付成功！正在跳转...</div>';
    }
    
    // 触发支付成功事件
    var event;
    try {
        event = new CustomEvent('payment:success', { detail: order });
    } catch (e) {
        // IE兼容的事件创建方式
        event = document.createEvent('CustomEvent');
        event.initCustomEvent('payment:success', true, true, order);
    }
    document.dispatchEvent(event);
    
    // 延迟跳转到软件页面
    setTimeout(function() {
        if (typeof universalModal !== 'undefined') {
            universalModal.hide();
        }
        
        // 跳转到软件页面显示下载选项
        var softwareId = order.software_id;
        window.location.hash = '#/software/' + softwareId;
        
        // 如果当前就在首页，直接触发路由检查
        if (window.location.pathname === '/' || window.location.pathname.endsWith('index.php')) {
            setTimeout(function() {
                if (typeof checkUrlRouting === 'function') {
                    checkUrlRouting();
                }
            }, 500);
        }
    }, 1000);
};

/**
 * 处理支付失败
 */
PaymentHandler.prototype.handlePaymentFailed = function(order) {
    console.log('处理支付失败:', order);
    
    // 更新状态显示
    var statusElement = document.getElementById('payment-status');
    if (statusElement) {
        statusElement.innerHTML = '<div style="display: inline-block; padding: 0.5rem 1rem; border-radius: 20px; background: rgba(255, 100, 100, 0.2); color: #ffaaaa; border: 1px solid #ff6666;"><i class="fas fa-times-circle" style="margin-right: 0.5rem;"></i> 支付失败，请重试</div>';
    }
    
    // 3秒后隐藏状态
    setTimeout(function() {
        if (statusElement) {
            statusElement.style.display = 'none';
        }
    }, 3000);
};

// 创建全局实例
var paymentHandler;

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        paymentHandler = new PaymentHandler();
        console.log('PaymentHandler 全局实例已创建');
    });
} else {
    paymentHandler = new PaymentHandler();
    console.log('PaymentHandler 全局实例已创建');
}

console.log('IE兼容的支付处理模块已加载');
