<?php
/**
 * 管理面板 - 控制面板
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 设置页面标题和活动菜单
$pageTitle = '控制面板';
$activeMenu = 'dashboard';

// 渲染头部（需要'dashboard.view'权限）
render_header($pageTitle, $activeMenu, 'dashboard.view');
?>

<!-- 页面内容开始 -->
<div id="dashboardApp">
    <div class="content-card">
        <h2>系统概览</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-blue-900 bg-opacity-30 p-4 rounded-lg border border-blue-700">
                <div class="text-blue-300 text-sm mb-1">软件总数</div>
                <div class="text-2xl text-blue-100">{{ stats.softwareCount }}</div>
            </div>

            <div class="bg-green-900 bg-opacity-30 p-4 rounded-lg border border-green-700">
                <div class="text-green-300 text-sm mb-1">分类总数</div>
                <div class="text-2xl text-green-100">{{ stats.categoryCount }}</div>
            </div>

            <div class="bg-purple-900 bg-opacity-30 p-4 rounded-lg border border-purple-700">
                <div class="text-purple-300 text-sm mb-1">管理员总数</div>
                <div class="text-2xl text-purple-100">{{ stats.adminCount }}</div>
            </div>

            <div class="bg-yellow-900 bg-opacity-30 p-4 rounded-lg border border-yellow-700">
                <div class="text-yellow-300 text-sm mb-1">总下载量</div>
                <div class="text-2xl text-yellow-100">{{ stats.totalDownloads }}</div>
            </div>
        </div>
    </div>

    <div class="content-card">
        <h2>快捷操作</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <a href="<?php echo $basePath; ?>panel/software.html" class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border border-blue-800 hover:bg-blue-800 hover:bg-opacity-30 transition-all flex items-center">
                <i class="fas fa-laptop-code text-2xl text-blue-400 mr-3"></i>
                <div>
                    <div class="text-blue-200 font-medium">软件管理</div>
                    <div class="text-blue-400 text-sm">添加、编辑和删除软件</div>
                </div>
            </a>

            <a href="<?php echo $basePath; ?>panel/category.html" class="bg-green-900 bg-opacity-20 p-4 rounded-lg border border-green-800 hover:bg-green-800 hover:bg-opacity-30 transition-all flex items-center">
                <i class="fas fa-folder text-2xl text-green-400 mr-3"></i>
                <div>
                    <div class="text-green-200 font-medium">分类管理</div>
                    <div class="text-green-400 text-sm">管理软件分类</div>
                </div>
            </a>

            <a href="<?php echo $basePath; ?>panel/settings.html" class="bg-purple-900 bg-opacity-20 p-4 rounded-lg border border-purple-800 hover:bg-purple-800 hover:bg-opacity-30 transition-all flex items-center">
                <i class="fas fa-cog text-2xl text-purple-400 mr-3"></i>
                <div>
                    <div class="text-purple-200 font-medium">站点设置</div>
                    <div class="text-purple-400 text-sm">修改网站标题和其他设置</div>
                </div>
            </a>
        </div>
    </div>

    <div class="content-card">
        <h2>系统信息</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <table class="w-full">
                    <tr>
                        <td class="py-2 text-blue-300">PHP 版本</td>
                        <td class="py-2"><?php echo PHP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <td class="py-2 text-blue-300">服务器软件</td>
                        <td class="py-2"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?></td>
                    </tr>
                    <tr>
                        <td class="py-2 text-blue-300">SQLite 版本</td>
                        <td class="py-2"><?php echo SQLite3::version()['versionString']; ?></td>
                    </tr>
                </table>
            </div>

            <div>
                <table class="w-full">
                    <tr>
                        <td class="py-2 text-blue-300">当前时间</td>
                        <td class="py-2"><?php echo date('Y-m-d H:i:s'); ?></td>
                    </tr>
                    <tr>
                        <td class="py-2 text-blue-300">系统安装时间</td>
                        <td class="py-2" v-if="installedTime">{{ installedTime }}</td>
                        <td class="py-2" v-else>未知</td>
                    </tr>
                    <tr>
                        <td class="py-2 text-blue-300">管理员登录时间</td>
                        <td class="py-2" id="loginTime">加载中...</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- 页面内容结束 -->

<script>
    new Vue({
        el: '#dashboardApp',
        data: {
            loading: true,
            stats: {
                softwareCount: 0,
                categoryCount: 0,
                adminCount: 0,
                totalDownloads: 0
            },
            installedTime: null
        },
        mounted() {
            this.loadDashboardData();
            this.loadSettings();
        },
        methods: {
            async loadDashboardData() {
                // 显示登录时间
                const loginTime = localStorage.getItem('admin_login_time');
                if (loginTime) {
                    const date = new Date(parseInt(loginTime) * 1000);
                    document.getElementById('loginTime').textContent = date.toLocaleString();
                }

                try {
                    const data = await apiGet('<?php echo $apiPath; ?>panel/dashboard.php');

                    if (data && data.success) {
                        this.stats = data.stats;
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }
                    this.loading = false;
                } catch (error) {
                    console.error('加载控制面板数据出错：', error);
                    this.loading = false;
                }
            },
            async loadSettings() {
                try {
                    const data = await apiGet('<?php echo $apiPath; ?>panel/settings.php');

                    if (data && data.success && data.data) {
                        // 如果有安装时间，格式化显示
                        if (data.data.installed_time) {
                            const date = new Date(data.data.installed_time * 1000);
                            this.installedTime = date.toLocaleString();
                        }
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }
                } catch (error) {
                    console.error('加载设置数据出错：', error);
                }
            }
        }
    });
</script>

<?php
// 渲染页脚
render_footer();
?>
