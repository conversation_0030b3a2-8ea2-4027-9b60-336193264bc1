<?php
/**
 * 管理面板公共文件
 * 此文件包含所有管理面板页面共用的功能和模板渲染函数
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 定义根目录常量（如果尚未定义）
if (!defined('__ADMIN_ROOT__')) {
    define('__ADMIN_ROOT__', __DIR__);
}
if (!defined('__ROOT__')) {
    define('__ROOT__', dirname(__DIR__));
}

// 包含公共函数库
require_once __ADMIN_ROOT__ . '/common.php';

// 包含Settings类
require_once __ROOT__ . '/includes/Settings.php';

// 加载管理后台配置
$adminConfig = include __ADMIN_ROOT__ . '/config.php';

// 获取基础路径配置
$basePath = $adminConfig['base_path'] ?? '/admin/index.php/';
$apiPath = $adminConfig['api_path'] ?? '/api/admin/';

// 加载站点设置
$settings = get_settings();
$siteTitle = $settings['basic']['site_title'] ?? '娜宝贝软件';

// 设置默认管理员信息（实际值将由前端JavaScript设置）
$adminUsername = '管理员';

/**
 * 包含页面模板
 *
 * @param string $templateName 模板名称
 * @param array $pageConfig 页面配置
 */
function include_template($templateName, $pageConfig = []) {
    $templatePath = __ADMIN_ROOT__ . '/templates/' . $templateName . '.template.php';

    if (!file_exists($templatePath)) {
        throw new Exception("模板文件不存在: {$templatePath}");
    }

    // 将配置传递给模板
    extract(['pageConfig' => $pageConfig]);

    include $templatePath;
}

/**
 * 渲染权限检查的JavaScript代码
 *
 * @param string|array $requiredPermission 所需的权限，可以是字符串或数组
 * @param bool $anyPermission 是否只需满足其中任一权限，默认为false（需满足所有权限）
 * @return string JavaScript代码
 */
function render_permission_check($requiredPermission, $anyPermission = false) {
    // 将单个权限转换为数组
    if (!is_array($requiredPermission)) {
        $requiredPermission = [$requiredPermission];
    }

    // 生成权限检查的JavaScript代码
    $jsCode = "
    <script>
    // 权限检查函数
    function checkPermission() {
        // 获取存储的权限
        const userPermissions = JSON.parse(localStorage.getItem('admin_permissions') || '[]');

        // 检查是否有all权限
        if (userPermissions.includes('all')) {
            return true;
        }

        // 检查所需权限
        const requiredPermissions = " . json_encode($requiredPermission) . ";
        const anyPermission = " . ($anyPermission ? 'true' : 'false') . ";

        if (anyPermission) {
            // 只需满足其中任一权限
            for (const permission of requiredPermissions) {
                if (userPermissions.includes(permission)) {
                    return true;
                }
            }
            return false;
        } else {
            // 需满足所有权限
            for (const permission of requiredPermissions) {
                if (!userPermissions.includes(permission)) {
                    return false;
                }
            }
            return true;
        }
    }

    // 检查登录状态
    function checkLoginStatus() {
        const adminId = localStorage.getItem('admin_id');
        const adminUsername = localStorage.getItem('admin_username');
        const loginTime = localStorage.getItem('admin_login_time');

        // 检查是否已登录
        if (!adminId || !adminUsername || !loginTime) {
            window.location.href = '" . $GLOBALS['basePath'] . "public/login.html';
            return false;
        }

        // 检查会话是否过期（24小时）
        const sessionTimeout = " . ($GLOBALS['adminConfig']['session_timeout'] ?? 86400) . ";
        const currentTime = Math.floor(Date.now() / 1000);
        if (currentTime - loginTime > sessionTimeout) {
            // 清除本地存储
            localStorage.removeItem('admin_id');
            localStorage.removeItem('admin_username');
            localStorage.removeItem('admin_login_time');
            localStorage.removeItem('admin_permissions');

            // 重定向到登录页面
            window.location.href = '" . $GLOBALS['basePath'] . "public/login.html?expired=1';
            return false;
        }

        return true;
    }

    // 页面加载时检查登录状态和权限
    document.addEventListener('DOMContentLoaded', function() {
        if (!checkLoginStatus()) {
            return;
        }

        // 更新用户名显示
        const usernameElement = document.getElementById('adminUsername');
        if (usernameElement) {
            usernameElement.textContent = localStorage.getItem('admin_username') || '管理员';
        }

        // 检查权限
        if (!checkPermission()) {
            window.location.href = '" . $GLOBALS['basePath'] . "panel/unauthorized.html';
        }
    });
    </script>
    ";

    return $jsCode;
}

/**
 * 渲染页面头部
 *
 * @param string $pageTitle 页面标题
 * @param string $activeMenu 当前活动的菜单项ID
 * @param string|array $requiredPermission 所需的权限，可以是字符串或数组
 * @param bool $anyPermission 是否只需满足其中任一权限，默认为false（需满足所有权限）
 */
function render_header($pageTitle = '管理面板', $activeMenu = 'dashboard', $requiredPermission = null, $anyPermission = false) {
    global $siteTitle, $adminUsername, $adminConfig, $basePath, $apiPath;

    // 生成权限检查代码（如果提供了权限要求）
    $permissionCheck = '';
    if ($requiredPermission !== null) {
        $permissionCheck = render_permission_check($requiredPermission, $anyPermission);
    }
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title><?php echo htmlspecialchars($pageTitle); ?> - <?php echo htmlspecialchars($siteTitle); ?>管理系统</title>
    <script src="<?php echo $adminConfig['assets_path']; ?>libs/tailwindcss/tailwind.js"></script>
    <link href="<?php echo $adminConfig['assets_path']; ?>libs/font-awesome/css/all.min.css" rel="stylesheet">
    <script src="<?php echo $adminConfig['assets_path']; ?>libs/vue/vue.min.js"></script>
    <script src="<?php echo $adminConfig['assets_path']; ?>libs/axios/axios.min.js"></script>
    <!-- 统一样式文件 -->
    <link rel="stylesheet" href="<?php echo $adminConfig['assets_path']; ?>css/admin-common.css">
    <link rel="stylesheet" href="<?php echo $adminConfig['assets_path']; ?>css/admin-components.css">

    <!-- 统一JavaScript工具库 -->
    <script src="<?php echo $adminConfig['assets_path']; ?>js/utils/AdminUtils.js"></script>
    <script src="<?php echo $adminConfig['assets_path']; ?>js/utils/FormValidator.js"></script>
    <script src="<?php echo $adminConfig['assets_path']; ?>js/components/AdminListView.js"></script>

    <script src="<?php echo $adminConfig['assets_path']; ?>js/api.js"></script>
    <script src="<?php echo $adminConfig['assets_path']; ?>js/category-selector.js"></script>

    <!-- 分类树选择组件模板 -->
    <script type="text/x-template" id="category-tree-select-template">
        <div class="category-tree-select">
            <div v-for="category in categories" :key="category.id" class="category-tree-item">
                <div :class="['category-item-content cursor-pointer p-2 hover:bg-gray-700 rounded', {'bg-blue-900': selectedId == category.id}]"
                     @click="$emit('select', category.id)">
                    <div class="flex items-center">
                        <!-- 缩进和展开/折叠图标 -->
                        <div v-if="category.level > 0" :style="{ width: (category.level * 16) + 'px' }" class="flex-shrink-0"></div>
                        <i v-if="category.child_count > 0"
                           class="fas fa-caret-right mr-2 transition-transform duration-200"
                           :class="{ 'transform rotate-90': isExpanded(category.id) }"
                           @click.stop="toggleExpand(category.id)"></i>
                        <span v-else-if="category.level > 0" class="w-4 mr-2"></span>

                        <!-- 分类名称 -->
                        <span>{{ category.name }}</span>
                        <span v-if="category.software_count > 0" class="ml-2 text-xs text-gray-400">({{ category.software_count }})</span>
                    </div>
                </div>

                <!-- 子分类 -->
                <div v-if="category.children && category.children.length > 0 && isExpanded(category.id)" class="pl-4">
                    <category-tree-select
                        :categories="category.children"
                        :selected-id="selectedId"
                        :expanded-categories="expandedCategories"
                        @select="id => $emit('select', id)"
                        @toggle-expand="id => $emit('toggle-expand', id)">
                    </category-tree-select>
                </div>
            </div>
        </div>
    </script>

    <!-- 分类选择器组件模板 -->
    <script type="text/x-template" id="category-selector-template">
        <div class="category-selector relative" :style="{ width }">
            <button
                type="button"
                @click="toggleDropdown"
                :disabled="disabled"
                class="form-input bg-transparent flex items-center justify-between w-full">
                <span>{{ selectedName || placeholder }}</span>
                <i class="fas fa-chevron-down ml-2"></i>
            </button>

            <!-- 树状分类选择器下拉菜单 -->
            <div v-if="showDropdown"
                 class="absolute z-10 mt-1 bg-gray-800 border border-gray-700 rounded-md shadow-lg overflow-y-auto"
                 :style="{ width: dropdownWidth, maxHeight }">
                <div class="p-2">
                    <div v-if="showNoneOption"
                         class="category-tree-item cursor-pointer p-2 hover:bg-gray-700 rounded"
                         @click="selectCategory(null)">
                        <span>{{ noneOptionText }}</span>
                    </div>

                    <category-tree-select
                        :categories="categories"
                        :selected-id="value"
                        :expanded-categories="expandedCategories"
                        @select="selectCategory"
                        @toggle-expand="toggleExpand">
                    </category-tree-select>
                </div>
            </div>
        </div>
    </script>
    <?php echo $permissionCheck; ?>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #0a0a1a;
            color: #e0e0ff;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 100, 255, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(150, 0, 255, 0.1) 0%, transparent 20%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 头部样式 */
        header {
            background: linear-gradient(90deg, #001133, #003366);
            padding: 1rem;
            border-bottom: 2px solid #00aaff;
            box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ffff, transparent);
            animation: scanline 4s linear infinite;
        }

        @keyframes scanline {
            0% {
                top: 0;
            }

            100% {
                top: 100%;
            }
        }

        .header-title h1 {
            font-size: 1.5rem;
            color: #00ffff;
            text-shadow: 0 0 10px #00aaff, 0 0 20px #0066ff;
            letter-spacing: 1px;
        }

        .header-user {
            display: flex;
            align-items: center;
        }

        .user-info {
            margin-right: 1rem;
            color: #aaccff;
        }

        .logout-btn {
            background: rgba(0, 50, 100, 0.5);
            color: #00ccff;
            border: 1px solid #0088cc;
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(0, 70, 130, 0.7);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        /* 主要内容区 */
        .main-container {
            display: flex;
            flex: 1;
        }

        /* 侧边栏 */
        .sidebar {
            width: 260px;
            background: rgba(10, 20, 40, 0.7);
            border-right: 1px solid #334466;
            padding: 0.5rem 0;
            backdrop-filter: blur(5px);
            overflow-y: auto;
            max-height: calc(100vh - 60px);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu-group {
            margin-bottom: 0.5rem;
            border-bottom: 1px solid rgba(51, 68, 102, 0.5);
            padding-bottom: 0.5rem;
        }

        .sidebar-menu-group:last-child {
            border-bottom: none;
        }

        .sidebar-group-title {
            padding: 0.6rem 1.5rem;
            color: #7799cc;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-group-title:hover {
            color: #99bbee;
        }

        .sidebar-group-title i {
            margin-right: 0.5rem;
            transition: transform 0.3s ease;
        }

        .sidebar-group-title.collapsed i {
            transform: rotate(-90deg);
        }

        .sidebar-menu-item {
            margin-bottom: 0.2rem;
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            padding: 0.7rem 1.5rem 0.7rem 2.5rem;
            color: #aaccff;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            justify-content: space-between;
        }

        .sidebar-menu-link:hover {
            background: rgba(0, 100, 200, 0.2);
            color: #00ffff;
        }

        .sidebar-menu-link.active {
            background: rgba(0, 100, 200, 0.3);
            color: #00ffff;
            border-left: 3px solid #00aaff;
        }

        .sidebar-menu-icon {
            margin-right: 0.8rem;
            width: 20px;
            text-align: center;
        }

        .sidebar-menu-content {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .sidebar-menu-badge {
            background: rgba(0, 100, 200, 0.3);
            color: #00ccff;
            border-radius: 10px;
            padding: 0.1rem 0.5rem;
            font-size: 0.7rem;
            margin-left: 0.5rem;
        }

        /* 内容区 */
        .content {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .content-card {
            background: rgba(20, 30, 60, 0.6);
            border-radius: 8px;
            border: 1px solid #334466;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 0 15px rgba(0, 100, 255, 0.2);
        }

        .content-card h2 {
            color: #00ccff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #334477;
            text-shadow: 0 0 5px #00aaff;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #aaccff;
        }

        .form-input {
            width: 100%;
            padding: 0.7rem;
            background: rgba(10, 20, 50, 0.5);
            border: 1px solid #334466;
            border-radius: 4px;
            color: #e0e0ff;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            border-color: #00aaff;
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.3);
            outline: none;
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0066cc, #0044aa);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0077dd, #0055bb);
            box-shadow: 0 0 15px rgba(0, 100, 255, 0.5);
        }

        .btn-danger {
            background: linear-gradient(135deg, #cc0000, #aa0000);
            color: #ffffff;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dd0000, #bb0000);
            box-shadow: 0 0 15px rgba(255, 0, 0, 0.5);
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 0.8rem;
            text-align: left;
            border-bottom: 1px solid #334466;
        }

        .data-table th {
            background: rgba(0, 50, 100, 0.3);
            color: #00ccff;
        }

        .data-table tr:hover {
            background: rgba(0, 50, 100, 0.2);
        }

        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .loading-spinner {
            border: 4px solid rgba(0, 100, 200, 0.3);
            border-top: 4px solid #00aaff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #334466;
            }

            .content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-title">
            <h1><?php echo htmlspecialchars($siteTitle); ?>管理系统</h1>
        </div>
        <div class="header-user">
            <div class="user-info">
                欢迎，<span id="adminUsername"><?php echo htmlspecialchars($adminUsername); ?></span>
            </div>
            <button class="logout-btn" id="logoutBtn">
                <i class="fas fa-sign-out-alt"></i> 退出
            </button>
        </div>
    </header>

    <div class="main-container">
        <div class="sidebar">
            <ul class="sidebar-menu" id="sidebarMenu">
                <!-- 菜单项将通过JavaScript动态加载 -->
                <div class="loading text-center py-4">
                    <div class="loading-spinner"></div>
                    <div class="mt-2 text-sm text-blue-300">加载菜单中...</div>
                </div>
            </ul>
        </div>

        <div class="content">
<?php
}

/**
 * 渲染页面底部
 */
function render_footer() {
    global $basePath, $apiPath;
?>
        </div>
    </div>

    <script>
        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', function() {
            if (confirm('确定要退出登录吗？')) {
                // 发送退出请求
                fetch('<?php echo $apiPath; ?>public/logout.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 清除本地存储
                        localStorage.removeItem('admin_id');
                        localStorage.removeItem('admin_username');
                        localStorage.removeItem('admin_login_time');
                        localStorage.removeItem('admin_permissions');

                        // 重定向到登录页面
                        window.location.href = '<?php echo $basePath; ?>public/login.html';
                    } else {
                        alert('退出失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('退出请求出错：', error);
                    alert('退出请求出错，请重试');
                });
            }
        });

        // 加载菜单
        function loadMenu() {
            fetch('<?php echo $apiPath; ?>panel/menu.php')
                .then(response => {
                    if (response.status === 401) {
                        // 未登录，重定向到登录页面
                        window.location.href = '<?php echo $basePath; ?>public/login.html';
                        return;
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.success && data.data) {
                        // 获取当前活动菜单
                        const currentPath = window.location.pathname;
                        const activeMenuId = currentPath.includes('/panel/') ?
                            currentPath.split('/panel/')[1].replace('.html', '') : 'dashboard';

                        // 获取用户权限
                        const userPermissions = JSON.parse(localStorage.getItem('admin_permissions') || '[]');
                        const hasAllPermissions = userPermissions.includes('all');

                        // 检查权限函数
                        const checkPermission = (permission) => {
                            if (!permission) return true; // 无需权限
                            return hasAllPermissions || userPermissions.includes(permission);
                        };

                        // 生成菜单HTML
                        let menuHtml = '';

                        // 处理菜单组
                        data.data.forEach(group => {
                            if (group.type === 'group') {
                                // 过滤出有权限查看的菜单项
                                const visibleItems = group.items.filter(item => checkPermission(item.permission));

                                // 如果该组没有可见项，则跳过
                                if (visibleItems.length === 0) return;

                                // 检查是否有活动菜单项
                                const hasActiveItem = visibleItems.some(item => item.id === activeMenuId);

                                // 获取组的折叠状态（默认展开有活动项的组）
                                const groupKey = `menu_group_${group.id}`;
                                let isCollapsed = localStorage.getItem(groupKey) === 'collapsed';

                                // 如果有活动项，强制展开
                                if (hasActiveItem) {
                                    isCollapsed = false;
                                }

                                // 添加组标题
                                menuHtml += `
                                    <li class="sidebar-menu-group">
                                        <div class="sidebar-group-title ${isCollapsed ? 'collapsed' : ''}" data-group="${group.id}">
                                            <i class="fas fa-chevron-down"></i>
                                            ${group.title}
                                        </div>
                                        <ul class="sidebar-group-items" style="${isCollapsed ? 'display:none;' : ''}">
                                `;

                                // 添加组内菜单项
                                visibleItems.forEach(item => {
                                    const isActive = activeMenuId === item.id ? 'active' : '';
                                    const badge = item.badge ? `<span class="sidebar-menu-badge">${item.badge}</span>` : '';

                                    // 拼接URL路径，直接与基础路径组合
                                    let url = '<?php echo $basePath; ?>' + item.url;

                                    menuHtml += `
                                        <li class="sidebar-menu-item">
                                            <a href="${url}" class="sidebar-menu-link ${isActive}">
                                                <div class="sidebar-menu-content">
                                                    <span class="sidebar-menu-icon"><i class="${item.icon}"></i></span>
                                                    ${item.title}
                                                </div>
                                                ${badge}
                                            </a>
                                        </li>
                                    `;
                                });

                                // 关闭组
                                menuHtml += `
                                        </ul>
                                    </li>
                                `;
                            } else {
                                // 处理单个菜单项（非组）
                                if (checkPermission(group.permission)) {
                                    const isActive = activeMenuId === group.id ? 'active' : '';
                                    const badge = group.badge ? `<span class="sidebar-menu-badge">${group.badge}</span>` : '';

                                    // 拼接URL路径，直接与基础路径组合
                                    let url = '<?php echo $basePath; ?>' + group.url;

                                    menuHtml += `
                                        <li class="sidebar-menu-item">
                                            <a href="${url}" class="sidebar-menu-link ${isActive}">
                                                <div class="sidebar-menu-content">
                                                    <span class="sidebar-menu-icon"><i class="${group.icon}"></i></span>
                                                    ${group.title}
                                                </div>
                                                ${badge}
                                            </a>
                                        </li>
                                    `;
                                }
                            }
                        });

                        // 更新菜单
                        document.getElementById('sidebarMenu').innerHTML = menuHtml;

                        // 添加组折叠/展开事件
                        document.querySelectorAll('.sidebar-group-title').forEach(title => {
                            title.addEventListener('click', function() {
                                const groupId = this.getAttribute('data-group');
                                const items = this.nextElementSibling;
                                const isCollapsed = this.classList.toggle('collapsed');

                                if (isCollapsed) {
                                    items.style.display = 'none';
                                    localStorage.setItem(`menu_group_${groupId}`, 'collapsed');
                                } else {
                                    items.style.display = '';
                                    localStorage.setItem(`menu_group_${groupId}`, 'expanded');
                                }
                            });
                        });
                    }
                })
                .catch(error => {
                    console.error('加载菜单出错：', error);
                    document.getElementById('sidebarMenu').innerHTML = `
                        <div class="text-center py-4 text-red-400">
                            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                            <div>加载菜单失败</div>
                            <button onclick="loadMenu()" class="btn btn-primary mt-2">重试</button>
                        </div>
                    `;
                });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 加载菜单
            loadMenu();
        });
    </script>
</body>
</html>
<?php
}
