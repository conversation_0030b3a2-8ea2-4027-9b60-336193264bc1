<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>支付结果 - 娜宝贝软件</title>
    <link href="/assets/libs/tailwindcss/tailwind.min.css" rel="stylesheet">
    <link href="/assets/libs/font-awesome/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .payment-result-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .status-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .status-pending {
            color: #f59e0b;
        }

        .status-success {
            color: #10b981;
        }

        .status-failed {
            color: #ef4444;
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .countdown {
            font-size: 1.2rem;
            font-weight: bold;
            color: #3b82f6;
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="payment-result-container w-full max-w-md p-8 text-center text-white">
        <div id="payment-status">
            <!-- 状态内容将通过JavaScript动态更新 -->
            <div class="status-icon status-pending">
                <i class="fas fa-spinner loading-spinner"></i>
            </div>
            <h2 class="text-2xl font-bold mb-4">检查支付状态中...</h2>
            <p class="text-gray-300 mb-6">正在验证您的支付结果，请稍候</p>
        </div>

        <div id="order-info" class="bg-black bg-opacity-30 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold mb-2">订单信息</h3>
            <div id="order-details" class="text-left space-y-2">
                <!-- 订单详情将通过JavaScript动态加载 -->
            </div>
        </div>

        <div id="action-buttons" class="space-y-3">
            <button id="check-status-btn" onclick="checkPaymentStatus()"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>手动检查状态
            </button>
            <a href="/" class="block w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg transition-colors">
                <i class="fas fa-home mr-2"></i>返回首页
            </a>
        </div>
    </div>

    <!-- 引入API工具函数 -->
    <script src="/assets/js/api.js"></script>
    <script src="/assets/libs/md5/md5.min.js"></script>

    <script>
        // 从URL获取订单号
        const urlParams = new URLSearchParams(window.location.search);
        const orderNo = urlParams.get('order_no');

        let orderData = null;
        let checkInterval = null;
        let countdown = 5;

        // 页面加载后立即检查
        document.addEventListener('DOMContentLoaded', function() {
            if (!orderNo) {
                // 如果没有订单号，重定向到首页
                window.location.href = '/';
                return;
            }

            // 检查是否是支付宝回调
            if (isAlipayCallback()) {
                // 如果是支付宝回调，直接验证回调参数
                verifyAlipayCallback();
            } else {
                // 否则正常加载订单信息并检查状态
                loadOrderInfo();
                checkPaymentStatus();
                // 每3秒自动检查一次支付状态
                checkInterval = setInterval(checkPaymentStatus, 3000);
            }
        });

        // 检查是否是支付宝回调
        function isAlipayCallback() {
            return urlParams.has('sign') && urlParams.has('out_trade_no') && urlParams.has('total_amount');
        }

        // 验证支付宝回调
        async function verifyAlipayCallback() {
            try {
                // 构建验证URL，包含所有当前URL参数
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('action', 'verify_alipay_return');

                const data = await apiGet(currentUrl.pathname.replace('/payment_return.php', '/api/public/payment_return.php') + currentUrl.search, {}, {}, {
                    showLoading: false
                });

                if (data && data.success) {
                    orderData = data.data;
                    updateOrderDisplay();
                    updatePageTitle();

                    // 显示支付成功状态
                    updateStatusDisplay('paid');

                    // 开始下载倒计时
                    startDownloadCountdown();
                } else {
                    console.error('支付宝回调验证失败:', data);
                    // 验证失败，显示错误状态
                    updateStatusDisplay('failed');

                    // 显示错误信息
                    const statusElement = document.getElementById('payment-status');
                    statusElement.innerHTML = `
                        <div class="status-icon status-failed">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <h2 class="text-2xl font-bold mb-4">支付验证失败</h2>
                        <p class="text-gray-300 mb-6">${data ? data.message : '支付验证出现错误'}</p>
                    `;
                }
            } catch (error) {
                console.error('支付宝回调验证出错:', error);
                updateStatusDisplay('failed');
            }
        }

        // 加载订单信息
        async function loadOrderInfo() {
            try {
                const data = await apiGet(`/api/public/payment_return.php?action=get_order&order_no=${orderNo}`, {}, {}, {
                    showLoading: false
                });

                if (data && data.success) {
                    orderData = data.data;
                    updateOrderDisplay();
                    updatePageTitle();
                } else {
                    console.error('加载订单信息失败:', data);
                    // 订单不存在，重定向到首页
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('加载订单信息出错:', error);
                window.location.href = '/';
            }
        }

        // 更新订单显示
        function updateOrderDisplay() {
            if (!orderData) return;

            const orderDetails = document.getElementById('order-details');
            orderDetails.innerHTML = `
                <div class="flex justify-between">
                    <span class="text-gray-300">订单号:</span>
                    <span class="font-mono">${orderData.order_no}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-300">软件名称:</span>
                    <span>${orderData.software_name}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-300">支付金额:</span>
                    <span>${orderData.amount} 元</span>
                </div>
            `;
        }

        // 更新页面标题
        function updatePageTitle() {
            if (orderData && orderData.site_title) {
                document.title = `支付结果 - ${orderData.site_title}`;
            }
        }

        // 检查支付状态
        async function checkPaymentStatus() {
            try {
                const data = await apiGet(`/api/public/payment.php?order_no=${orderNo}`, {}, {}, {
                    showLoading: false
                });

                if (data && data.success) {
                    const status = data.data.status;
                    updateStatusDisplay(status);

                    if (status === 'paid') {
                        // 支付成功，停止检查
                        if (checkInterval) {
                            clearInterval(checkInterval);
                            checkInterval = null;
                        }

                        // 开始下载倒计时
                        startDownloadCountdown();
                    }
                } else {
                    console.error('检查支付状态失败:', data);
                }
            } catch (error) {
                console.error('检查支付状态出错:', error);
            }
        }

        // 更新状态显示
        function updateStatusDisplay(status) {
            const statusElement = document.getElementById('payment-status');

            if (status === 'paid') {
                statusElement.innerHTML = `
                    <div class="status-icon status-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h2 class="text-2xl font-bold mb-4">支付成功！</h2>
                    <p class="text-gray-300 mb-6">您的支付已完成，即将跳转到软件页面</p>
                `;
            } else if (status === 'failed') {
                statusElement.innerHTML = `
                    <div class="status-icon status-failed">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <h2 class="text-2xl font-bold mb-4">支付失败</h2>
                    <p class="text-gray-300 mb-6">支付未成功，请重新尝试</p>
                `;
                // 停止检查
                if (checkInterval) {
                    clearInterval(checkInterval);
                    checkInterval = null;
                }
            } else {
                // pending 状态保持检查中的显示
                statusElement.innerHTML = `
                    <div class="status-icon status-pending">
                        <i class="fas fa-spinner loading-spinner"></i>
                    </div>
                    <h2 class="text-2xl font-bold mb-4">检查支付状态中...</h2>
                    <p class="text-gray-300 mb-6">正在验证您的支付结果，请稍候</p>
                `;
            }
        }

        // 开始下载倒计时
        function startDownloadCountdown() {
            const statusElement = document.getElementById('payment-status');

            const updateCountdown = () => {
                statusElement.innerHTML = `
                    <div class="status-icon status-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h2 class="text-2xl font-bold mb-4">支付成功！</h2>
                    <p class="text-gray-300 mb-4">您的支付已完成</p>
                    <p class="countdown">${countdown} 秒后跳转到软件页面</p>
                `;

                countdown--;

                if (countdown < 0) {
                    // 开始下载
                    downloadSoftware();
                } else {
                    setTimeout(updateCountdown, 1000);
                }
            };

            updateCountdown();
        }

        // 跳转到软件页面显示下载选项
        async function downloadSoftware() {
            try {
                if (!orderData) return;

                // 跳转到软件页面显示下载选项弹窗
                const softwareUrl = `/#/software/${orderData.software_id}`;
                window.location.href = softwareUrl;

            } catch (error) {
                console.error('跳转到软件页面出错:', error);
                alert('跳转出错，请手动访问软件页面');
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (checkInterval) {
                clearInterval(checkInterval);
            }
        });
    </script>
</body>
</html>
