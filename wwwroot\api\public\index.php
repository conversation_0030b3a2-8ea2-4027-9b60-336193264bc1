<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含公共API验证文件
require_once dirname(__DIR__) . DS . 'public.php';

// 获取数据库连接
$db = get_db_connection();

// 获取站点设置
$settings = get_settings();

// 记录日志（仅在调试模式下）
if (isset($config['debug']) && $config['debug']) {
    error_log("站点设置: " . json_encode($settings));
}

// 获取分页设置
$defaultPageSize = 10;
$maxPageSize = 100;

if (isset($config['pagination'])) {
    // 获取默认每页显示数量
    if (isset($config['pagination']['default_page_size'])) {
        // 确保将default_page_size转换为整数
        $pageSize = $config['pagination']['default_page_size'];
        if (is_string($pageSize)) {
            $pageSize = trim($pageSize);
        }
        $defaultPageSize = (int)$pageSize;

        // 确保最小值为1
        if ($defaultPageSize < 1) {
            $defaultPageSize = 1;
        }
    }

    // 获取最大每页显示数量
    if (isset($config['pagination']['max_page_size'])) {
        $maxPageSize = (int)$config['pagination']['max_page_size'];

        // 确保最小值为1
        if ($maxPageSize < 1) {
            $maxPageSize = 1;
        }
    }

    if (isset($config['debug']) && $config['debug']) {
        error_log("从配置中获取的默认分页大小: " . $defaultPageSize);
        error_log("从配置中获取的最大分页大小: " . $maxPageSize);
    }
} else if (isset($config['debug']) && $config['debug']) {
    error_log("配置中没有分页设置，使用默认值: 默认=" . $defaultPageSize . ", 最大=" . $maxPageSize);
}

// 获取请求参数
$categoryId = get_param('category', null);
$search = get_param('search', null);
$page = (int)get_param('page', 1);
$requestedPageSize = (int)get_param('pageSize', $defaultPageSize);
$flag = get_param('flag', 'full'); // 'full'=完整数据, 'software'=仅软件列表, 'config'=仅配置信息
$softwareId = get_param('software_id', null); // 单个软件查询

if (isset($config['debug']) && $config['debug']) {
    error_log("原始请求参数: page=$page, pageSize=$requestedPageSize, flag=$flag, software_id=$softwareId");
}

// 如果是单个软件查询，直接返回软件信息
if (!empty($softwareId)) {
    $softwareQuery = "
        SELECT
            s.id, s.name, s.category, s.icon, s.description,
            s.version, s.size, s.downloads, s.fake_downloads, s.price,
            s.video_url, s.download_url_1, s.download_url_2, s.download_url_3,
            s.download_url_4, s.download_url_5, s.buy_url,
            s.sort, s.created_at,
            c.name as category_name
        FROM
            softwares s
        LEFT JOIN
            categories c ON s.category = c.id
        WHERE
            s.id = :software_id
    ";

    $stmt = $db->prepare($softwareQuery);
    $stmt->bindValue(':software_id', $softwareId, SQLITE3_INTEGER);
    $result = $stmt->execute();
    $row = $result->fetchArray(SQLITE3_ASSOC);

    if (!$row) {
        json_response(['success' => false, 'message' => '软件不存在']);
        exit;
    }

    // 处理下载量显示
    if (!empty($row['fake_downloads'])) {
        $row['display_downloads'] = $row['fake_downloads'] . '万';
    } else {
        $row['display_downloads'] = $row['downloads'];
    }

    // 处理价格显示和价格值
    $priceValue = !empty($row['price']) ? floatval($row['price']) : 0;
    $row['price_value'] = $priceValue;

    if ($priceValue > 0) {
        // 格式化价格为两位小数并添加"元"
        $row['price'] = number_format($priceValue, 2, '.', '') . '元';
    } else {
        $row['price'] = '免费';
    }

    // 检查用户是否已购买（通过IP地址）
    $row['has_paid'] = false;
    if ($priceValue > 0) {
        $clientIp = get_client_ip();

        // 检查是否有已支付的订单
        $orderStmt = $db->prepare('
            SELECT * FROM payment_orders
            WHERE software_id = :software_id AND user_ip = :ip AND status = "paid"
        ');
        $orderStmt->bindValue(':software_id', $row['id'], SQLITE3_INTEGER);
        $orderStmt->bindValue(':ip', $clientIp, SQLITE3_TEXT);
        $orderResult = $orderStmt->execute();
        $order = $orderResult->fetchArray(SQLITE3_ASSOC);

        if ($order) {
            $row['has_paid'] = true;
        } else {
            // 检查是否有下载权限
            $permStmt = $db->prepare('
                SELECT * FROM download_permissions
                WHERE software_id = :software_id AND ip = :ip
            ');
            $permStmt->bindValue(':software_id', $row['id'], SQLITE3_INTEGER);
            $permStmt->bindValue(':ip', $clientIp, SQLITE3_TEXT);
            $permResult = $permStmt->execute();
            $permission = $permResult->fetchArray(SQLITE3_ASSOC);

            if ($permission) {
                $row['has_paid'] = true;
            }
        }
    }

    // 添加兼容性字段，保持前端兼容
    $row['download_url'] = $row['download_url_1'];
    $row['backup_download_url'] = $row['download_url_2'];
    $row['baidu_url'] = $row['download_url_3'];

    json_response(['success' => true, 'software' => $row]);
    exit;
}

// 确保页码和每页数量合法
$page = max(1, $page);

// 使用请求的页面大小，但不能超过配置的最大值
$pageSize = min($maxPageSize, max(1, $requestedPageSize));

// 计算偏移量
$offset = ($page - 1) * $pageSize;

if (isset($config['debug']) && $config['debug']) {
    if ($requestedPageSize > $maxPageSize) {
        error_log("请求的页面大小 $requestedPageSize 超过最大限制 $maxPageSize，已调整为最大值");
    }
    error_log("处理后的分页参数: page=$page, pageSize=$pageSize, offset=$offset");
}

// 获取所有分类（树状结构）
$categoryQuery = "SELECT c.*, COUNT(s.id) as software_count,
                 (SELECT COUNT(*) FROM categories WHERE parent_id = c.id) as child_count
                 FROM categories c
                 LEFT JOIN softwares s ON c.id = s.category
                 GROUP BY c.id
                 ORDER BY c.level ASC, c.sort ASC, c.id ASC";

$categoryResult = $db->query($categoryQuery);

$categories = [];
$categoryMap = [];

// 先获取所有分类
while ($row = $categoryResult->fetchArray(SQLITE3_ASSOC)) {
    $row['children'] = []; // 添加children数组用于存放子分类
    $categories[] = $row;
    $categoryMap[$row['id']] = &$categories[count($categories) - 1]; // 使用引用，方便后续添加子分类
}

// 构建树状结构
$tree = [];
foreach ($categories as $cat) {
    if ($cat['parent_id'] == 0) {
        // 顶级分类直接添加到树中
        $tree[] = &$categoryMap[$cat['id']];
    } else {
        // 子分类添加到父分类的children数组中
        if (isset($categoryMap[$cat['parent_id']])) {
            $categoryMap[$cat['parent_id']]['children'][] = &$categoryMap[$cat['id']];
        }
    }
}

// 计算每个分类及其子分类的软件总数
foreach ($categoryMap as &$category) {
    $category['total_software_count'] = calculateTotalSoftwareCount($category);
}

// 递归计算分类及其子分类的软件总数
function calculateTotalSoftwareCount($category) {
    $count = $category['software_count'] ?? 0;

    if (!empty($category['children'])) {
        foreach ($category['children'] as $child) {
            $count += calculateTotalSoftwareCount($child);
        }
    }

    return $count;
}

// 使用树状结构替换原来的平面分类列表
$categories = $tree;

// 构建软件查询条件
$conditions = [];
$params = [];

if (!empty($categoryId)) {
    // 查找选定分类及其所有子分类
    $categoryIds = [$categoryId];

    // 查询所有子分类
    $childQuery = "SELECT id FROM categories WHERE parent_id = $categoryId OR path LIKE '%,$categoryId,%' OR path LIKE '%,$categoryId' OR path = '$categoryId'";
    $childResult = $db->query($childQuery);

    while ($childRow = $childResult->fetchArray(SQLITE3_ASSOC)) {
        $categoryIds[] = $childRow['id'];
    }

    // 构建IN条件
    $placeholders = [];
    foreach ($categoryIds as $index => $id) {
        $placeholder = ":category_$index";
        $placeholders[] = $placeholder;
        $params[$placeholder] = $id;
    }

    $conditions[] = "s.category IN (" . implode(", ", $placeholders) . ")";
}

if (!empty($search)) {
    $conditions[] = "(s.name LIKE :search OR s.description LIKE :search)";
    $params[':search'] = "%{$search}%";
}

$whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// 获取总记录数
$countQuery = "SELECT COUNT(*) as total FROM softwares s $whereClause";
$stmt = $db->prepare($countQuery);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$result = $stmt->execute();
$totalRow = $result->fetchArray(SQLITE3_ASSOC);
$total = $totalRow['total'];

// 获取软件列表
$softwareQuery = "
    SELECT
        s.id, s.name, s.category, s.icon, s.description,
        s.version, s.size, s.downloads, s.fake_downloads, s.price,
        s.video_url, s.download_url_1, s.download_url_2, s.download_url_3,
        s.download_url_4, s.download_url_5, s.buy_url,
        s.sort, s.created_at,
        c.name as category_name
    FROM
        softwares s
    LEFT JOIN
        categories c ON s.category = c.id
    $whereClause
    ORDER BY
        s.sort DESC, s.created_at DESC
    LIMIT :limit OFFSET :offset
";

$stmt = $db->prepare($softwareQuery);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':limit', $pageSize, SQLITE3_INTEGER);
$stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);
$result = $stmt->execute();

$software = [];
while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
    // 处理下载量显示
    if (!empty($row['fake_downloads'])) {
        $row['display_downloads'] = $row['fake_downloads'] . '万';
    } else {
        $row['display_downloads'] = $row['downloads'];
    }

    // 处理价格显示和价格值
    $priceValue = !empty($row['price']) ? floatval($row['price']) : 0;
    $row['price_value'] = $priceValue;

    if ($priceValue > 0) {
        // 格式化价格为两位小数并添加"元"
        $row['price'] = number_format($priceValue, 2, '.', '') . '元';
    } else {
        $row['price'] = '免费';
    }

    // 检查用户是否已购买（通过IP地址）
    $row['has_paid'] = false;
    if ($priceValue > 0) {
        $clientIp = get_client_ip();

        // 检查是否有已支付的订单
        $orderStmt = $db->prepare('
            SELECT * FROM payment_orders
            WHERE software_id = :software_id AND user_ip = :ip AND status = "paid"
        ');
        $orderStmt->bindValue(':software_id', $row['id'], SQLITE3_INTEGER);
        $orderStmt->bindValue(':ip', $clientIp, SQLITE3_TEXT);
        $orderResult = $orderStmt->execute();
        $order = $orderResult->fetchArray(SQLITE3_ASSOC);

        if ($order) {
            $row['has_paid'] = true;
        } else {
            // 检查是否有下载权限
            $permStmt = $db->prepare('
                SELECT * FROM download_permissions
                WHERE software_id = :software_id AND ip = :ip
            ');
            $permStmt->bindValue(':software_id', $row['id'], SQLITE3_INTEGER);
            $permStmt->bindValue(':ip', $clientIp, SQLITE3_TEXT);
            $permResult = $permStmt->execute();
            $permission = $permResult->fetchArray(SQLITE3_ASSOC);

            if ($permission) {
                $row['has_paid'] = true;
            }
        }
    }

    // 添加兼容性字段，保持前端兼容
    $row['download_url'] = $row['download_url_1'];
    $row['backup_download_url'] = $row['download_url_2'];
    $row['baidu_url'] = $row['download_url_3'];

    $software[] = $row;
}

// 如果是配置信息请求，直接返回配置
if ($flag === 'config') {
    // 准备分页配置
    $paginationConfig = [
        'default_page_size' => $defaultPageSize,
        'max_page_size' => $maxPageSize,
        'show_total' => true
    ];

    if (isset($settings['pagination'])) {
        if (isset($settings['pagination']['show_total'])) {
            $paginationConfig['show_total'] = (bool)$settings['pagination']['show_total'];
        }
    }

    if (isset($config['debug']) && $config['debug']) {
        error_log("返回的分页配置: " . json_encode($paginationConfig));
    }

    // 只返回前端需要的配置信息
    $response = [
        'search' => isset($settings['search']) ? $settings['search'] : [
            'mode' => 'click',
            'delay' => 0
        ],
        'pagination' => $paginationConfig
    ];

    json_response($response);
    exit;
}

// 计算总页数
$totalPages = $total > 0 ? ceil($total / $pageSize) : 1;

// 根据flag参数返回不同的数据
$response = [
    'pagination' => [
        'total' => (int)$total,
        'page' => (int)$page,
        'pageSize' => (int)$pageSize,
        'pages' => (int)$totalPages
    ],
    'software' => $software
];

if (isset($config['debug']) && $config['debug']) {
    error_log("分页信息: " . json_encode($response['pagination']));
    error_log("软件数量: " . count($software));
}

// 如果是完整数据请求，添加分类信息和配置信息
if ($flag === 'full') {
    $response['categories'] = $categories;

    // 添加配置信息
    $response['config'] = [
        'search' => isset($settings['search']) ? $settings['search'] : [
            'mode' => 'click',
            'delay' => 0
        ],
        'pagination' => [
            'page_size' => $defaultPageSize,
            'max_page_size' => $maxPageSize,
            'show_total' => isset($settings['pagination']['show_total']) ? (bool)$settings['pagination']['show_total'] : true
        ],
        'debug' => isset($settings['debug']) ? (bool)$settings['debug'] : false
    ];

    if (isset($config['debug']) && $config['debug']) {
        error_log("添加配置信息到响应: " . json_encode($response['config']));
    }
}

// 如果是搜索或分类筛选，添加标志
if (!empty($search) || !empty($categoryId)) {
    $response['is_filtered'] = true;
}

if (isset($config['debug']) && $config['debug']) {
    error_log("最终响应: " . json_encode(array_keys($response)));
}
json_response($response);
