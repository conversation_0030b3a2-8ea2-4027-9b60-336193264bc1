<?php
/**
 * 管理后台API公共函数库
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 引入API公共函数库
require_once dirname(__DIR__) . DS . 'common.php';

/**
 * 检查管理员权限
 *
 * @param string $permission 权限名称
 * @return bool 是否有权限
 */
function check_admin_permission($permission) {
    // 检查系统是否已安装
    check_installed();

    // 检查会话状态
    session_start();

    // 检查是否已登录
    if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
        // 检查请求头中的Accept字段
        $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
        $wantsJson = strpos($acceptHeader, 'application/json') !== false;

        // 如果客户端明确要求JSON响应
        if ($wantsJson) {
            api_error('未登录或会话已过期', null, 401);
        } else {
            // 重定向到登录页面
            header('Location: /admin/index.php/public/login.html');
            exit;
        }
    }

    // 检查是否有指定权限
    if (!isset($_SESSION['admin_permissions']) || !in_array($permission, $_SESSION['admin_permissions'])) {
        api_error('没有权限执行此操作', null, 403);
    }

    return true;
}
