/**
 * 管理后台列表视图组件
 * 提供标准的列表页面结构和功能
 */

const AdminListView = {
    props: {
        // 配置选项
        config: {
            type: Object,
            required: true
        }
    },

    data() {
        return {
            loading: true,
            items: [],
            searchQuery: '',
            filters: {},
            pagination: {
                page: 1,
                pageSize: 10,
                totalItems: 0,
                totalPages: 1
            },
            selectedItems: [],
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            currentItem: null,
            formData: {},
            submitting: false,
            deleting: false,
            searchTimeout: null
        };
    },

    computed: {
        // 是否有选中项
        hasSelectedItems() {
            return this.selectedItems.length > 0;
        },

        // 是否全选
        isAllSelected() {
            return this.items.length > 0 && this.selectedItems.length === this.items.length;
        },

        // 是否部分选中
        isIndeterminate() {
            return this.selectedItems.length > 0 && this.selectedItems.length < this.items.length;
        }
    },

    mounted() {
        this.initializeComponent();
    },

    methods: {
        /**
         * 初始化组件
         */
        initializeComponent() {
            // 从URL参数恢复状态
            this.restoreStateFromUrl();

            // 加载数据
            this.loadItems();

            // 启用实时搜索
            if (this.config.enableSearch !== false) {
                this.enableRealTimeSearch();
            }
        },

        /**
         * 从URL参数恢复状态
         */
        restoreStateFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);

            // 恢复搜索查询
            this.searchQuery = urlParams.get('search') || '';

            // 恢复分页
            this.pagination.page = parseInt(urlParams.get('page')) || 1;
            this.pagination.pageSize = parseInt(urlParams.get('pageSize')) || this.config.defaultPageSize || 10;

            // 恢复筛选条件
            if (this.config.filters) {
                this.config.filters.forEach(filter => {
                    const value = urlParams.get(filter.key);
                    if (value) {
                        this.$set(this.filters, filter.key, value);
                    }
                });
            }
        },

        /**
         * 更新URL参数
         */
        updateUrlParams() {
            const params = new URLSearchParams();

            if (this.searchQuery) {
                params.set('search', this.searchQuery);
            }

            if (this.pagination.page > 1) {
                params.set('page', this.pagination.page);
            }

            if (this.pagination.pageSize !== (this.config.defaultPageSize || 10)) {
                params.set('pageSize', this.pagination.pageSize);
            }

            Object.keys(this.filters).forEach(key => {
                if (this.filters[key]) {
                    params.set(key, this.filters[key]);
                }
            });

            const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
            window.history.replaceState({}, '', newUrl);
        },

        /**
         * 启用实时搜索
         */
        enableRealTimeSearch() {
            this.$watch('searchQuery', () => {
                this.debounceSearch();
            });
        },

        /**
         * 防抖搜索
         */
        debounceSearch() {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.pagination.page = 1;
                this.loadItems();
            }, 300);
        },

        /**
         * 加载数据
         */
        async loadItems() {
            this.loading = true;

            try {
                const params = this.buildRequestParams();
                const url = `${this.config.apiEndpoint}?${params.toString()}`;

                const response = await this.makeApiRequest('GET', url);

                if (response.success) {
                    this.items = response.data || [];
                    this.pagination = { ...this.pagination, ...response.pagination };
                    this.updateUrlParams();
                } else {
                    this.showError('加载数据失败: ' + response.message);
                }
            } catch (error) {
                console.error('加载数据出错:', error);
                this.showError('加载数据失败');
            } finally {
                this.loading = false;
            }
        },

        /**
         * 构建请求参数
         */
        buildRequestParams() {
            const params = new URLSearchParams();

            // 分页参数
            params.set('page', this.pagination.page);
            params.set('pageSize', this.pagination.pageSize);

            // 搜索参数
            if (this.searchQuery) {
                if (this.config.searchFields) {
                    this.config.searchFields.forEach(field => {
                        params.set(field, this.searchQuery);
                    });
                } else {
                    params.set('search', this.searchQuery);
                }
            }

            // 筛选参数
            Object.keys(this.filters).forEach(key => {
                if (this.filters[key]) {
                    params.set(key, this.filters[key]);
                }
            });

            return params;
        },

        /**
         * 发起API请求
         */
        async makeApiRequest(method, url, data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(url, options);
            return await response.json();
        },

        /**
         * 筛选条件变更
         */
        onFilterChange() {
            this.pagination.page = 1;
            this.loadItems();
        },

        /**
         * 切换页码
         */
        changePage(page) {
            if (page < 1 || page > this.pagination.totalPages) {
                return;
            }
            this.pagination.page = page;
            this.loadItems();
        },

        /**
         * 改变每页大小
         */
        changePageSize(pageSize) {
            this.pagination.pageSize = pageSize;
            this.pagination.page = 1;
            this.loadItems();
        },

        /**
         * 刷新数据
         */
        refreshData() {
            this.pagination.page = 1;
            this.loadItems();
        },

        /**
         * 重置筛选条件
         */
        resetFilters() {
            this.searchQuery = '';
            this.filters = {};
            this.pagination.page = 1;
            this.loadItems();
        },

        /**
         * 全选/取消全选
         */
        toggleSelectAll() {
            if (this.isAllSelected) {
                this.selectedItems = [];
            } else {
                this.selectedItems = [...this.items.map(item => item.id)];
            }
        },

        /**
         * 选择/取消选择项目
         */
        toggleSelectItem(itemId) {
            const index = this.selectedItems.indexOf(itemId);
            if (index > -1) {
                this.selectedItems.splice(index, 1);
            } else {
                this.selectedItems.push(itemId);
            }
        },

        /**
         * 显示添加模态框
         */
        showAddForm() {
            this.formData = this.getDefaultFormData();
            this.currentItem = null;
            this.showAddModal = true;
        },

        /**
         * 显示编辑模态框
         */
        showEditForm(item) {
            this.formData = { ...item };
            this.currentItem = item;
            this.showEditModal = true;
        },

        /**
         * 显示删除确认
         */
        confirmDelete(item) {
            this.currentItem = item;
            this.showDeleteModal = true;
        },

        /**
         * 批量删除确认
         */
        confirmBatchDelete() {
            if (!this.hasSelectedItems) return;
            this.showDeleteModal = true;
        },

        /**
         * 获取默认表单数据
         */
        getDefaultFormData() {
            const defaultData = {};
            if (this.config.fields) {
                this.config.fields.forEach(field => {
                    defaultData[field.key] = field.default || '';
                });
            }
            return defaultData;
        },

        /**
         * 提交表单
         */
        async submitForm() {
            if (this.submitting) return;

            this.submitting = true;

            try {
                const isEdit = !!this.currentItem;
                const method = isEdit ? 'PUT' : 'POST';
                const url = isEdit
                    ? `${this.config.apiEndpoint}/${this.currentItem.id}`
                    : this.config.apiEndpoint;

                const response = await this.makeApiRequest(method, url, this.formData);

                if (response.success) {
                    this.showSuccess(isEdit ? '更新成功' : '添加成功');
                    this.closeModals();
                    this.loadItems();
                } else {
                    this.showError(response.message || '操作失败');
                }
            } catch (error) {
                console.error('提交表单出错:', error);
                this.showError('操作失败');
            } finally {
                this.submitting = false;
            }
        },

        /**
         * 删除项目
         */
        async deleteItem() {
            if (this.deleting) return;

            this.deleting = true;

            try {
                if (this.currentItem) {
                    // 单个删除
                    const response = await this.makeApiRequest('DELETE', `${this.config.apiEndpoint}/${this.currentItem.id}`);

                    if (response.success) {
                        this.showSuccess('删除成功');
                        this.closeModals();
                        this.loadItems();
                    } else {
                        this.showError(response.message || '删除失败');
                    }
                } else if (this.hasSelectedItems) {
                    // 批量删除
                    const response = await this.makeApiRequest('DELETE', this.config.apiEndpoint, {
                        ids: this.selectedItems
                    });

                    if (response.success) {
                        this.showSuccess('批量删除成功');
                        this.selectedItems = [];
                        this.closeModals();
                        this.loadItems();
                    } else {
                        this.showError(response.message || '批量删除失败');
                    }
                }
            } catch (error) {
                console.error('删除出错:', error);
                this.showError('删除失败');
            } finally {
                this.deleting = false;
            }
        },

        /**
         * 关闭所有模态框
         */
        closeModals() {
            this.showAddModal = false;
            this.showEditModal = false;
            this.showDeleteModal = false;
            this.currentItem = null;
            this.formData = {};
        },

        /**
         * 检查权限
         */
        checkPermission(permission) {
            return AdminUtils.checkPermission(permission);
        },

        /**
         * 显示成功消息
         */
        showSuccess(message) {
            // 这里可以集成具体的消息提示组件
            alert(message);
        },

        /**
         * 显示错误消息
         */
        showError(message) {
            // 这里可以集成具体的消息提示组件
            alert(message);
        },

        /**
         * 格式化日期时间
         */
        formatDateTime(timestamp) {
            return AdminUtils.formatDateTime(timestamp);
        },

        /**
         * 格式化文件大小
         */
        formatFileSize(bytes) {
            return AdminUtils.formatFileSize(bytes);
        },

        /**
         * 处理操作按钮点击
         */
        handleAction(action, item) {
            // 如果有自定义处理函数，调用父组件的方法
            if (action.handler) {
                this.$parent[action.handler](item);
            } else {
                // 默认操作
                switch (action.key) {
                    case 'edit':
                        this.showEditForm(item);
                        break;
                    case 'delete':
                        this.confirmDelete(item);
                        break;
                    default:
                        console.warn('未知的操作:', action.key);
                }
            }
        }
    },

    template: `
        <div class="admin-list-view">
            <!-- 顶部操作栏 -->
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div v-if="config.enableSearch !== false" class="relative">
                        <input
                            type="text"
                            v-model="searchQuery"
                            :placeholder="config.searchPlaceholder || '搜索...'"
                            class="form-input pr-10"
                        >
                        <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
                    </div>

                    <!-- 筛选器 -->
                    <div v-if="config.filters" class="flex items-center space-x-2">
                        <select
                            v-for="filter in config.filters"
                            :key="filter.key"
                            v-model="filters[filter.key]"
                            @change="onFilterChange"
                            class="form-input"
                            :style="{ width: filter.width || '150px' }"
                        >
                            <option value="">{{ filter.placeholder || '全部' }}</option>
                            <option
                                v-for="option in filter.options"
                                :key="option.value"
                                :value="option.value"
                            >
                                {{ option.label }}
                            </option>
                        </select>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <!-- 批量操作 -->
                    <button
                        v-if="config.enableBatchDelete && hasSelectedItems"
                        @click="confirmBatchDelete"
                        class="btn btn-danger"
                    >
                        <i class="fas fa-trash mr-2"></i> 批量删除
                    </button>

                    <!-- 添加按钮 -->
                    <button
                        v-if="config.enableAdd && checkPermission(config.addPermission)"
                        @click="showAddForm"
                        class="btn btn-primary"
                    >
                        <i class="fas fa-plus mr-2"></i> {{ config.addButtonText || '添加' }}
                    </button>

                    <!-- 刷新按钮 -->
                    <button @click="refreshData" class="btn btn-secondary">
                        <i class="fas fa-sync-alt mr-2"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 数据列表 -->
            <div class="content-card">
                <h2>{{ config.title }}</h2>

                <!-- 加载状态 -->
                <div v-if="loading" class="loading">
                    <div class="loading-spinner"></div>
                </div>

                <!-- 空数据提示 -->
                <div v-else-if="items.length === 0" class="text-center py-8 text-gray-400">
                    <i :class="config.emptyIcon || 'fas fa-inbox'" class="text-4xl mb-3"></i>
                    <p>{{ config.emptyText || '暂无数据' }}</p>
                </div>

                <!-- 数据表格 -->
                <div v-else>
                    <div class="overflow-x-auto">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th v-if="config.enableSelection" class="w-16">
                                        <input
                                            type="checkbox"
                                            :checked="isAllSelected"
                                            :indeterminate="isIndeterminate"
                                            @change="toggleSelectAll"
                                        >
                                    </th>
                                    <th
                                        v-for="column in config.columns"
                                        :key="column.key"
                                        :class="column.class"
                                    >
                                        {{ column.label }}
                                    </th>
                                    <th v-if="config.actions">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in items" :key="item.id">
                                    <td v-if="config.enableSelection">
                                        <input
                                            type="checkbox"
                                            :checked="selectedItems.includes(item.id)"
                                            @change="toggleSelectItem(item.id)"
                                        >
                                    </td>
                                    <td
                                        v-for="column in config.columns"
                                        :key="column.key"
                                    >
                                        <!-- 自定义插槽 -->
                                        <slot :name="'column-' + column.key" :item="item" :value="item[column.key]">
                                            <!-- 默认渲染逻辑 -->
                                            <span v-if="column.type === 'datetime'">{{ formatDateTime(item[column.key]) }}</span>
                                            <span v-else>{{ item[column.key] || '-' }}</span>
                                        </slot>
                                    </td>
                                    <td v-if="config.actions">
                                        <div class="flex space-x-2">
                                            <button
                                                v-for="action in config.actions"
                                                :key="action.key"
                                                v-if="!action.permission || checkPermission(action.permission)"
                                                @click="handleAction(action, item)"
                                                :class="action.class || 'text-blue-400 hover:text-blue-300'"
                                                :title="action.title"
                                            >
                                                <i :class="action.icon"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="flex justify-between items-center mt-4">
                        <div class="text-sm text-gray-400">
                            共 {{ pagination.totalItems }} 条记录，第 {{ pagination.page }}/{{ pagination.totalPages }} 页
                        </div>
                        <div class="flex space-x-2">
                            <button
                                @click="changePage(1)"
                                :disabled="pagination.page === 1"
                                class="btn btn-primary"
                                :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }"
                            >
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button
                                @click="changePage(pagination.page - 1)"
                                :disabled="pagination.page === 1"
                                class="btn btn-primary"
                                :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }"
                            >
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <button
                                @click="changePage(pagination.page + 1)"
                                :disabled="pagination.page === pagination.totalPages"
                                class="btn btn-primary"
                                :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }"
                            >
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button
                                @click="changePage(pagination.totalPages)"
                                :disabled="pagination.page === pagination.totalPages"
                                class="btn btn-primary"
                                :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }"
                            >
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模态框插槽 -->
            <slot name="modals"></slot>
        </div>
    `
};

// 如果在浏览器环境中，将组件挂载到全局对象
if (typeof window !== 'undefined') {
    window.AdminListView = AdminListView;
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminListView;
}
