<?php

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 定义API根目录常量
if (!defined('__API_ROOT__')) {
    define('__API_ROOT__', dirname(dirname(__DIR__)));
}

// 包含公共函数库
require_once __API_ROOT__ . DS . 'common.php';

/**
 * 清除配置文件中的救援密码
 */
function clearRescuePassword() {

    // 加载API配置
    $apiConfig = include __API_ROOT__ . DS . 'config.php';

    // 获取站点设置文件路径
    if (!isset($apiConfig['site_settings_path'])) {
        throw new RuntimeException('no site_settings_path in apiConfig?');
    }
    $siteSettingsFile = $apiConfig['site_settings_path'];

    if (file_exists($siteSettingsFile)) {
        $config = include $siteSettingsFile;

        // 保留用户名，将密码设置为空串
        if (isset($config['admin']) && isset($config['admin']['password'])) {
            $username = $config['admin']['username'] ?? '';
            $config['admin'] = [
                'username' => $username,
                'password' => '' // 设置为空串而不是删除
            ];
        }

        // 写入更新后的配置
        $configContent = "<?php\nreturn " . var_export($config, true) . ";\n";
        file_put_contents($siteSettingsFile, $configContent);
    }
}

// 定义API根目录常量
if (!defined('__API_ROOT__')) {
    define('__API_ROOT__', dirname(dirname(__DIR__)));
}

// 检查是否已安装，如果未安装则会自动重定向或返回JSON响应
check_installed(true);

// 设置响应类型为JSON
header('Content-Type: application/json');

// 检查是否POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => '只允许POST请求'], 405);
}

// 获取JSON数据
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// 验证必填字段
if (empty($data['username']) || empty($data['password'])) {
    json_response(['success' => false, 'message' => '用户名和密码不能为空'], 400);
}

// 安全检查：密码长度至少3位
if (strlen($data['password']) < 3) {
    json_response(['success' => false, 'message' => '密码长度太短，不安全'], 400);
}

// 获取数据库连接
$db = get_db_connection();

// 获取站点设置中的救援账号信息
$settings = get_settings();
// 只要设置了非空密码就视为救援模式
$rescueMode = isset($settings['admin']['password']) && $settings['admin']['password'] !== '';
$rescueUsername = isset($settings['admin']['username']) ? $settings['admin']['username'] : '';
$rescuePassword = isset($settings['admin']['password']) ? $settings['admin']['password'] : '';

// 首先尝试使用救援账号登录
$useRescueAccount = false;
if ($rescueMode && !empty($rescueUsername) && !empty($rescuePassword) &&
    $data['username'] === $rescueUsername && $data['password'] === $rescuePassword) {
    $useRescueAccount = true;
}

// 如果不是使用救援账号，则查询数据库
if (!$useRescueAccount) {
    // 查询用户信息
    $stmt = $db->prepare('SELECT id, username, password, salt FROM admins WHERE username = :username LIMIT 1');
    $stmt->bindValue(':username', $data['username'], SQLITE3_TEXT);
    $result = $stmt->execute();

    $user = $result->fetchArray(SQLITE3_ASSOC);

    // 验证用户存在
    if (!$user) {
        json_response(['success' => false, 'message' => '用户名或密码错误'], 401);
    }

    // 验证密码
    if (!password_verify($data['password'] . $user['salt'], $user['password'])) {
        json_response(['success' => false, 'message' => '用户名或密码错误'], 401);
    }

    // 启动会话
    session_start();

    // 设置会话数据
    $_SESSION['admin_id'] = $user['id'];
    $_SESSION['admin_username'] = $user['username'];
    $_SESSION['admin_login_time'] = time();

    // 更新最后登录时间
    $stmt = $db->prepare('UPDATE admins SET last_login = :last_login WHERE id = :id');
    $stmt->bindValue(':last_login', time(), SQLITE3_INTEGER);
    $stmt->bindValue(':id', $user['id'], SQLITE3_INTEGER);
    $stmt->execute();
} else {
    // 使用救援账号登录

    // 查询是否有同名用户，如果有则使用该用户ID，否则创建新用户
    $stmt = $db->prepare('SELECT id, role_id FROM admins WHERE username = :username LIMIT 1');
    $stmt->bindValue(':username', $rescueUsername, SQLITE3_TEXT);
    $result = $stmt->execute();
    $existingUser = $result->fetchArray(SQLITE3_ASSOC);

    // 启动会话
    session_start();

    // 设置会话数据
    if ($existingUser) {
        $_SESSION['admin_id'] = $existingUser['id'];
    } else {
        // 数据库中不存在该用户，创建新用户
        try {
            // 生成随机盐值
            $salt = bin2hex(random_bytes(16));

            // 生成密码哈希
            $passwordHash = password_hash($rescuePassword . $salt, PASSWORD_DEFAULT);

            // 插入新用户
            $stmt = $db->prepare('INSERT INTO admins (username, password, salt, role_id, created_at) VALUES (:username, :password, :salt, :role_id, :created_at)');
            $stmt->bindValue(':username', $rescueUsername, SQLITE3_TEXT);
            $stmt->bindValue(':password', $passwordHash, SQLITE3_TEXT);
            $stmt->bindValue(':salt', $salt, SQLITE3_TEXT);
            $stmt->bindValue(':role_id', 1, SQLITE3_INTEGER);
            $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
            $stmt->execute();

            // 获取新用户ID
            $_SESSION['admin_id'] = $db->lastInsertRowID();

            // 记录日志 - 可选
            error_log("救援模式：自动创建管理员账户 {$rescueUsername}");
        } catch (Exception $e) {
            // 创建用户失败，使用虚拟ID
            $_SESSION['admin_id'] = 999999;
            error_log("救援模式创建用户失败: " . $e->getMessage());
        }
    }
    $_SESSION['admin_username'] = $rescueUsername;
    $_SESSION['admin_login_time'] = time();
    $_SESSION['admin_rescue_mode'] = true;

    // 清除配置文件中的明文密码
    clearRescuePassword();
}

// 返回成功响应
json_response(['success' => true, 'message' => '登录成功']);