<?php
/**
 * 管理面板 - 站点设置
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 设置页面标题和活动菜单
$pageTitle = '站点设置';
$activeMenu = 'settings';

// 获取后台配置
$adminConfig = include dirname(__DIR__) . '/config.php';

// 渲染头部（需要'admin.view'权限）
render_header($pageTitle, $activeMenu, 'admin.view');

// 引入图片加载工具
echo '<script src="' . $adminConfig['assets_path'] . 'js/image-loader.js"></script>';
?>

<!-- 页面内容开始 -->
<div id="settingsApp">
    <div class="content-card">
        <h2>站点设置</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else>
            <!-- 选项卡导航 -->
            <div class="mb-6 border-b border-gray-700">
                <ul class="flex flex-wrap -mb-px">
                    <li class="mr-2" v-for="(tab, index) in tabs" :key="index">
                        <button
                            @click="activeTab = tab.name"
                            :class="['inline-block py-2 px-4 rounded-t-lg border-b-2',
                                    activeTab === tab.name
                                        ? 'text-blue-300 border-blue-500'
                                        : 'text-gray-400 border-transparent hover:text-gray-300 hover:border-gray-400']">
                            <i :class="tab.icon + ' mr-2'"></i>{{ tab.title }}
                        </button>
                    </li>
                </ul>
            </div>

            <form @submit.prevent="saveSettings">
                <!-- 动态生成选项卡内容 -->
                <div v-for="tab in tabs" :key="tab.name" v-show="activeTab === tab.name" class="grid grid-cols-1 gap-6">
                    <h2 class="text-xl font-bold mb-4 text-blue-300 border-blue-800">{{ tab.title }}</h2>

                    <!-- 动态生成设置组 -->
                    <div v-for="group in tab.groups" :key="group.name" class="bg-gray-800 rounded-lg p-4 mb-4">
                        <h3 class="text-lg mb-4 border-b pb-2 text-blue-300">{{ group.title }}</h3>

                        <!-- 动态生成表单字段 -->
                        <div v-for="field in group.fields" :key="field.name" class="form-group" v-if="field.visible">
                            <label class="form-label">
                                {{ field.label }}
                                <span v-if="field.required" class="text-red-500">*</span>
                            </label>

                            <!-- 文本输入框 -->
                            <div v-if="field.type === 'text'" class="w-full">
                                <div class="flex">
                                    <input type="text"
                                           :name="field.name"
                                           v-model="formData[field.name]"
                                           class="form-input flex-grow"
                                           :placeholder="field.placeholder || ''">

                                    <!-- 上传按钮 -->
                                    <button v-if="field.has_upload"
                                            type="button"
                                            class="ml-2 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                                            @click="openFileUploader(field)">
                                        <i class="fas fa-upload"></i>
                                    </button>
                                </div>

                                <!-- 图片预览 -->
                                <div v-if="field.has_upload && formData[field.name]" class="mt-2 relative">
                                    <div class="preview-container" :data-field="field.name">
                                        <img :src="getImageUrl(formData[field.name])"
                                             class="max-h-32 max-w-full border border-gray-700 rounded"
                                             alt="预览"
                                             @error="handleImageError">
                                    </div>
                                </div>
                            </div>

                            <!-- 文本区域 -->
                            <textarea v-else-if="field.type === 'textarea'"
                                      :name="field.name"
                                      v-model="formData[field.name]"
                                      class="form-input"
                                      rows="4"
                                      :placeholder="field.placeholder || ''"></textarea>

                            <!-- 数字输入框 -->
                            <div v-else-if="field.type === 'number'" class="flex items-center">
                                <input type="number"
                                       :name="field.name"
                                       v-model.number="formData[field.name]"
                                       class="form-input w-32"
                                       :min="field.min"
                                       :max="field.max"
                                       :step="field.step">
                                <span v-if="field.unit" class="ml-2 text-gray-400">{{ field.unit }}</span>
                            </div>

                            <!-- 单选按钮 -->
                            <div v-else-if="field.type === 'radio'" class="flex items-center mt-2">
                                <label v-for="option in field.options" :key="option.value" class="inline-flex items-center mr-4">
                                    <input type="radio"
                                           :name="field.name"
                                           :value="option.value"
                                           v-model="formData[field.name]"
                                           class="form-radio">
                                    <span class="ml-2">{{ option.label }}</span>
                                </label>
                            </div>

                            <!-- 复选框 -->
                            <div v-else-if="field.type === 'checkbox'" class="flex items-center mt-2">
                                <label class="inline-flex items-center">
                                    <input type="checkbox"
                                           :name="field.name"
                                           v-model="formData[field.name]"
                                           class="form-checkbox">
                                    <span class="ml-2">{{ field.checkboxLabel || field.label }}</span>
                                </label>
                            </div>

                            <!-- 下拉选择框 -->
                            <select v-else-if="field.type === 'select'"
                                    :name="field.name"
                                    v-model="formData[field.name]"
                                    class="form-input bg-transparent">
                                <option v-for="option in field.options" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </option>
                            </select>

                            <!-- 密码输入框 -->
                            <input v-else-if="field.type === 'password'"
                                   type="password"
                                   :name="field.name"
                                   v-model="formData[field.name]"
                                   class="form-input"
                                   :placeholder="field.placeholder || ''">

                            <!-- 字段描述 -->
                            <div v-if="field.description" class="text-xs text-gray-400 mt-1">{{ field.description }}</div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end mt-6">
                    <button type="submit" class="btn btn-primary" :disabled="saving">
                        <span v-if="saving"><i class="fas fa-spinner fa-spin mr-2"></i> 保存中...</span>
                        <span v-else><i class="fas fa-save mr-2"></i> 保存设置</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- 页面内容结束 -->

<script>
    new Vue({
        el: '#settingsApp',
        data: function() {
            return {
                loading: true,
                saving: false,
                activeTab: 'website', // 默认选中网站设置选项卡
                tabs: [], // 将通过API获取
                formData: {} // 表单数据，将通过API获取
            };
        },
        mounted() {
            this.loadData();
        },
        watch: {
            // 监听选项卡切换
            activeTab(newTab, oldTab) {
                if (newTab !== oldTab) {
                    // 通知后端选项卡已切换，可能需要加载更多数据
                    this.loadTabData(newTab);
                }
            }
        },
        methods: {
            // 加载所有数据
            async loadData() {
                this.loading = true;

                try {
                    // 加载选项卡列表
                    const tabsData = await apiGet('<?php echo $apiPath; ?>panel/settings_tabs.php');

                    if (tabsData && tabsData.success && tabsData.data) {
                        this.tabs = tabsData.data;

                        // 设置默认选项卡
                        if (this.tabs.length > 0) {
                            this.activeTab = this.tabs[0].name;
                            await this.loadTabData(this.activeTab);
                        }

                        // 加载表单数据
                        await this.loadFormData();
                    } else if (tabsData && tabsData.redirect) {
                        window.location.href = tabsData.redirect;
                    } else {
                        console.error('加载选项卡列表失败：', tabsData);
                        alert('加载选项卡列表失败，请刷新页面重试');
                    }
                } catch (error) {
                    console.error('加载数据出错：', error);
                    alert('加载数据出错：' + error.message);
                } finally {
                    this.loading = false;
                }
            },

            // 加载选项卡数据
            async loadTabData(tabName) {
                try {
                    const data = await apiGet('<?php echo $apiPath; ?>panel/settings_tabs.php?tab=' + tabName);

                    if (data && data.success && data.data) {
                        // 更新选项卡数组中的对应项
                        const index = this.tabs.findIndex(tab => tab.name === tabName);
                        if (index !== -1) {
                            this.$set(this.tabs, index, data.data);
                        }
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }
                } catch (error) {
                    console.error('加载选项卡数据出错：', error);
                }
            },

            // 加载表单数据
            async loadFormData() {
                try {
                    const data = await apiGet('<?php echo $apiPath; ?>panel/settings.php');

                    if (data && data.success && data.data) {
                        this.formData = data.data;
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }
                } catch (error) {
                    console.error('加载表单数据出错：', error);
                }
            },

            // 保存设置
            async saveSettings() {
                this.saving = true;

                try {
                    await apiPost(
                        '<?php echo $apiPath; ?>panel/settings.php',
                        this.formData,
                        {},
                        { showSuccess: true, successMessage: '设置保存成功', operation: '保存' }
                    );
                } catch (error) {
                    console.error('保存设置出错：', error);
                } finally {
                    this.saving = false;
                }
            },

            // 打开文件上传器
            openFileUploader(field) {
                // 创建一个隐藏的文件输入框
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*'; // 只接受图片文件
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);

                // 监听文件选择事件
                fileInput.addEventListener('change', async (event) => {
                    if (fileInput.files && fileInput.files[0]) {
                        // 创建FormData对象
                        const formData = new FormData();
                        formData.append('file', fileInput.files[0]);
                        formData.append('field_name', field.name);

                        // 如果有其他参数，添加到请求中
                        if (field.upload_type) formData.append('type', field.upload_type);
                        if (field.upload_path) formData.append('sub_path', field.upload_path);
                        if (field.upload_filename) formData.append('file_name', field.upload_filename);

                        // 显示上传中提示
                        const uploadingEl = document.createElement('div');
                        uploadingEl.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                        uploadingEl.innerHTML = `
                            <div class="bg-gray-800 rounded-lg p-6 text-center">
                                <div class="loading-spinner mb-4"></div>
                                <p class="text-white">正在上传图片，请稍候...</p>
                            </div>
                        `;
                        document.body.appendChild(uploadingEl);

                        try {
                            // 发送上传请求
                            const response = await fetch('<?php echo $apiPath; ?>panel/settings_upload.php', {
                                method: 'POST',
                                body: formData
                            });

                            // 关闭上传中提示
                            document.body.removeChild(uploadingEl);

                            if (!response.ok) {
                                throw new Error(`HTTP错误: ${response.status}`);
                            }

                            // 处理响应
                            const result = await response.json();

                            if (result.success) {
                                // 上传成功，更新字段值
                                this.formData[field.name] = result.data.file_url;
                                alert('图片上传成功');
                            } else {
                                // 上传失败
                                alert(result.message || '图片上传失败');
                            }
                        } catch (error) {
                            // 关闭上传中提示
                            if (document.body.contains(uploadingEl)) {
                                document.body.removeChild(uploadingEl);
                            }

                            console.error('图片上传出错:', error);
                            alert('图片上传出错: ' + error.message);
                        }
                    }

                    // 从文档中移除文件输入框
                    document.body.removeChild(fileInput);
                });

                // 触发文件选择对话框
                fileInput.click();
            },

            // 获取图片URL（添加时间戳避免缓存）
            getImageUrl(url) {
                if (!url) return '';

                // 处理不同类型的URL
                if (url) {
                    // 1. 处理协议相对URL（以 // 开头）
                    if (url.startsWith('//')) {
                        // 添加当前协议
                        url = window.location.protocol + url;
                    }
                    // 2. 处理相对路径（不以 http://, https:// 或 // 开头）
                    else if (!url.match(/^(https?:)?\/\//)) {
                        // 使用 basic.frontend_url 或当前域名拼接相对路径
                        const frontendUrl = this.formData['basic.frontend_url'] || window.location.origin;
                        url = frontendUrl + (url.startsWith('/') ? url.substring(1) : url);
                    }
                    // 3. 完整URL（以 http:// 或 https:// 开头）保持不变
                }

                const timestamp = Date.now();
                const hasQuery = url.includes('?');
                return url + (hasQuery ? '&' : '?') + '_t=' + timestamp;
            },

            // 处理图片加载错误
            handleImageError(event) {
                const img = event.target;
                const originalSrc = img.src;

                // 如果不是完整URL，尝试添加域名
                if (!originalSrc.startsWith('http')) {
                    // 使用 getImageUrl 函数处理相对路径
                    // 从 originalSrc 中提取出基本路径（去除查询参数）
                    const baseSrc = originalSrc.split('?')[0];
                    img.src = this.getImageUrl(baseSrc);
                }
            }
        }
    });
</script>

<?php
// 渲染页脚
render_footer();
