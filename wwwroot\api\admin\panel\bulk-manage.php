<?php
/**
 * 批量管理API
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'POST':
            // 批量操作
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);

            // 验证必填字段
            if (empty($data['action']) || empty($data['software_ids']) || !is_array($data['software_ids'])) {
                json_response(['success' => false, 'message' => '缺少必要参数'], 400);
            }

            // 获取操作类型
            $action = $data['action'];
            $softwareIds = array_map('intval', $data['software_ids']);
            $params = $data['params'] ?? [];

            // 检查权限
            check_permission('software.edit');

            // 执行批量操作
            switch ($action) {
                case 'change_category':
                    // 批量更改分类
                    if (empty($params['targetCategory'])) {
                        json_response(['success' => false, 'message' => '请选择目标分类'], 400);
                    }

                    $targetCategory = intval($params['targetCategory']);

                    // 检查分类是否存在
                    $stmt = $db->prepare('SELECT id FROM categories WHERE id = :id');
                    $stmt->bindValue(':id', $targetCategory, SQLITE3_INTEGER);
                    $result = $stmt->execute();

                    if (!$result->fetchArray()) {
                        json_response(['success' => false, 'message' => '目标分类不存在'], 400);
                    }

                    // 更新软件分类
                    $placeholders = implode(',', array_fill(0, count($softwareIds), '?'));
                    $stmt = $db->prepare("UPDATE softwares SET category = ? WHERE id IN ($placeholders)");
                    $stmt->bindValue(1, $targetCategory, SQLITE3_INTEGER);

                    foreach ($softwareIds as $index => $id) {
                        $stmt->bindValue($index + 2, $id, SQLITE3_INTEGER);
                    }

                    $stmt->execute();

                    json_response([
                        'success' => true,
                        'message' => '批量更改分类成功',
                        'processed_count' => count($softwareIds)
                    ]);
                    break;

                case 'update_price':
                    // 批量更新价格
                    if (!isset($params['price'])) {
                        json_response(['success' => false, 'message' => '请输入价格'], 400);
                    }

                    $price = $params['price'];

                    // 更新软件价格
                    $placeholders = implode(',', array_fill(0, count($softwareIds), '?'));
                    $stmt = $db->prepare("UPDATE softwares SET price = ? WHERE id IN ($placeholders)");
                    $stmt->bindValue(1, $price, SQLITE3_TEXT);

                    foreach ($softwareIds as $index => $id) {
                        $stmt->bindValue($index + 2, $id, SQLITE3_INTEGER);
                    }

                    $stmt->execute();

                    json_response([
                        'success' => true,
                        'message' => '批量更新价格成功',
                        'processed_count' => count($softwareIds)
                    ]);
                    break;

                case 'update_downloads':
                    // 批量更新下载量
                    if (!isset($params['downloadOperation']) || !isset($params['downloadValue'])) {
                        json_response(['success' => false, 'message' => '请选择操作类型并输入数值'], 400);
                    }

                    $operation = $params['downloadOperation'];
                    $value = intval($params['downloadValue']);

                    if ($value < 0) {
                        json_response(['success' => false, 'message' => '下载量不能为负数'], 400);
                    }

                    // 根据操作类型构建SQL
                    switch ($operation) {
                        case 'set':
                            $sql = "UPDATE softwares SET downloads = ? WHERE id IN ";
                            break;

                        case 'add':
                            $sql = "UPDATE softwares SET downloads = downloads + ? WHERE id IN ";
                            break;

                        case 'subtract':
                            $sql = "UPDATE softwares SET downloads = CASE WHEN downloads > ? THEN downloads - ? ELSE 0 END WHERE id IN ";
                            break;

                        default:
                            json_response(['success' => false, 'message' => '不支持的操作类型'], 400);
                    }

                    // 添加占位符
                    $placeholders = implode(',', array_fill(0, count($softwareIds), '?'));
                    $sql .= "($placeholders)";

                    // 准备语句
                    $stmt = $db->prepare($sql);

                    // 绑定参数
                    if ($operation === 'subtract') {
                        $stmt->bindValue(1, $value, SQLITE3_INTEGER);
                        $stmt->bindValue(2, $value, SQLITE3_INTEGER);
                        $paramOffset = 3;
                    } else {
                        $stmt->bindValue(1, $value, SQLITE3_INTEGER);
                        $paramOffset = 2;
                    }

                    foreach ($softwareIds as $index => $id) {
                        $stmt->bindValue($index + $paramOffset, $id, SQLITE3_INTEGER);
                    }

                    $stmt->execute();

                    json_response([
                        'success' => true,
                        'message' => '批量更新下载量成功',
                        'processed_count' => count($softwareIds)
                    ]);
                    break;

                case 'delete':
                    // 批量删除软件
                    if (empty($params['confirmDelete'])) {
                        json_response(['success' => false, 'message' => '请确认删除操作'], 400);
                    }

                    // 检查权限
                    check_permission('software.delete');

                    // 删除软件
                    $placeholders = implode(',', array_fill(0, count($softwareIds), '?'));
                    $stmt = $db->prepare("DELETE FROM softwares WHERE id IN ($placeholders)");

                    foreach ($softwareIds as $index => $id) {
                        $stmt->bindValue($index + 1, $id, SQLITE3_INTEGER);
                    }

                    $stmt->execute();

                    json_response([
                        'success' => true,
                        'message' => '批量删除软件成功',
                        'processed_count' => count($softwareIds)
                    ]);
                    break;

                default:
                    json_response(['success' => false, 'message' => '不支持的操作类型'], 400);
            }
            break;

        default:
            json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}
