@echo off
echo ========================================
echo Software Management System - Test Environment Cleanup Tool
echo ========================================
echo.
echo This script will delete the following items:
echo  - wwwroot/uploads folder and all its contents
echo  - wwwroot/database folder and all its contents
echo  - wwwroot/site_settings.php file
echo.
echo WARNING: This operation will delete all test data and cannot be undone!
echo.

set /p confirm=Are you sure you want to continue? (Y/N):

if /i "%confirm%" NEQ "Y" (
    echo.
    echo Operation cancelled.
    goto :end
)

echo.
echo Starting cleanup...
echo.

rem Delete uploads folder
if exist wwwroot\uploads (
    echo Deleting uploads folder...
    rmdir /s /q wwwroot\uploads
    if not exist wwwroot\uploads (
        echo - uploads folder deleted successfully
    ) else (
        echo - Failed to delete uploads folder
    )
) else (
    echo - uploads folder does not exist, skipping
)

rem Delete database folder
if exist wwwroot\database (
    echo Deleting database folder...
    rmdir /s /q wwwroot\database
    if not exist wwwroot\database (
        echo - database folder deleted successfully
    ) else (
        echo - Failed to delete database folder
    )
) else (
    echo - database folder does not exist, skipping
)

rem Delete site_settings.php file
if exist wwwroot\site_settings.php (
    echo Deleting site_settings.php file...
    del /f /q wwwroot\site_settings.php
    if not exist wwwroot\site_settings.php (
        echo - site_settings.php file deleted successfully
    ) else (
        echo - Failed to delete site_settings.php file
    )
) else (
    echo - site_settings.php file does not exist, skipping
)
del /f /q wwwroot\site_settings.php.bak
del /f /q wwwroot\site_settings.php.bak.*

echo.
echo Cleanup completed!
echo.
echo You can now reinstall the system and perform testing.
echo.

:end
pause
