<?php
/**
 * 管理面板 - 订单管理
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 设置页面标题和活动菜单
$pageTitle = '订单管理';
$activeMenu = 'orders';

// 渲染头部（需要'orders.view'权限）
render_header($pageTitle, $activeMenu, 'orders.view');
?>

<!-- 页面内容开始 -->
<div id="ordersApp">
    <!-- 顶部操作栏 -->
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center">
            <div class="relative mr-4">
                <input type="text" v-model="searchQuery" @input="debounceSearch" placeholder="搜索订单..." class="form-input pr-10">
                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            </div>

            <div class="flex items-center space-x-4">
                <select v-model="selectedStatus" @change="onStatusChange" class="form-input bg-transparent">
                    <option value="">所有状态</option>
                    <option value="pending">待支付</option>
                    <option value="paid">已支付</option>
                    <option value="failed">支付失败</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 订单列表 -->
    <div class="content-card">
        <h2>订单列表</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else-if="orderList.length === 0" class="text-center py-8 text-gray-400">
            <i class="fas fa-inbox text-4xl mb-3"></i>
            <p>暂无订单数据</p>
        </div>

        <div v-else>
            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="w-16">ID</th>
                            <th>订单号</th>
                            <th>软件名称</th>
                            <th>金额</th>
                            <th>支付方式</th>
                            <th>状态</th>
                            <th>用户IP</th>
                            <th>创建时间</th>
                            <th>支付时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="order in orderList" :key="order.id">
                            <td>{{ order.id }}</td>
                            <td>{{ order.order_no }}</td>
                            <td>{{ order.software_name || '未知软件' }}</td>
                            <td>{{ formatPrice(order.amount) }}</td>
                            <td>{{ formatPaymentType(order.payment_type) }}</td>
                            <td>
                                <span :class="getStatusClass(order.status)">
                                    {{ formatStatus(order.status) }}
                                </span>
                            </td>
                            <td>{{ order.user_ip }}</td>
                            <td>{{ formatTime(order.created_at) }}</td>
                            <td>{{ order.paid_at ? formatTime(order.paid_at) : '-' }}</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button v-if="checkPermission('orders.edit')" @click="editOrder(order)" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button v-if="checkPermission('orders.delete')" @click="confirmDelete(order)" class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-400">
                    共 {{ pagination.totalItems }} 条记录，第 {{ pagination.page }}/{{ pagination.totalPages }} 页
                </div>
                <div class="flex space-x-2">
                    <button @click="changePage(1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button @click="changePage(pagination.page - 1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button @click="changePage(pagination.page + 1)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button @click="changePage(pagination.totalPages)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑订单状态模态框 -->
    <div v-if="showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl text-blue-300 font-semibold">编辑订单状态</h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form @submit.prevent="submitForm">
                    <div class="form-group">
                        <label class="form-label">订单号</label>
                        <input type="text" v-model="formData.order_no" class="form-input" disabled>
                    </div>

                    <div class="form-group">
                        <label class="form-label">软件名称</label>
                        <input type="text" v-model="formData.software_name" class="form-input" disabled>
                    </div>

                    <div class="form-group">
                        <label class="form-label">金额</label>
                        <input type="text" :value="formatPrice(formData.amount)" class="form-input" disabled>
                    </div>

                    <div class="form-group">
                        <label class="form-label">状态 <span class="text-red-500">*</span></label>
                        <select v-model="formData.status" class="form-input">
                            <option value="pending">待支付</option>
                            <option value="paid">已支付</option>
                            <option value="failed">支付失败</option>
                        </select>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" @click="closeModal" class="btn bg-gray-700 text-white">取消</button>
                        <button type="submit" class="btn btn-primary" :disabled="submitting">
                            <span v-if="submitting"><i class="fas fa-spinner fa-spin mr-2"></i> 提交中...</span>
                            <span v-else>保存修改</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
            <div class="p-6">
                <div class="text-center mb-6">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-5xl mb-4"></i>
                    <h3 class="text-xl text-red-300 font-semibold">确认删除</h3>
                    <p class="mt-2 text-gray-300">您确定要删除订单 "{{ orderToDelete.order_no }}" 吗？此操作不可撤销。</p>
                </div>

                <div class="flex justify-center space-x-3">
                    <button @click="showDeleteModal = false" class="btn bg-gray-700 text-white">取消</button>
                    <button @click="deleteOrder" class="btn btn-danger" :disabled="deleting">
                        <span v-if="deleting"><i class="fas fa-spinner fa-spin mr-2"></i> 删除中...</span>
                        <span v-else>确认删除</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 页面内容结束 -->

<script>
    new Vue({
        el: '#ordersApp',
        data: {
            loading: true,
            orderList: [],
            searchQuery: '',
            selectedStatus: '',
            pagination: {
                page: 1,
                pageSize: 10,
                totalItems: 0,
                totalPages: 1
            },
            showEditModal: false,
            showDeleteModal: false,
            formData: {
                id: null,
                order_no: '',
                software_name: '',
                amount: 0,
                status: ''
            },
            orderToDelete: {},
            submitting: false,
            deleting: false,
            searchTimeout: null
        },
        mounted() {
            this.loadOrderList();
        },
        methods: {
            // 检查权限
            checkPermission(permission) {
                const userPermissions = JSON.parse(localStorage.getItem('admin_permissions') || '[]');
                return userPermissions.includes('all') || userPermissions.includes(permission);
            },

            // 加载订单列表
            async loadOrderList() {
                this.loading = true;

                let url = `<?php echo $apiPath; ?>panel/orders.php?page=${this.pagination.page}&pageSize=${this.pagination.pageSize}`;

                if (this.searchQuery) {
                    url += `&search=${encodeURIComponent(this.searchQuery)}`;
                }

                if (this.selectedStatus) {
                    url += `&status=${this.selectedStatus}`;
                }

                try {
                    const data = await apiGet(url);

                    if (data && data.success) {
                        this.orderList = data.data.items || [];
                        this.pagination = data.data.pagination || this.pagination;
                    }

                    this.loading = false;
                } catch (error) {
                    console.error('加载订单数据出错：', error);
                    this.loading = false;
                }
            },

            // 防抖搜索
            debounceSearch() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.pagination.page = 1;
                    this.loadOrderList();
                }, 300);
            },

            // 状态变更处理
            onStatusChange() {
                this.pagination.page = 1;
                this.loadOrderList();
            },

            // 切换页码
            changePage(page) {
                if (page < 1 || page > this.pagination.totalPages) {
                    return;
                }
                this.pagination.page = page;
                this.loadOrderList();
            },

            // 编辑订单
            editOrder(order) {
                this.formData = { ...order };
                this.showEditModal = true;
            },

            // 确认删除
            confirmDelete(order) {
                this.orderToDelete = order;
                this.showDeleteModal = true;
            },

            // 删除订单
            async deleteOrder() {
                this.deleting = true;

                try {
                    const data = await apiDelete(
                        `<?php echo $apiPath; ?>panel/orders.php?id=${this.orderToDelete.id}`,
                        {},
                        { showSuccess: true, successMessage: '订单删除成功', operation: '删除' }
                    );

                    this.deleting = false;
                    this.showDeleteModal = false;

                    if (data && data.success) {
                        // 删除成功，刷新列表
                        this.loadOrderList();
                    }
                } catch (error) {
                    this.deleting = false;
                    this.showDeleteModal = false;
                    console.error('删除订单出错：', error);
                }
            },

            // 提交表单
            async submitForm() {
                this.submitting = true;

                try {
                    const data = await apiPut(
                        `<?php echo $apiPath; ?>panel/orders.php?id=${this.formData.id}`,
                        { status: this.formData.status },
                        {},
                        { showSuccess: true, successMessage: '订单状态更新成功', operation: '更新' }
                    );

                    this.submitting = false;

                    if (data && data.success) {
                        this.closeModal();
                        this.loadOrderList();
                    }
                } catch (error) {
                    this.submitting = false;
                    console.error('更新订单状态出错：', error);
                }
            },

            // 关闭模态框
            closeModal() {
                this.showEditModal = false;
                this.formData = {
                    id: null,
                    order_no: '',
                    software_name: '',
                    amount: 0,
                    status: ''
                };
                this.submitting = false;
            },

            // 格式化价格
            formatPrice(price) {
                return parseFloat(price).toFixed(2) + ' 元';
            },

            // 格式化支付方式
            formatPaymentType(type) {
                const types = {
                    'wechat_pay': '微信支付',
                    'alipay': '支付宝'
                };
                return types[type] || type;
            },

            // 格式化状态
            formatStatus(status) {
                const statuses = {
                    'pending': '待支付',
                    'paid': '已支付',
                    'failed': '支付失败'
                };
                return statuses[status] || status;
            },

            // 获取状态样式类
            getStatusClass(status) {
                const classes = {
                    'pending': 'px-2 py-1 rounded-full bg-yellow-900 text-yellow-300',
                    'paid': 'px-2 py-1 rounded-full bg-green-900 text-green-300',
                    'failed': 'px-2 py-1 rounded-full bg-red-900 text-red-300'
                };
                return classes[status] || '';
            },

            // 格式化时间戳
            formatTime(timestamp) {
                if (!timestamp) return '-';
                const date = new Date(timestamp * 1000);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }
        }
    });
</script>

<?php
// 渲染页脚
render_footer();
?>
