<?php
/**
 * 管理员管理页面配置
 */

return [
    'title' => '管理员管理',
    'apiEndpoint' => '/api/admin/panel/admin.php',
    'activeMenu' => 'admin',
    'permissions' => ['admin.view'],
    'appId' => 'adminApp',
    
    // 功能开关
    'enableSearch' => true,
    'enableAdd' => true,
    'enableEdit' => true,
    'enableDelete' => true,
    'enableBatchDelete' => false,
    'enableSelection' => false,
    
    // 搜索配置
    'searchPlaceholder' => '搜索管理员...',
    'searchFields' => ['username'],
    
    // 空数据配置
    'emptyText' => '暂无管理员数据',
    'emptyIcon' => 'fas fa-users-slash',
    
    // 添加按钮配置
    'addButtonText' => '添加管理员',
    'addPermission' => 'admin.add',
    
    // 分页配置
    'defaultPageSize' => 10,
    
    // 表格列配置
    'columns' => [
        [
            'key' => 'id',
            'label' => 'ID',
            'type' => 'number',
            'class' => 'w-16'
        ],
        [
            'key' => 'username',
            'label' => '用户名',
            'type' => 'text'
        ],
        [
            'key' => 'role_name',
            'label' => '角色',
            'type' => 'custom'
        ],
        [
            'key' => 'last_login',
            'label' => '上次登录',
            'type' => 'custom'
        ],
        [
            'key' => 'created_at',
            'label' => '创建时间',
            'type' => 'datetime'
        ]
    ],
    
    // 操作按钮配置
    'actions' => [
        [
            'key' => 'edit',
            'icon' => 'fas fa-edit',
            'title' => '编辑管理员',
            'class' => 'text-blue-400 hover:text-blue-300',
            'permission' => 'admin.edit',
            'handler' => 'editAdmin'
        ],
        [
            'key' => 'delete',
            'icon' => 'fas fa-trash-alt',
            'title' => '删除管理员',
            'class' => 'text-red-400 hover:text-red-300',
            'permission' => 'admin.delete',
            'condition' => 'item.id !== currentAdminId',
            'handler' => 'confirmDelete'
        ]
    ],
    
    // 表单字段配置
    'fields' => [
        [
            'key' => 'username',
            'label' => '用户名',
            'type' => 'text',
            'required' => true,
            'placeholder' => '请输入用户名',
            'editDisabled' => true,  // 编辑时禁用
            'help' => '用户名一旦创建不可修改'
        ],
        [
            'key' => 'password',
            'label' => '密码',
            'type' => 'password',
            'required' => true,
            'placeholder' => '请输入密码',
            'editLabel' => '新密码（留空不修改）',
            'editRequired' => false,
            'help' => '密码长度至少6位'
        ],
        [
            'key' => 'role_id',
            'label' => '角色',
            'type' => 'select',
            'required' => false,
            'placeholder' => '选择角色',
            'noneOptionText' => '无角色',
            'options' => 'roles',  // 动态加载角色列表
            'help' => '选择管理员的角色权限'
        ]
    ],
    
    // 删除确认配置
    'deleteConfirmation' => [
        'title' => '确认删除',
        'message' => '您确定要删除管理员 "{username}" 吗？此操作不可撤销。',
        'restrictions' => [
            [
                'condition' => 'id === currentAdminId',
                'message' => '不能删除当前登录的管理员账号。'
            ]
        ]
    ],
    
    // 添加/编辑模态框配置
    'modal' => [
        'addTitle' => '添加管理员',
        'editTitle' => '编辑管理员',
        'addSubmitText' => '添加管理员',
        'editSubmitText' => '保存修改'
    ],
    
    // 特殊配置
    'specialFeatures' => [
        'loadRoles' => true,           // 需要加载角色列表
        'preventSelfDelete' => true,   // 防止删除自己
        'passwordOptional' => true     // 编辑时密码可选
    ]
];
