<?php
/**
 * 管理面板 - 软件管理
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 设置页面标题和活动菜单
$pageTitle = '软件管理';
$activeMenu = 'software';

// 渲染头部（需要'software.view'权限）
render_header($pageTitle, $activeMenu, 'software.view');
?>

<!-- 页面内容开始 -->
<div id="softwareApp">
    <!-- 顶部操作栏 -->
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center">
            <div class="relative mr-4">
                <input type="text" v-model="searchQuery" @input="debounceSearch" placeholder="搜索软件..." class="form-input pr-10">
                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            </div>

            <div class="flex items-center space-x-4">
                <category-selector
                    v-model="selectedCategory"
                    :categories="categories"
                    placeholder="所有分类"
                    noneOptionText="所有分类"
                    width="200px"
                    @input="onCategoryChange">
                </category-selector>

                <div v-if="selectedCategory" class="flex items-center">
                    <input type="checkbox" id="include-subcategories" v-model="includeSubcategories" @change="onCategoryChange" class="form-checkbox h-4 w-4 text-blue-600">
                    <label for="include-subcategories" class="ml-2 text-sm text-gray-300">包含子分类</label>
                </div>
            </div>
        </div>

        <div>
            <button v-if="checkPermission('software.add')" @click="showAddModal = true" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i> 添加软件
            </button>
        </div>
    </div>

    <!-- 软件列表 -->
    <div class="content-card">
        <h2>软件列表</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else-if="softwareList.length === 0" class="text-center py-8 text-gray-400">
            <i class="fas fa-inbox text-4xl mb-3"></i>
            <p>暂无软件数据</p>
        </div>

        <div v-else>
            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="w-16">ID</th>
                            <th class="w-16">图标</th>
                            <th>软件名称</th>
                            <th>分类</th>
                            <th>版本</th>
                            <th>大小</th>
                            <th>下载量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="software in softwareList" :key="software.id">
                            <td>{{ software.id }}</td>
                            <td>
                                <img v-if="software.icon" :src="software.icon" alt="图标" class="w-10 h-10 rounded">
                                <div v-else class="w-10 h-10 bg-blue-900 rounded flex items-center justify-center">
                                    <i class="fas fa-laptop-code text-blue-300"></i>
                                </div>
                            </td>
                            <td>{{ software.name }}</td>
                            <td>{{ software.category_name || '未分类' }}</td>
                            <td>{{ software.version || '未知' }}</td>
                            <td>{{ software.size || '未知' }}</td>
                            <td>{{ software.downloads }} <span v-if="software.fake_downloads" class="text-xs text-gray-400">({{ software.fake_downloads }}万)</span></td>
                            <td>
                                <div class="flex space-x-2">
                                    <button v-if="checkPermission('software.edit')" @click="editSoftware(software)" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button v-if="checkPermission('software.delete')" @click="confirmDelete(software)" class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-400">
                    共 {{ pagination.totalItems }} 条记录，第 {{ pagination.page }}/{{ pagination.totalPages }} 页
                </div>
                <div class="flex space-x-2">
                    <button @click="changePage(1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button @click="changePage(pagination.page - 1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button @click="changePage(pagination.page + 1)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button @click="changePage(pagination.totalPages)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑软件模态框 -->
    <div v-if="showAddModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-3xl max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl text-blue-300 font-semibold">{{ showEditModal ? '编辑软件' : '添加软件' }}</h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form @submit.prevent="submitForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label class="form-label">软件名称 <span class="text-red-500">*</span></label>
                            <input type="text" v-model="formData.name" class="form-input" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">分类</label>
                            <category-selector
                                v-model="formData.category"
                                :categories="categories"
                                placeholder="选择分类"
                                noneOptionText="无分类"
                                width="100%">
                            </category-selector>
                        </div>

                        <div class="form-group">
                            <label class="form-label">版本</label>
                            <input type="text" v-model="formData.version" class="form-input">
                        </div>

                        <div class="form-group">
                            <label class="form-label">大小</label>
                            <input type="text" v-model="formData.size" class="form-input">
                        </div>

                        <div class="form-group">
                            <label class="form-label">价格</label>
                            <input type="text" v-model="formData.price" class="form-input">
                        </div>

                        <div class="form-group">
                            <label class="form-label">虚拟下载量（万）</label>
                            <input type="text" v-model="formData.fake_downloads" class="form-input">
                        </div>

                        <div class="form-group">
                            <label class="form-label">排序</label>
                            <input type="number" v-model="formData.sort" class="form-input">
                        </div>

                        <div class="form-group">
                            <label class="form-label">实际下载量</label>
                            <input type="number" v-model="formData.downloads" class="form-input" :disabled="!showEditModal">
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">软件图标</label>
                            <div class="flex items-center space-x-2">
                                <input type="text" v-model="formData.icon" class="form-input flex-grow" placeholder="图标URL">
                                <button type="button" @click="openIconUploader" class="btn btn-secondary">
                                    <i class="fas fa-upload mr-1"></i> 上传
                                </button>
                            </div>
                            <div v-if="formData.icon" class="mt-2">
                                <img :src="formData.icon" alt="图标预览" class="h-12 w-12 object-contain border border-gray-700 rounded">
                            </div>
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">下载URL 1（本地下载）</label>
                            <div class="flex items-center space-x-2">
                                <input type="text" v-model="formData.download_url_1" class="form-input flex-grow" placeholder="本地软件下载链接">
                                <button type="button" @click="openSoftwareUploader" class="btn btn-secondary">
                                    <i class="fas fa-upload mr-1"></i> 上传
                                </button>
                            </div>
                            <div class="text-xs text-gray-400 mt-1">可以是外部链接或上传本地文件</div>
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">下载URL 2（备用下载）</label>
                            <input type="text" v-model="formData.download_url_2" class="form-input" placeholder="备用下载链接">
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">下载URL 3（百度网盘）</label>
                            <input type="text" v-model="formData.download_url_3" class="form-input" placeholder="百度网盘链接">
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">下载URL 4（预留扩展）</label>
                            <input type="text" v-model="formData.download_url_4" class="form-input" placeholder="预留下载链接">
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">下载URL 5（预留扩展）</label>
                            <input type="text" v-model="formData.download_url_5" class="form-input" placeholder="预留下载链接">
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">购买链接</label>
                            <input type="text" v-model="formData.buy_url" class="form-input">
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">视频URL</label>
                            <input type="text" v-model="formData.video_url" class="form-input">
                        </div>

                        <div class="form-group md:col-span-2">
                            <label class="form-label">软件描述</label>
                            <textarea v-model="formData.description" class="form-input" rows="4"></textarea>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" @click="closeModal" class="btn bg-gray-700 text-white">取消</button>
                        <button type="submit" class="btn btn-primary" :disabled="submitting">
                            <span v-if="submitting"><i class="fas fa-spinner fa-spin mr-2"></i> 提交中...</span>
                            <span v-else>{{ showEditModal ? '保存修改' : '添加软件' }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
            <div class="p-6">
                <div class="text-center mb-6">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-5xl mb-4"></i>
                    <h3 class="text-xl text-red-300 font-semibold">确认删除</h3>
                    <p class="mt-2 text-gray-300">您确定要删除软件 "{{ softwareToDelete.name }}" 吗？此操作不可撤销。</p>
                </div>

                <div class="flex justify-center space-x-3">
                    <button @click="showDeleteModal = false" class="btn bg-gray-700 text-white">取消</button>
                    <button @click="deleteSoftware" class="btn btn-danger" :disabled="deleting">
                        <span v-if="deleting"><i class="fas fa-spinner fa-spin mr-2"></i> 删除中...</span>
                        <span v-else>确认删除</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 页面内容结束 -->

<!-- 树状分类选择组件模板 -->
<script type="text/x-template" id="category-tree-select-template">
    <div class="category-tree-select">
        <div v-for="category in categories" :key="category.id" class="category-tree-item">
            <div :class="['category-item-content cursor-pointer p-2 hover:bg-gray-700 rounded', {'bg-blue-900': selectedId == category.id}]"
                 @click="$emit('select', category.id)">
                <div class="flex items-center">
                    <!-- 缩进和展开/折叠图标 -->
                    <div v-if="category.level > 0" :style="{ width: (category.level * 16) + 'px' }" class="flex-shrink-0"></div>
                    <i v-if="category.child_count > 0"
                       class="fas fa-caret-right mr-2 transition-transform duration-200"
                       :class="{ 'transform rotate-90': isExpanded(category.id) }"
                       @click.stop="toggleExpand(category.id)"></i>
                    <span v-else-if="category.level > 0" class="w-4 mr-2"></span>

                    <!-- 分类名称 -->
                    <span>{{ category.name }}</span>
                    <span v-if="category.software_count > 0" class="ml-2 text-xs text-gray-400">({{ category.software_count }})</span>
                </div>
            </div>

            <!-- 子分类 -->
            <div v-if="category.children && category.children.length > 0 && isExpanded(category.id)" class="pl-4">
                <category-tree-select
                    :categories="category.children"
                    :selected-id="selectedId"
                    :expanded-categories="expandedCategories"
                    @select="id => $emit('select', id)"
                    @toggle-expand="id => toggleExpand(id)">
                </category-tree-select>
            </div>
        </div>
    </div>
</script>

<script>
    // 注册树状分类选择组件
    Vue.component('category-tree-select', {
        template: '#category-tree-select-template',
        props: {
            categories: {
                type: Array,
                required: true
            },
            selectedId: {
                type: [Number, String, null],
                default: null
            },
            expandedCategories: {
                type: Array,
                default: () => []
            }
        },
        methods: {
            isExpanded(categoryId) {
                return this.expandedCategories.includes(categoryId);
            },
            toggleExpand(categoryId) {
                this.$emit('toggle-expand', categoryId);
            }
        }
    });

    new Vue({
        el: '#softwareApp',
        data: {
            loading: true,
            softwareList: [],
            categories: [],
            searchQuery: '',
            selectedCategory: '',
            includeSubcategories: true,
            // 移除不再需要的变量
            expandedCategories: [],
            pagination: {
                page: 1,
                pageSize: 10,
                totalItems: 0,
                totalPages: 1
            },
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            formData: {
                id: null,
                name: '',
                category: '',
                icon: '',
                description: '',
                version: '',
                size: '',
                downloads: 0,
                fake_downloads: '',
                price: '',
                video_url: '',
                download_url_1: '',
                download_url_2: '',
                download_url_3: '',
                download_url_4: '',
                download_url_5: '',
                buy_url: '',
                sort: 0
            },
            softwareToDelete: {},
            submitting: false,
            deleting: false,
            searchTimeout: null
        },
        mounted() {
            this.loadCategories();
            this.loadSoftwareList();

            // 添加点击事件监听器，点击页面其他地方关闭分类选择器
            document.addEventListener('click', this.handleClickOutside);
        },
        methods: {
            // 检查权限
            checkPermission(permission) {
                const userPermissions = JSON.parse(localStorage.getItem('admin_permissions') || '[]');
                return userPermissions.includes('all') || userPermissions.includes(permission);
            },

            // 加载分类列表（树状结构）
            async loadCategories() {
                try {
                    const data = await apiGet('<?php echo $apiPath; ?>panel/category.php');

                    if (data && data.success) {
                        this.categories = data.data.items || [];

                        // 如果有选中的分类，更新分类名称
                        if (this.selectedCategory) {
                            this.updateSelectedCategoryName();
                        }

                        // 默认展开所有顶级分类
                        this.categories.forEach(category => {
                            if (category.level === 0 && category.child_count > 0) {
                                this.expandedCategories.push(category.id);
                            }
                        });
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }
                } catch (error) {
                    console.error('加载分类数据出错：', error);
                }
            },

            // 移除不再需要的方法

            // 分类变更处理
            onCategoryChange(categoryId) {
                this.loadSoftwareList();
            },

            // 移除不再需要的方法

            // 切换分类展开/折叠状态
            toggleExpand(categoryId) {
                const index = this.expandedCategories.indexOf(categoryId);
                if (index === -1) {
                    this.expandedCategories.push(categoryId);
                } else {
                    this.expandedCategories.splice(index, 1);
                }
            },

            // 加载软件列表
            async loadSoftwareList() {
                this.loading = true;

                let url = `<?php echo $apiPath; ?>panel/software.php?page=${this.pagination.page}&pageSize=${this.pagination.pageSize}`;

                if (this.searchQuery) {
                    url += `&search=${encodeURIComponent(this.searchQuery)}`;
                }

                if (this.selectedCategory) {
                    url += `&category=${this.selectedCategory}`;

                    // 如果选中了"包含子分类"选项，添加参数
                    if (this.includeSubcategories) {
                        url += `&include_subcategories=1`;
                    }
                }

                try {
                    const data = await apiGet(url);

                    if (data && data.success) {
                        this.softwareList = data.data.items || [];
                        this.pagination = data.data.pagination || this.pagination;
                    }

                    this.loading = false;
                } catch (error) {
                    console.error('加载软件数据出错：', error);
                    this.loading = false;
                }
            },

            // 重新加载当前编辑的软件数据
            async reloadCurrentSoftware() {
                if (!this.formData.id) {
                    return;
                }

                try {
                    const data = await apiGet(`<?php echo $apiPath; ?>panel/software.php?id=${this.formData.id}`);

                    if (data && data.success && data.data) {
                        // 更新表单数据，保持当前的编辑状态
                        const updatedSoftware = data.data;
                        Object.keys(updatedSoftware).forEach(key => {
                            if (updatedSoftware[key] !== null && updatedSoftware[key] !== undefined) {
                                this.formData[key] = updatedSoftware[key];
                            }
                        });

                        // 同时更新软件列表中的对应项
                        const index = this.softwareList.findIndex(s => s.id == this.formData.id);
                        if (index !== -1) {
                            this.softwareList[index] = { ...updatedSoftware };
                        }
                    }
                } catch (error) {
                    console.error('重新加载软件数据出错：', error);
                }
            },

            // 防抖搜索
            debounceSearch() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.pagination.page = 1;
                    this.loadSoftwareList();
                }, 300);
            },

            // 切换页码
            changePage(page) {
                if (page < 1 || page > this.pagination.totalPages) {
                    return;
                }
                this.pagination.page = page;
                this.loadSoftwareList();
            },

            // 编辑软件
            editSoftware(software) {
                this.formData = { ...software };
                this.showEditModal = true;
            },

            // 确认删除
            confirmDelete(software) {
                this.softwareToDelete = software;
                this.showDeleteModal = true;
            },

            // 删除软件
            async deleteSoftware() {
                this.deleting = true;

                try {
                    const data = await apiDelete(
                        `<?php echo $apiPath; ?>panel/software.php?id=${this.softwareToDelete.id}`,
                        {},
                        { showSuccess: true, successMessage: '软件删除成功', operation: '删除' }
                    );

                    this.deleting = false;
                    this.showDeleteModal = false;

                    if (data && data.success) {
                        // 删除成功，刷新列表
                        this.loadSoftwareList();
                    }
                } catch (error) {
                    this.deleting = false;
                    this.showDeleteModal = false;
                    console.error('删除软件出错：', error);
                }
            },

            // 提交表单
            async submitForm() {
                // 验证表单
                if (!this.formData.name) {
                    alert('软件名称不能为空');
                    return;
                }

                this.submitting = true;

                try {
                    let data;
                    const operation = this.showEditModal ? '更新' : '添加';
                    const successMessage = this.showEditModal ? '软件更新成功' : '软件添加成功';

                    if (this.showEditModal) {
                        // 编辑模式
                        data = await apiPut(
                            `<?php echo $apiPath; ?>panel/software.php?id=${this.formData.id}`,
                            this.formData,
                            {},
                            { showSuccess: true, successMessage, operation }
                        );
                    } else {
                        // 添加模式
                        data = await apiPost(
                            '<?php echo $apiPath; ?>panel/software.php',
                            this.formData,
                            {},
                            { showSuccess: true, successMessage, operation }
                        );
                    }

                    this.submitting = false;

                    if (data && data.success) {
                        this.closeModal();
                        this.loadSoftwareList();
                    }
                } catch (error) {
                    this.submitting = false;
                    console.error((this.showEditModal ? '更新' : '添加') + '软件出错：', error);
                }
            },

            // 关闭模态框
            closeModal() {
                this.showAddModal = false;
                this.showEditModal = false;
                this.formData = {
                    id: null,
                    name: '',
                    category: '',
                    icon: '',
                    description: '',
                    version: '',
                    size: '',
                    downloads: 0,
                    fake_downloads: '',
                    price: '',
                    video_url: '',
                    download_url: '',
                    baidu_url: '',
                    buy_url: '',
                    sort: 0
                };
                this.submitting = false;
            },

            // 移除不再需要的方法

            // 打开图标上传器
            openIconUploader() {
                // 如果是编辑模式且有软件ID，则自动保存到数据库
                const softwareId = this.showEditModal && this.formData.id ? this.formData.id : null;

                this.createFileInput('icon', (fileUrl, fileSize, databaseUpdated, responseData) => {
                    this.formData.icon = fileUrl;

                    // 如果创建了新记录，切换到编辑模式
                    if (responseData && responseData.new_record_created) {
                        // 更新表单数据
                        this.formData.id = responseData.software_id;
                        this.formData.name = responseData.software.name;

                        // 关闭添加模态框，打开编辑模态框
                        this.showAddModal = false;
                        this.showEditModal = true;

                        alert('已创建新软件记录并上传图标');
                    } else if (databaseUpdated) {
                        // 如果已经自动保存到数据库，显示提示
                        alert('图标已自动保存到数据库');
                    }
                }, softwareId);
            },

            // 打开软件上传器
            openSoftwareUploader() {
                // 如果是编辑模式且有软件ID，则自动保存到数据库
                const softwareId = this.showEditModal && this.formData.id ? this.formData.id : null;

                this.createFileInput('software', (fileUrl, fileSize, databaseUpdated, responseData) => {
                    // 使用新的数字化字段结构
                    this.formData.download_url_1 = fileUrl;
                    // 保持向后兼容性
                    this.formData.download_url = fileUrl;
                    if (!this.formData.size) {
                        this.formData.size = fileSize;
                    }

                    // 如果创建了新记录，切换到编辑模式
                    if (responseData && responseData.new_record_created) {
                        // 更新表单数据
                        this.formData.id = responseData.software_id;
                        this.formData.name = responseData.software.name;

                        // 关闭添加模态框，打开编辑模态框
                        this.showAddModal = false;
                        this.showEditModal = true;

                        alert('已创建新软件记录并上传软件文件');
                    } else if (databaseUpdated) {
                        // 如果已经自动保存到数据库，显示提示并重新加载数据
                        alert('软件文件链接已自动保存到数据库');
                        // 重新加载当前软件数据以显示最新的下载URL
                        this.reloadCurrentSoftware();
                    }
                }, softwareId);
            },

            // 创建文件上传输入框
            createFileInput(type, callback, softwareId = null) {
                // 创建一个隐藏的文件输入框
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.style.display = 'none';

                // 设置接受的文件类型
                if (type === 'icon') {
                    fileInput.accept = 'image/*';
                } else if (type === 'software') {
                    fileInput.accept = '.zip,.rar,.7z,.exe,.msi,.dmg,.pkg,.deb,.rpm';
                }

                // 添加到文档中
                document.body.appendChild(fileInput);

                // 监听文件选择事件
                fileInput.addEventListener('change', async (event) => {
                    if (fileInput.files && fileInput.files[0]) {
                        try {
                            const file = fileInput.files[0];

                            // 创建FormData对象
                            const formData = new FormData();
                            formData.append('file', file);

                            // 如果有软件ID，添加到请求中
                            let uploadUrl = `<?php echo $apiPath; ?>panel/software_upload.php?type=${type}`;
                            if (softwareId) {
                                uploadUrl += `&software_id=${softwareId}`;
                            }

                            // 显示上传中提示
                            const uploadingEl = document.createElement('div');
                            uploadingEl.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                            uploadingEl.innerHTML = `
                                <div class="bg-gray-900 rounded-lg shadow-lg p-6 text-center">
                                    <div class="loading-spinner mb-4"></div>
                                    <p class="text-blue-300">正在上传${type === 'icon' ? '图标' : '软件'}...</p>
                                </div>
                            `;
                            document.body.appendChild(uploadingEl);

                            // 发送上传请求
                            const response = await fetch(uploadUrl, {
                                method: 'POST',
                                body: formData,
                                headers: {
                                    'Authorization': 'Bearer ' + localStorage.getItem('admin_token')
                                }
                            });

                            // 关闭上传中提示
                            document.body.removeChild(uploadingEl);

                            // 处理响应
                            const result = await response.json();

                            if (result.success) {
                                // 上传成功
                                alert(`${type === 'icon' ? '图标' : '软件'}上传成功`);

                                // 调用回调函数，传递文件URL、大小、数据库更新状态和完整响应数据
                                const databaseUpdated = result.data.database_updated || false;
                                callback(result.data.file_url, result.data.file_size, databaseUpdated, result.data);

                                // 如果已经自动保存到数据库，刷新软件列表
                                if (databaseUpdated || result.data.new_record_created) {
                                    this.loadSoftwareList();
                                }
                            } else {
                                // 上传失败
                                alert(result.message || `${type === 'icon' ? '图标' : '软件'}上传失败`);
                            }
                        } catch (error) {
                            console.error('文件上传出错:', error);
                            alert(`${type === 'icon' ? '图标' : '软件'}上传出错`);
                        }
                    }

                    // 从文档中移除文件输入框
                    document.body.removeChild(fileInput);
                });

                // 触发文件选择对话框
                fileInput.click();
            }
        }
    });
</script>

<?php
// 渲染页脚
render_footer();
?>
