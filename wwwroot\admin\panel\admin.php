<?php
/**
 * 管理面板 - 管理员管理
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 设置页面标题和活动菜单
$pageTitle = '管理员管理';
$activeMenu = 'admin';

// 渲染头部（需要'admin.view'权限）
render_header($pageTitle, $activeMenu, 'admin.view');
?>

<!-- 页面内容开始 -->
<div id="adminApp">
    <!-- 顶部操作栏 -->
    <div class="flex flex-wrap justify-between items-center mb-4 gap-4">
        <div class="flex flex-wrap items-center gap-4">
            <div class="relative">
                <input type="text" v-model="searchQuery" @input="debounceSearch" placeholder="搜索管理员..." class="form-input pr-10">
                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            </div>
        </div>

        <div>
            <button v-if="checkPermission('admin.add')" @click="showAddModal = true" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i> 添加管理员
            </button>
        </div>
    </div>

    <!-- 管理员列表 -->
    <div class="content-card">
        <h2>管理员列表</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else-if="adminList.length === 0" class="text-center py-8 text-gray-400">
            <i class="fas fa-users-slash text-4xl mb-3"></i>
            <p>暂无管理员数据</p>
        </div>

        <div v-else>
            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="w-16">ID</th>
                            <th>用户名</th>
                            <th>角色</th>
                            <th>上次登录</th>
                            <th>创建时间</th>
                            <th class="w-32">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="admin in adminList" :key="admin.id">
                            <td>{{ admin.id }}</td>
                            <td>{{ admin.username }}</td>
                            <td>
                                <span v-if="admin.role_name" class="badge bg-blue-900 text-blue-200">{{ admin.role_name }}</span>
                                <span v-else class="badge bg-gray-700 text-gray-300">无角色</span>
                            </td>
                            <td>{{ formatTime(admin.last_login) || '从未登录' }}</td>
                            <td>{{ formatTime(admin.created_at) }}</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button v-if="checkPermission('admin.edit')" @click="editAdmin(admin)" class="btn-icon text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button v-if="checkPermission('admin.delete') && admin.id !== currentAdminId" @click="confirmDelete(admin)" class="btn-icon text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-400">
                    共 {{ pagination.totalItems }} 条记录，第 {{ pagination.page }}/{{ pagination.totalPages }} 页
                </div>
                <div class="flex space-x-2">
                    <button @click="changePage(1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button @click="changePage(pagination.page - 1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button @click="changePage(pagination.page + 1)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button @click="changePage(pagination.totalPages)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑管理员模态框 -->
    <div v-if="showAddModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl text-blue-300 font-semibold">{{ showEditModal ? '编辑管理员' : '添加管理员' }}</h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form @submit.prevent="submitForm">
                    <div class="form-group">
                        <label class="form-label">用户名 <span class="text-red-500">*</span></label>
                        <input type="text" v-model="formData.username" class="form-input" required :disabled="showEditModal">
                    </div>

                    <div class="form-group">
                        <label class="form-label">{{ showEditModal ? '新密码（留空不修改）' : '密码 *' }}</label>
                        <input type="password" v-model="formData.password" class="form-input" :required="!showEditModal">
                    </div>

                    <div class="form-group">
                        <label class="form-label">角色</label>
                        <select v-model="formData.role_id" class="form-input bg-transparent">
                            <option value="">无角色</option>
                            <option v-for="role in roleList" :key="role.id" :value="role.id">{{ role.name }}</option>
                        </select>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" @click="closeModal" class="btn bg-gray-700 text-white">取消</button>
                        <button type="submit" class="btn btn-primary" :disabled="submitting">
                            <span v-if="submitting"><i class="fas fa-spinner fa-spin mr-2"></i> 提交中...</span>
                            <span v-else>{{ showEditModal ? '保存修改' : '添加管理员' }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
            <div class="p-6">
                <div class="text-center mb-6">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-5xl mb-4"></i>
                    <h3 class="text-xl text-red-300 font-semibold">确认删除</h3>
                    <p class="mt-2 text-gray-300">您确定要删除管理员 "{{ adminToDelete.username }}" 吗？此操作不可撤销。</p>
                </div>

                <div class="flex justify-center space-x-3">
                    <button @click="showDeleteModal = false" class="btn bg-gray-700 text-white">取消</button>
                    <button @click="deleteAdmin" class="btn btn-danger" :disabled="deleting">
                        <span v-if="deleting"><i class="fas fa-spinner fa-spin mr-2"></i> 删除中...</span>
                        <span v-else>确认删除</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 页面内容结束 -->

<script>
    new Vue({
        el: '#adminApp',
        data: {
            loading: true,
            adminList: [],
            roleList: [],
            searchQuery: '',
            pagination: {
                page: 1,
                pageSize: 10,
                totalItems: 0,
                totalPages: 1
            },
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            formData: {
                id: null,
                username: '',
                password: '',
                role_id: ''
            },
            adminToDelete: {},
            submitting: false,
            deleting: false,
            searchTimeout: null,
            currentAdminId: parseInt(localStorage.getItem('admin_id') || '0')
        },
        mounted() {
            this.loadRoleList();
            this.loadAdminList();
        },
        methods: {
            // 检查权限
            checkPermission(permission) {
                const userPermissions = JSON.parse(localStorage.getItem('admin_permissions') || '[]');
                return userPermissions.includes('all') || userPermissions.includes(permission);
            },

            // 格式化时间戳
            formatTime(timestamp) {
                if (!timestamp) return null;
                const date = new Date(timestamp * 1000);
                return date.toLocaleString();
            },

            // 加载角色列表
            async loadRoleList() {
                try {
                    const data = await apiGet('<?php echo $apiPath; ?>panel/roles.php');

                    if (data && data.success) {
                        this.roleList = data.data.items || [];
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }
                } catch (error) {
                    console.error('加载角色数据出错：', error);
                }
            },

            // 加载管理员列表
            async loadAdminList() {
                this.loading = true;

                let url = `<?php echo $apiPath; ?>panel/admin.php?page=${this.pagination.page}&pageSize=${this.pagination.pageSize}`;

                if (this.searchQuery) {
                    url += `&search=${encodeURIComponent(this.searchQuery)}`;
                }

                try {
                    const data = await apiGet(url);

                    if (data && data.success) {
                        this.adminList = data.data.items || [];
                        this.pagination = data.data.pagination || this.pagination;
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }

                    this.loading = false;
                } catch (error) {
                    console.error('加载管理员数据出错：', error);
                    this.loading = false;
                }
            },

            // 防抖搜索
            debounceSearch() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.pagination.page = 1;
                    this.loadAdminList();
                }, 300);
            },

            // 切换页码
            changePage(page) {
                if (page < 1 || page > this.pagination.totalPages) {
                    return;
                }
                this.pagination.page = page;
                this.loadAdminList();
            },

            // 编辑管理员
            editAdmin(admin) {
                this.formData = {
                    id: admin.id,
                    username: admin.username,
                    password: '',
                    role_id: admin.role_id || ''
                };
                this.showEditModal = true;
            },

            // 确认删除
            confirmDelete(admin) {
                this.adminToDelete = admin;
                this.showDeleteModal = true;
            },

            // 删除管理员
            async deleteAdmin() {
                this.deleting = true;

                try {
                    const data = await apiDelete(
                        `<?php echo $apiPath; ?>panel/admin.php?id=${this.adminToDelete.id}`,
                        {},
                        { showSuccess: true, successMessage: '管理员删除成功', operation: '删除' }
                    );

                    this.deleting = false;
                    this.showDeleteModal = false;

                    if (data && data.success) {
                        // 删除成功，刷新列表
                        this.loadAdminList();
                    }
                } catch (error) {
                    this.deleting = false;
                    this.showDeleteModal = false;
                    console.error('删除管理员出错：', error);
                }
            },

            // 提交表单
            async submitForm() {
                // 验证表单
                if (!this.formData.username) {
                    alert('用户名不能为空');
                    return;
                }

                if (!this.showEditModal && !this.formData.password) {
                    alert('密码不能为空');
                    return;
                }

                this.submitting = true;

                try {
                    let data;
                    const operation = this.showEditModal ? '更新' : '添加';
                    const successMessage = this.showEditModal ? '管理员更新成功' : '管理员添加成功';

                    if (this.showEditModal) {
                        // 编辑模式
                        data = await apiPut(
                            `<?php echo $apiPath; ?>panel/admin.php?id=${this.formData.id}`,
                            this.formData,
                            {},
                            { showSuccess: true, successMessage, operation }
                        );
                    } else {
                        // 添加模式
                        data = await apiPost(
                            '<?php echo $apiPath; ?>panel/admin.php',
                            this.formData,
                            {},
                            { showSuccess: true, successMessage, operation }
                        );
                    }

                    this.submitting = false;

                    if (data && data.success) {
                        this.closeModal();
                        this.loadAdminList();
                    }
                } catch (error) {
                    this.submitting = false;
                    console.error((this.showEditModal ? '更新' : '添加') + '管理员出错：', error);
                }
            },

            // 关闭模态框
            closeModal() {
                this.showAddModal = false;
                this.showEditModal = false;
                this.formData = {
                    id: null,
                    username: '',
                    password: '',
                    role_id: ''
                };
                this.submitting = false;
            }
        }
    });
</script>

<?php
// 渲染页脚
render_footer();
?>
