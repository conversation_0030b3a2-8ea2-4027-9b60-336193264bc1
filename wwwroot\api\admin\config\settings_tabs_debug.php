<?php
/**
 * 管理后台调试设置选项卡配置
 *
 * 该文件定义了管理后台调试设置页面的选项卡、分组和字段结构
 * 仅负责UI展示和交互，不包含具体参数的默认值
 */

return [
    // 调试设置选项卡
    [
        'name' => 'debug',
        'title' => '调试设置',
        'icon' => 'fas fa-bug',
        'groups' => [
            // 调试功能设置组
            [
                'name' => 'debug_features',
                'title' => '调试功能',
                'settings_path' => 'debug', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'debug.enabled',
                        'label' => '启用调试模式',
                        'type' => 'checkbox',
                        'description' => '启用后将显示详细的错误信息和调试日志'
                    ],
                    [
                        'name' => 'debug.skip_payment_signature_verification',
                        'label' => '跳过支付签名验证',
                        'type' => 'checkbox',
                        'condition' => 'debug.enabled',
                        'description' => '⚠️ 仅用于测试！跳过支付宝/微信支付的签名验证，生产环境请务必关闭'
                    ],
                    [
                        'name' => 'debug.log_payment_callbacks',
                        'label' => '记录支付回调日志',
                        'type' => 'checkbox',
                        'condition' => 'debug.enabled',
                        'description' => '记录所有支付回调的详细信息到日志文件'
                    ]
                ]
            ]
        ]
    ]
];
