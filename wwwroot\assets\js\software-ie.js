/**
 * IE兼容的软件相关功能模块
 * 使用ES5语法替代ES6
 */

/**
 * 显示下载选项弹窗 (IE兼容版本)
 */
function showDownloadOptions(software) {
    console.log('显示下载选项:', software);
    
    if (typeof universalModal !== 'undefined') {
        universalModal.show('download', { software: software });
    } else {
        console.error('universalModal 未定义');
        alert('弹窗组件未加载，无法显示下载选项');
    }
}

/**
 * 处理购买按钮点击 (IE兼容版本)
 */
function handlePurchase(software) {
    console.log('处理购买:', software);
    
    // 检查支付配置
    if (typeof paymentConfig === 'undefined' || !paymentConfig.enabled) {
        alert('支付功能未启用');
        return;
    }
    
    var availablePayments = paymentConfig.available_payments || [];
    if (availablePayments.length === 0) {
        alert('暂无可用的支付方式');
        return;
    }
    
    // 显示支付选择弹窗
    if (typeof universalModal !== 'undefined') {
        universalModal.show('payment-options', {
            software: software,
            availablePayments: availablePayments
        });
    } else {
        console.error('universalModal 未定义');
        alert('弹窗组件未加载，无法显示支付选项');
    }
}

/**
 * 显示公告弹窗 (IE兼容版本)
 * 注意：此函数需要在HTML中动态生成，因为包含PHP变量
 */
function showAnnouncement() {
    // 此函数的实际实现将在index.php中动态生成
    // 这里只是占位符，确保函数存在
    console.log('showAnnouncement 函数被调用，但需要在HTML中重新定义');
}

/**
 * 处理软件直链访问 (IE兼容版本)
 */
function handleSoftwareDirectLink(softwareId) {
    console.log('处理软件直链访问，软件ID:', softwareId);
    
    // 调用API获取单个软件信息
    apiGet('api/public/index.php', { software_id: softwareId }, {}, { showLoading: false })
        .then(function(data) {
            if (data && data.success && data.software) {
                console.log('获取软件信息成功:', data.software);
                
                // 检查是否需要支付
                var software = data.software;
                if (software.price_value > 0 && !software.has_paid) {
                    // 需要支付，显示支付选项
                    handlePurchase(software);
                } else {
                    // 免费或已购买，显示下载选项
                    showDownloadOptions(software);
                }
            } else {
                console.error('获取软件信息失败:', data);
                alert('软件不存在或已下架');
            }
        })
        .catch(function(error) {
            console.error('获取软件信息出错:', error);
            alert('获取软件信息失败，请重试');
        });
}

/**
 * URL路由检查函数 (IE兼容版本)
 */
function checkUrlRouting() {
    var hash = window.location.hash;
    
    // 检查是否是软件直链格式：#/software/12345
    var softwareMatch = hash.match(/^#\/software\/(\d+)$/);
    if (softwareMatch) {
        var softwareId = parseInt(softwareMatch[1]);
        console.log('检测到软件直链，软件ID:', softwareId);
        
        // 延迟一下确保Vue实例已经初始化
        setTimeout(function() {
            handleSoftwareDirectLink(softwareId);
        }, 500);
        
        return true; // 返回true表示处理了路由，不显示公告
    }
    
    return false; // 返回false表示没有特殊路由，可以显示公告
}

/**
 * 关于我们按钮处理 (IE兼容版本)
 */
function initAboutButton() {
    var aboutBtn = document.getElementById('aboutBtn');
    var qrModal = document.getElementById('qrModal');
    var qrClose = document.getElementById('qrClose');
    
    if (aboutBtn && qrModal) {
        aboutBtn.addEventListener('click', function() {
            qrModal.classList.add('active');
        });
    }
    
    if (qrClose && qrModal) {
        qrClose.addEventListener('click', function() {
            qrModal.classList.remove('active');
        });
    }
    
    // 点击外部关闭
    if (qrModal) {
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) {
                qrModal.classList.remove('active');
            }
        });
    }
}

/**
 * 初始化页面事件 (IE兼容版本)
 */
function initPageEvents() {
    console.log('初始化页面事件');
    
    // 初始化关于我们按钮
    initAboutButton();
    
    // 页面加载时检查URL路由和公告
    setTimeout(function() {
        // 检查URL路由，如果是软件直链则不显示公告
        if (!checkUrlRouting()) {
            showAnnouncement();
        }
    }, 1000); // 延迟1秒显示公告
    
    // 监听hash变化，支持浏览器前进后退
    if (window.addEventListener) {
        window.addEventListener('hashchange', function() {
            console.log('Hash变化:', window.location.hash);
            checkUrlRouting();
        });
    } else if (window.attachEvent) {
        // IE8兼容
        window.attachEvent('onhashchange', function() {
            console.log('Hash变化:', window.location.hash);
            checkUrlRouting();
        });
    }
    
    // 监听支付成功事件
    if (document.addEventListener) {
        document.addEventListener('payment:success', function(event) {
            console.log('收到支付成功事件:', event.detail);
        });
    } else if (document.attachEvent) {
        // IE8兼容
        document.attachEvent('onpayment:success', function(event) {
            console.log('收到支付成功事件:', event.detail);
        });
    }
}

/**
 * 页面加载完成后初始化
 */
if (document.readyState === 'loading') {
    if (document.addEventListener) {
        document.addEventListener('DOMContentLoaded', initPageEvents);
    } else if (document.attachEvent) {
        // IE8兼容
        document.attachEvent('onreadystatechange', function() {
            if (document.readyState === 'complete') {
                initPageEvents();
            }
        });
    }
} else {
    initPageEvents();
}

// 全局函数导出，保持向后兼容
window.showDownloadOptions = showDownloadOptions;
window.handlePurchase = handlePurchase;
window.showAnnouncement = showAnnouncement;
window.handleSoftwareDirectLink = handleSoftwareDirectLink;
window.checkUrlRouting = checkUrlRouting;

console.log('IE兼容的软件功能模块已加载');
