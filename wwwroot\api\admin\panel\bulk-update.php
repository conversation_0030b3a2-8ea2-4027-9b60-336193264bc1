<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

// 获取请求方法
$method = $_SERVER['REQUEST_METHOD'];

try {
    // 处理批量删除
    if ($method === 'DELETE') {
        $action = get_param('action', null);
        $ids = get_param('ids', null);

        if ($action !== 'delete' || empty($ids)) {
            json_response(['success' => false, 'message' => '参数错误'], 400);
        }

        // 解析ID列表
        $idArray = explode(',', $ids);
        if (empty($idArray)) {
            json_response(['success' => false, 'message' => '未提供有效的ID列表'], 400);
        }

        // 将ID转换为整数并用逗号连接
        $idList = implode(',', array_map('intval', $idArray));

        // 开始事务
        $db->exec('BEGIN TRANSACTION');

        try {
            // 删除软件
            $result = $db->exec("DELETE FROM softwares WHERE id IN ($idList)");

            if ($result === false) {
                throw new Exception('删除软件失败');
            }

            // 提交事务
            $db->exec('COMMIT');

            json_response([
                'success' => true,
                'message' => '批量删除成功',
                'affected_rows' => $db->changes()
            ]);
        } catch (Exception $e) {
            // 回滚事务
            $db->exec('ROLLBACK');
            throw $e;
        }
    }
    // 处理批量更新
    else if ($method === 'POST') {
        // 获取请求体
        $requestBody = file_get_contents('php://input');
        $data = json_decode($requestBody, true);

        if (empty($data) || !isset($data['action'])) {
            json_response(['success' => false, 'message' => '参数错误'], 400);
        }

        $action = $data['action'];

        // 批量更新分类
        if ($action === 'update_category') {
            if (empty($data['ids']) || !isset($data['category_id'])) {
                json_response(['success' => false, 'message' => '参数错误: 需要提供ids和category_id'], 400);
            }

            $ids = $data['ids'];
            $categoryId = $data['category_id'];

            // 验证分类ID
            if (!empty($categoryId)) {
                $categoryExists = $db->querySingle("SELECT id FROM categories WHERE id = $categoryId");
                if (!$categoryExists) {
                    json_response(['success' => false, 'message' => '指定的分类不存在'], 400);
                }
            }

            // 将ID转换为整数并用逗号连接
            $idList = implode(',', array_map('intval', $ids));

            // 开始事务
            $db->exec('BEGIN TRANSACTION');

            try {
                // 更新软件分类
                $result = $db->exec("UPDATE softwares SET category = " . ($categoryId ? $categoryId : 'NULL') . " WHERE id IN ($idList)");

                if ($result === false) {
                    throw new Exception('更新软件分类失败');
                }

                // 提交事务
                $db->exec('COMMIT');

                json_response([
                    'success' => true,
                    'message' => '批量更新分类成功',
                    'affected_rows' => $db->changes()
                ]);
            } catch (Exception $e) {
                // 回滚事务
                $db->exec('ROLLBACK');
                throw $e;
            }
        }
        // 其他批量操作...
        else {
            json_response(['success' => false, 'message' => '不支持的操作: ' . $action], 400);
        }
    } else {
        json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '批量操作失败: ' . $e->getMessage()
    ], 500);
}