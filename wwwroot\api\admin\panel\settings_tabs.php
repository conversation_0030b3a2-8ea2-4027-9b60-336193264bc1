<?php
/**
 * 管理后台设置选项卡API
 *
 * 该文件负责加载所有设置选项卡配置文件并提供API接口
 * 支持获取所有选项卡列表或单个选项卡的详细信息
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 检查权限
require_permission('admin.settings');

// 获取当前设置
$currentSettings = Settings::getAll();

// 获取配置文件目录
$configDir = dirname(dirname(__FILE__)) . DS . 'config';

// 包含选项卡加载器文件
require_once $configDir . DS . 'settings_tabs.php';

// 加载所有选项卡配置
$tabsConfig = load_settings_tabs($configDir);

// 处理单个选项卡请求
if (isset($_GET['tab'])) {
    $tabName = $_GET['tab'];
    $tabData = null;

    // 查找请求的选项卡
    foreach ($tabsConfig as $tab) {
        if ($tab['name'] === $tabName) {
            $tabData = $tab;
            break;
        }
    }

    if (!$tabData) {
        api_error('选项卡不存在');
    }

    // 处理字段可见性
    if (isset($tabData['groups'])) {
        foreach ($tabData['groups'] as &$group) {
            if (isset($group['fields'])) {
                foreach ($group['fields'] as &$field) {
                    // 设置字段可见性
                    $field['visible'] = true;

                    // 如果有条件，检查条件是否满足
                    if (isset($field['condition'])) {
                        $field['visible'] = Settings::checkCondition($field['condition'], $currentSettings);
                    }
                }
            }
        }
    }

    api_success($tabData);
}

// 处理选项卡列表请求
$tabsList = [];

foreach ($tabsConfig as $tab) {
    // 只返回基本信息，不包含字段
    $tabsList[] = [
        'name' => $tab['name'],
        'title' => $tab['title'],
        'icon' => $tab['icon']
    ];
}

api_success($tabsList);
