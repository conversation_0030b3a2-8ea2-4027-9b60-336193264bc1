<?php
/**
 * 管理后台网站设置选项卡配置
 *
 * 该文件定义了管理后台网站设置页面的选项卡、分组和字段结构
 * 仅负责UI展示和交互，不包含具体参数的默认值
 */

return [
    // 网站设置选项卡
    [
        'name' => 'website',
        'title' => '网站设置',
        'icon' => 'fas fa-globe',
        'groups' => [
            // 基本设置组
            [
                'name' => 'basic',
                'title' => '基本设置',
                'settings_path' => 'basic', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'basic.site_title',
                        'label' => '网站标题',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入网站标题',
                        'description' => '显示在浏览器标题栏和网站顶部'
                    ],
                    [
                        'name' => 'basic.site_subtitle',
                        'label' => '网站副标题',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入网站副标题',
                        'description' => '显示在网站标题下方'
                    ],
                    [
                        'name' => 'basic.site_logo',
                        'label' => '网站Logo URL',
                        'type' => 'text',
                        'placeholder' => '请输入网站Logo URL'
                    ],
                    [
                        'name' => 'basic.site_favicon',
                        'label' => '网站图标 URL',
                        'type' => 'text',
                        'placeholder' => '请输入网站图标 URL'
                    ],
                    [
                        'name' => 'basic.qr_code_url',
                        'label' => '关注二维码 URL',
                        'type' => 'text',
                        'placeholder' => '请输入关注二维码 URL',
                        'description' => '用于显示在前台的关注二维码图片链接',
                        'has_upload' => true,
                        'upload_type' => 'site_image',
                        'upload_path' => 'qrcode'
                    ],
                    [
                        'name' => 'basic.frontend_url',
                        'label' => '前台网站URL',
                        'type' => 'text',
                        'placeholder' => '请输入前台网站完整URL',
                        'description' => '用于预览上传的图片，可以是相对路径或完整URL'
                    ]
                ]
            ],
            // SEO优化设置组
            [
                'name' => 'seo',
                'title' => 'SEO优化',
                'settings_path' => 'basic', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'basic.site_description',
                        'label' => '网站描述',
                        'type' => 'textarea',
                        'placeholder' => '请输入网站描述',
                        'description' => '用于SEO优化，显示在搜索引擎结果中'
                    ],
                    [
                        'name' => 'basic.site_keywords',
                        'label' => '网站关键词',
                        'type' => 'text',
                        'placeholder' => '请输入网站关键词',
                        'description' => '多个关键词用英文逗号分隔'
                    ]
                ]
            ],
            // 公告设置组
            [
                'name' => 'announcement',
                'title' => '公告设置',
                'settings_path' => 'announcement', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'announcement.enabled',
                        'label' => '启用公告',
                        'type' => 'radio',
                        'options' => [
                            ['value' => 'true', 'label' => '启用'],
                            ['value' => 'false', 'label' => '禁用']
                        ],
                        'valueType' => 'boolean' // 指示前端将值转换为布尔值
                    ],
                    [
                        'name' => 'announcement.title',
                        'label' => '公告标题',
                        'type' => 'text',
                        'condition' => 'announcement.enabled',
                        'placeholder' => '请输入公告标题'
                    ],
                    [
                        'name' => 'announcement.content',
                        'label' => '公告内容',
                        'type' => 'textarea',
                        'condition' => 'announcement.enabled',
                        'placeholder' => '请输入公告内容',
                        'description' => '支持HTML格式'
                    ],
                    [
                        'name' => 'announcement.style',
                        'label' => '公告样式',
                        'type' => 'select',
                        'condition' => 'announcement.enabled',
                        'options' => [
                            ['value' => 'info', 'label' => '信息 (蓝色)'],
                            ['value' => 'success', 'label' => '成功 (绿色)'],
                            ['value' => 'warning', 'label' => '警告 (黄色)'],
                            ['value' => 'danger', 'label' => '危险 (红色)']
                        ]
                    ],
                    [
                        'name' => 'announcement.repeat_show',
                        'label' => '每次访问都显示',
                        'type' => 'checkbox',
                        'condition' => 'announcement.enabled',
                        'description' => '如果不勾选，用户关闭后将不再显示，直到公告内容更新'
                    ]
                ]
            ]
        ]
    ]
];
