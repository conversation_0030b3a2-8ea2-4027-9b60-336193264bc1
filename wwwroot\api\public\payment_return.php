<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

/**
 * 支付结果API处理
 */

// 包含公共API验证文件
require_once dirname(__DIR__) . DS . 'public.php';

// 包含支付函数库
require_once dirname(__DIR__) . DS . 'payment.php';

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : '';
$orderNo = isset($_GET['order_no']) ? $_GET['order_no'] : '';

try {
    switch ($action) {
        case 'get_order':
        case 'get_order_info':
            // 获取订单信息
            if (empty($orderNo)) {
                json_response(['success' => false, 'message' => '缺少订单号'], 400);
            }

            // 获取数据库连接
            $db = get_db_connection();

            // 查询订单信息
            $stmt = $db->prepare('SELECT * FROM payment_orders WHERE order_no = :order_no');
            $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
            $result = $stmt->execute();
            $order = $result->fetchArray(SQLITE3_ASSOC);

            if (!$order) {
                json_response(['success' => false, 'message' => '订单不存在'], 404);
            }

            // 获取软件信息
            $stmt = $db->prepare('SELECT * FROM softwares WHERE id = :id');
            $stmt->bindValue(':id', $order['software_id'], SQLITE3_INTEGER);
            $result = $stmt->execute();
            $software = $result->fetchArray(SQLITE3_ASSOC);

            // 获取站点设置
            $settings = get_settings();

            // 返回订单信息
            json_response([
                'success' => true,
                'data' => [
                    'order_no' => $order['order_no'],
                    'software_id' => $order['software_id'],
                    'software_name' => $software ? $software['name'] : '未知软件',
                    'amount' => $order['amount'],
                    'payment_type' => $order['payment_type'],
                    'status' => $order['status'],
                    'created_at' => $order['created_at'],
                    'paid_at' => $order['paid_at'],
                    'site_title' => $settings['basic']['site_title'] ?? '娜宝贝软件'
                ]
            ]);
            break;

        case 'check_order':
            // 检查订单状态
            if (empty($orderNo)) {
                json_response(['success' => false, 'message' => '缺少订单号'], 400);
            }

            // 获取数据库连接
            $db = get_db_connection();

            // 查询订单信息
            $stmt = $db->prepare('SELECT * FROM payment_orders WHERE order_no = :order_no');
            $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
            $result = $stmt->execute();
            $order = $result->fetchArray(SQLITE3_ASSOC);

            if (!$order) {
                json_response(['success' => false, 'message' => '订单不存在'], 404);
            }

            // 获取软件信息
            $stmt = $db->prepare('SELECT * FROM softwares WHERE id = :id');
            $stmt->bindValue(':id', $order['software_id'], SQLITE3_INTEGER);
            $result = $stmt->execute();
            $software = $result->fetchArray(SQLITE3_ASSOC);

            // 获取站点设置
            $settings = get_settings();

            // 返回订单状态信息
            json_response([
                'success' => true,
                'data' => [
                    'order_no' => $order['order_no'],
                    'software_id' => $order['software_id'],
                    'software_name' => $software ? $software['name'] : '未知软件',
                    'amount' => $order['amount'],
                    'payment_type' => $order['payment_type'],
                    'status' => $order['status'],
                    'created_at' => $order['created_at'],
                    'paid_at' => $order['paid_at'],
                    'site_title' => $settings['basic']['site_title'] ?? '娜宝贝软件'
                ]
            ]);
            break;

        case 'test_config':
            // 测试支付宝配置
            $config = get_payment_config('alipay');
            if (!$config) {
                json_response(['success' => false, 'message' => '支付宝支付未启用'], 400);
            }

            // 测试公钥格式
            $publicKeyContent = $config['public_key'];
            $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                         wordwrap($publicKeyContent, 64, "\n", true) .
                         "\n-----END PUBLIC KEY-----";

            // 测试公钥是否有效
            $keyResource = openssl_pkey_get_public($publicKey);
            $keyValid = ($keyResource !== false);
            // 在PHP 8.0+中，资源会自动释放，不需要手动释放

            json_response([
                'success' => true,
                'data' => [
                    'app_id' => $config['app_id'] ?? 'not_set',
                    'public_key_length' => strlen($config['public_key'] ?? ''),
                    'private_key_length' => strlen($config['private_key'] ?? ''),
                    'gateway' => $config['gateway'] ?? 'not_set',
                    'notify_url' => $config['notify_url'] ?? 'not_set',
                    'public_key_valid' => $keyValid,
                    'public_key_formatted' => $publicKey,
                    'openssl_errors' => openssl_error_string()
                ]
            ]);
            break;

        case 'test_sign':
            // 测试具体的签名验证
            $config = get_payment_config('alipay');
            if (!$config) {
                json_response(['success' => false, 'message' => '支付宝支付未启用'], 400);
            }

            // 使用您提供的具体参数进行测试
            $testParams = [
                'app_id' => '2021004139607564',
                'auth_app_id' => '2021004139607564',
                'charset' => 'utf-8',
                'method' => 'alipay.trade.page.pay.return',
                'order_no' => '202505251500412770',
                'out_trade_no' => '202505251500412770',
                'seller_id' => '2088742202512445',
                'timestamp' => '2025-05-25 23:01:14',
                'total_amount' => '0.01',
                'trade_no' => '2025052522001437801446816132',
                'version' => '1.0'
            ];

            $testSign = 'Cw0Ury50VdDD0knXp9VnCcpdtxdtUfSL3yvzfsqnI3uTwRIe2d9s47QrX++3ERmsCTFA18aqYgtYa0SKMLSQGt6xs8uojZIGdQwgxcZOkbqCb3wZ0dzm4VoxWe1MAjC4dKt/F7/iFeVWCqjf+l7vMTjvQkrOcb9BZYgik4lwfxVcw0B6hnsE102WAsAUP97+az7UlR8CAJIX3cqumDenNqg/JHmsCDpxV7uTPKm7lYs4r/cJk8vsGBMWnmD7w37ZrpYeJ9LNR5kcSJtQOxzSEP6H/RX7+8pNvGwOWhnftJTY48K/AzOtrA6LHZPF8dbXXOVVfU7tJ0YlbSripUy6ZA==';

            ksort($testParams);
            $stringToBeSigned = '';
            foreach ($testParams as $k => $v) {
                $stringToBeSigned .= $k . '=' . $v . '&';
            }
            $stringToBeSigned = rtrim($stringToBeSigned, '&');

            $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                         wordwrap($config['public_key'], 64, "\n", true) .
                         "\n-----END PUBLIC KEY-----";

            $result = openssl_verify($stringToBeSigned, base64_decode($testSign), $publicKey, OPENSSL_ALGO_SHA256);

            json_response([
                'success' => true,
                'data' => [
                    'test_params' => $testParams,
                    'string_to_be_signed' => $stringToBeSigned,
                    'verify_result' => $result,
                    'public_key_valid' => openssl_pkey_get_public($publicKey) !== false,
                    'openssl_error' => openssl_error_string()
                ]
            ]);
            break;

        case 'verify_alipay_return':
            // 验证支付宝同步回调
            if (empty($orderNo)) {
                json_response(['success' => false, 'message' => '缺少订单号'], 400);
            }

            // 获取支付宝配置
            $config = get_payment_config('alipay');
            if (!$config) {
                json_response(['success' => false, 'message' => '支付宝支付未启用'], 400);
            }

            // 检查是否启用了调试模式跳过签名验证
            $siteSettings = get_settings();
            $skipSignatureVerification = isset($siteSettings['debug']['skip_payment_signature_verification'])
                && $siteSettings['debug']['skip_payment_signature_verification'] === true;

            // 获取所有GET参数
            $params = $_GET;

            // 移除action参数（这是我们自己添加的）
            unset($params['action']);

            // 验证必要参数
            if (empty($params['sign']) || empty($params['out_trade_no']) || empty($params['total_amount'])) {
                json_response(['success' => false, 'message' => '回调参数不完整'], 400);
            }

            // 验证订单号是否匹配
            if ($params['out_trade_no'] !== $orderNo) {
                json_response(['success' => false, 'message' => '订单号不匹配'], 400);
            }

            // 验证签名
            $sign = $params['sign'];
            $signType = isset($params['sign_type']) ? $params['sign_type'] : 'RSA2';

            // 移除签名和签名类型参数，以及其他不参与签名的参数
            $signParams = $params;
            unset($signParams['order_no']);
            unset($signParams['sign'], $signParams['sign_type']);

            // 对于同步回调，还需要移除一些不参与签名的参数
            // 根据支付宝文档，某些参数可能不参与签名验证
            // unset($signParams['action']); // 已经在前面移除了

            // 按照参数名ASCII码从小到大排序
            ksort($signParams);

            // 拼接参数 - 只包含非空值
            $stringToBeSigned = urldecode(http_build_query($signParams));

            // 验证签名
            $publicKeyContent = $config['public_key'];

            // 如果公钥不包含BEGIN/END标记，则添加
            if (strpos($publicKeyContent, '-----BEGIN') === false) {
                $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                             wordwrap($publicKeyContent, 64, "\n", true) .
                             "\n-----END PUBLIC KEY-----";
            } else {
                $publicKey = $publicKeyContent;
            }

            // 记录调试信息
            $debugInfo = [
                'original_params' => $params,
                'sign_params' => $signParams,
                'string_to_be_signed' => $stringToBeSigned,
                'sign' => $sign,
                'public_key_length' => strlen($config['public_key']),
                'public_key_raw' => $config['public_key'],
                'formatted_public_key_length' => strlen($publicKey)
            ];

            // 根据调试设置决定是否跳过签名验证
            if ($skipSignatureVerification) {
                $result = 1; // 跳过验证，直接设为成功
                $debugInfo['signature_verification'] = 'skipped_by_debug_setting';
            } else {
                $result = openssl_verify($stringToBeSigned, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);
                $debugInfo['signature_verification'] = 'performed';
            }

            if ($result !== 1) {
                // 返回详细的调试信息
                json_response([
                    'success' => false,
                    'message' => '签名验证失败',
                    'debug' => $debugInfo,
                    'openssl_error' => openssl_error_string()
                ], 400);
            }

            // 获取数据库连接
            $db = get_db_connection();

            // 查询订单信息
            $stmt = $db->prepare('SELECT * FROM payment_orders WHERE order_no = :order_no');
            $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
            $result = $stmt->execute();
            $order = $result->fetchArray(SQLITE3_ASSOC);

            if (!$order) {
                json_response(['success' => false, 'message' => '订单不存在'], 404);
            }

            // 验证金额是否匹配
            $expectedAmount = number_format(floatval($order['amount']), 2, '.', '');
            $actualAmount = number_format(floatval($params['total_amount']), 2, '.', '');

            if ($expectedAmount !== $actualAmount) {
                json_response(['success' => false, 'message' => '支付金额不匹配'], 400);
            }

            // 如果订单还未支付，则更新订单状态
            if ($order['status'] === 'pending') {
                $stmt = $db->prepare('UPDATE payment_orders SET status = :status, paid_at = :paid_at, transaction_id = :transaction_id WHERE order_no = :order_no');
                $stmt->bindValue(':status', 'paid', SQLITE3_TEXT);
                $stmt->bindValue(':paid_at', time(), SQLITE3_INTEGER);
                $stmt->bindValue(':transaction_id', isset($params['trade_no']) ? $params['trade_no'] : '', SQLITE3_TEXT);
                $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
                $stmt->execute();
            }

            // 获取软件信息
            $stmt = $db->prepare('SELECT * FROM softwares WHERE id = :id');
            $stmt->bindValue(':id', $order['software_id'], SQLITE3_INTEGER);
            $result = $stmt->execute();
            $software = $result->fetchArray(SQLITE3_ASSOC);

            // 获取站点设置
            $siteSettings = get_settings();

            // 返回验证成功的结果
            json_response([
                'success' => true,
                'message' => '支付验证成功',
                'data' => [
                    'order_no' => $order['order_no'],
                    'software_id' => $order['software_id'],
                    'software_name' => $software ? $software['name'] : '未知软件',
                    'amount' => $order['amount'],
                    'payment_type' => $order['payment_type'],
                    'status' => 'paid',
                    'created_at' => $order['created_at'],
                    'paid_at' => time(),
                    'site_title' => $siteSettings['basic']['site_title'] ?? '娜宝贝软件',
                    'verified' => true
                ]
            ]);
            break;

        default:
            json_response(['success' => false, 'message' => '不支持的操作'], 400);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}
