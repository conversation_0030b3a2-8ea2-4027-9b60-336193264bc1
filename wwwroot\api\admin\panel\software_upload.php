<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

// 只允许POST请求
if ($method !== 'POST') {
    json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
}

// 获取上传类型
$type = get_param('type', '');

// 验证上传类型
if (!in_array($type, ['software', 'icon', 'site_image'])) {
    json_response(['success' => false, 'message' => '无效的上传类型'], 400);
}

// 获取站点设置
$settings = get_settings();

// 检查存储路径配置
if ($type === 'software' && empty($settings['upload']['software_package_path'])) {
    json_response(['success' => false, 'message' => '软件包存储路径未配置，请先在站点设置中配置'], 400);
}

if ($type === 'icon' && empty($settings['upload']['software_icon_path'])) {
    json_response(['success' => false, 'message' => '软件图标存储路径未配置，请先在站点设置中配置'], 400);
}

if ($type === 'site_image' && empty($settings['upload']['site_images_path'])) {
    json_response(['success' => false, 'message' => '站点图片存储路径未配置，请先在站点设置中配置'], 400);
}

// 获取存储路径
if ($type === 'software') {
    $storagePath = $settings['upload']['software_package_path'];
} elseif ($type === 'icon') {
    $storagePath = $settings['upload']['software_icon_path'];
} elseif ($type === 'site_image') {
    $storagePath = $settings['upload']['site_images_path'];

    // 获取子路径参数（如qrcode）
    $subPath = get_param('sub_path', '');
    if ($subPath) {
        $storagePath .= '/' . $subPath;
    }
}

$fullStoragePath = __WWWROOT__ . DS . $storagePath;

// 确保存储目录存在
if (!is_dir($fullStoragePath)) {
    if (!mkdir($fullStoragePath, 0755, true)) {
        json_response(['success' => false, 'message' => '无法创建存储目录'], 500);
    }
}

// 检查是否有文件上传
if (empty($_FILES['file'])) {
    json_response(['success' => false, 'message' => '未找到上传文件'], 400);
}

$file = $_FILES['file'];

// 检查上传错误
if ($file['error'] !== UPLOAD_ERR_OK) {
    $errorMessages = [
        UPLOAD_ERR_INI_SIZE => '上传的文件超过了php.ini中upload_max_filesize指令限制的大小',
        UPLOAD_ERR_FORM_SIZE => '上传的文件超过了HTML表单中MAX_FILE_SIZE指令指定的大小',
        UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
        UPLOAD_ERR_NO_FILE => '没有文件被上传',
        UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
        UPLOAD_ERR_CANT_WRITE => '文件写入失败',
        UPLOAD_ERR_EXTENSION => '文件上传因扩展程序而停止',
    ];

    $errorMessage = $errorMessages[$file['error']] ?? '未知上传错误';
    json_response(['success' => false, 'message' => $errorMessage], 400);
}

// 验证文件类型
if ($type === 'icon' || $type === 'site_image') {
    // 图标和站点图片只允许图片文件
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        json_response(['success' => false, 'message' => '只允许上传图片文件 (JPEG, PNG, GIF, WEBP)'], 400);
    }
} elseif ($type === 'software') {
    // 软件允许常见的压缩文件和可执行文件
    $allowedExtensions = ['zip', 'rar', '7z', 'exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm'];
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($fileExtension, $allowedExtensions)) {
        json_response([
            'success' => false,
            'message' => '只允许上传以下格式的文件: ' . implode(', ', $allowedExtensions)
        ], 400);
    }
}

// 获取文件扩展名
$fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

// 初始化变量
$newRecordCreated = false;
$softwareId = null;

// 根据上传类型确定文件名
if ($type === 'site_image') {
    // 对于站点图片，使用固定文件名
    $fileName = get_param('file_name', '');

    // 如果是二维码，使用固定文件名
    if (get_param('sub_path', '') === 'qrcode') {
        $fileName = 'qrcode';
    } else if (empty($fileName)) {
        // 如果没有指定文件名，使用原始文件名
        $fileName = pathinfo($file['name'], PATHINFO_FILENAME);
    }

    $newFileName = "{$fileName}.{$fileExtension}";
} else {
    // 对于软件和图标，使用软件ID作为文件名
    $softwareId = get_param('software_id', null);

    // 如果没有提供软件ID，则创建一个新的软件记录
    if (!$softwareId) {
        // 获取数据库连接
        $db = get_db_connection();

        // 创建一个新的软件记录
        $stmt = $db->prepare('INSERT INTO softwares (name, created_at) VALUES (:name, :created_at)');
        $stmt->bindValue(':name', pathinfo($file['name'], PATHINFO_FILENAME), SQLITE3_TEXT); // 使用文件名作为软件名称
        $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
        $stmt->execute();

        // 获取新创建的软件ID
        $softwareId = $db->lastInsertRowID();
        $newRecordCreated = true;
    }

    // 使用软件ID作为文件名
    $newFileName = "{$softwareId}.{$fileExtension}";
}

$filePath = $fullStoragePath . DS . $newFileName;

// 移动上传的文件
if (!move_uploaded_file($file['tmp_name'], $filePath)) {
    json_response(['success' => false, 'message' => '文件上传失败'], 500);
}

// 计算文件大小（格式化为人类可读的格式）
$fileSize = filesize($filePath);
$formattedSize = format_file_size($fileSize);

// 准备响应数据
$responseData = [
    'file_name' => $file['name'],
    'file_path' => $storagePath . $newFileName,
    'file_url' => '/' . $storagePath . $newFileName,
    'file_size' => $formattedSize,
    'file_size_bytes' => $fileSize,
    'file_type' => $file['type'],
    'software_id' => $softwareId,
    'new_record_created' => $newRecordCreated
];

// 处理软件和图标类型的数据库更新
if ($type === 'software' || $type === 'icon') {
    // 获取数据库连接
    $db = get_db_connection();

    // 检查软件是否存在
    $stmt = $db->prepare('SELECT id, name, category, description FROM softwares WHERE id = :id');
    $stmt->bindValue(':id', $softwareId, SQLITE3_INTEGER);
    $result = $stmt->execute();
    $software = $result->fetchArray(SQLITE3_ASSOC);

    if ($software) {
        // 根据上传类型更新不同的字段
        if ($type === 'software') {
            // 更新下载URL（使用新的数字化字段结构）
            $stmt = $db->prepare('UPDATE softwares SET download_url_1 = :url, size = :size WHERE id = :id');
            $stmt->bindValue(':url', $responseData['file_url'], SQLITE3_TEXT);
            $stmt->bindValue(':size', $responseData['file_size'], SQLITE3_TEXT);
            $stmt->bindValue(':id', $softwareId, SQLITE3_INTEGER);
            $stmt->execute();

            $responseData['updated_field'] = 'download_url_1';
        } else if ($type === 'icon') {
            // 更新图标URL
            $stmt = $db->prepare('UPDATE softwares SET icon = :url WHERE id = :id');
            $stmt->bindValue(':url', $responseData['file_url'], SQLITE3_TEXT);
            $stmt->bindValue(':id', $softwareId, SQLITE3_INTEGER);
            $stmt->execute();

            $responseData['updated_field'] = 'icon';
        }

        $responseData['database_updated'] = true;

        // 如果是新创建的记录，返回软件信息
        if ($newRecordCreated) {
            $responseData['software'] = $software;
        }
    }
} elseif ($type === 'site_image') {
    // 对于站点图片，不需要更新数据库
    $responseData['field_name'] = get_param('field_name', '');
    $responseData['updated_field'] = $responseData['field_name'];
}

// 返回成功响应
json_response([
    'success' => true,
    'message' => '文件上传成功',
    'data' => $responseData
]);

/**
 * 格式化文件大小
 *
 * @param int $bytes 文件大小（字节）
 * @param int $precision 精度
 * @return string 格式化后的文件大小
 */
function format_file_size($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, $precision) . ' ' . $units[$pow];
}
