<?php
/**
 * 管理面板 - 下载统计
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 设置页面标题和活动菜单
$pageTitle = '下载统计';
$activeMenu = 'stats';

// 渲染头部（需要'stats.view'权限）
render_header($pageTitle, $activeMenu, 'stats.view');
?>

<!-- 页面内容开始 -->
<div id="statsApp">
    <!-- 顶部过滤器 -->
    <div class="flex flex-wrap justify-between items-center mb-4 gap-4">
        <div class="flex flex-wrap items-center gap-4">
            <div class="relative">
                <select v-model="timeRange" @change="loadStats" class="form-input bg-transparent pr-8">
                    <option value="today">今天</option>
                    <option value="yesterday">昨天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                    <option value="year">今年</option>
                    <option value="all">全部</option>
                    <option value="custom">自定义</option>
                </select>
                <i class="fas fa-calendar absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            </div>

            <div v-if="timeRange === 'custom'" class="flex items-center gap-2">
                <input type="date" v-model="startDate" @change="loadStats" class="form-input bg-transparent">
                <span class="text-gray-400">至</span>
                <input type="date" v-model="endDate" @change="loadStats" class="form-input bg-transparent">
            </div>

            <div class="relative">
                <select v-model="selectedSoftware" @change="loadStats" class="form-input bg-transparent pr-8">
                    <option value="">所有软件</option>
                    <option v-for="software in softwareList" :key="software.id" :value="software.id">{{ software.name }}</option>
                </select>
                <i class="fas fa-laptop-code absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            </div>
        </div>

        <div>
            <button @click="exportData" class="btn btn-primary">
                <i class="fas fa-download mr-2"></i> 导出数据
            </button>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="content-card p-4">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-blue-300 mb-1">总下载量</div>
                    <div class="text-2xl text-white">{{ summary.totalDownloads }}</div>
                </div>
                <div class="text-4xl text-blue-400">
                    <i class="fas fa-download"></i>
                </div>
            </div>
        </div>

        <div class="content-card p-4">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-green-300 mb-1">独立IP数</div>
                    <div class="text-2xl text-white">{{ summary.uniqueIPs }}</div>
                </div>
                <div class="text-4xl text-green-400">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>

        <div class="content-card p-4">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-purple-300 mb-1">移动端下载</div>
                    <div class="text-2xl text-white">{{ summary.mobileDownloads }} <span class="text-sm text-gray-400">({{ summary.mobilePercentage }}%)</span></div>
                </div>
                <div class="text-4xl text-purple-400">
                    <i class="fas fa-mobile-alt"></i>
                </div>
            </div>
        </div>

        <div class="content-card p-4">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-yellow-300 mb-1">桌面端下载</div>
                    <div class="text-2xl text-white">{{ summary.desktopDownloads }} <span class="text-sm text-gray-400">({{ summary.desktopPercentage }}%)</span></div>
                </div>
                <div class="text-4xl text-yellow-400">
                    <i class="fas fa-desktop"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="content-card mb-6">
        <h2>下载趋势</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else-if="!chartData.labels.length" class="text-center py-8 text-gray-400">
            <i class="fas fa-chart-bar text-4xl mb-3"></i>
            <p>暂无下载数据</p>
        </div>

        <div v-else class="h-80">
            <canvas id="downloadChart"></canvas>
        </div>
    </div>

    <!-- 下载记录表格 -->
    <div class="content-card">
        <h2>下载记录</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else-if="logs.length === 0" class="text-center py-8 text-gray-400">
            <i class="fas fa-inbox text-4xl mb-3"></i>
            <p>暂无下载记录</p>
        </div>

        <div v-else>
            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>软件</th>
                            <th>IP地址</th>
                            <th>设备类型</th>
                            <th>操作系统</th>
                            <th>浏览器</th>
                            <th>下载时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="log in logs" :key="log.id">
                            <td>{{ log.id }}</td>
                            <td>{{ log.software_name }}</td>
                            <td>{{ log.ip }}</td>
                            <td>{{ log.device_type }}</td>
                            <td>{{ log.os }}</td>
                            <td>{{ log.browser }}</td>
                            <td>{{ formatTime(log.created_at) }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-400">
                    共 {{ pagination.totalItems }} 条记录，第 {{ pagination.page }}/{{ pagination.totalPages }} 页
                </div>
                <div class="flex space-x-2">
                    <button @click="changePage(1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button @click="changePage(pagination.page - 1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button @click="changePage(pagination.page + 1)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button @click="changePage(pagination.totalPages)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 页面内容结束 -->

<script src="<?php echo $adminConfig['assets_path']; ?>libs/chart.js/chart.min.js"></script>

<script>
    new Vue({
        el: '#statsApp',
        data: {
            loading: true,
            timeRange: 'month',
            startDate: '',
            endDate: '',
            selectedSoftware: '',
            softwareList: [],
            logs: [],
            pagination: {
                page: 1,
                pageSize: 10,
                totalItems: 0,
                totalPages: 1
            },
            summary: {
                totalDownloads: 0,
                uniqueIPs: 0,
                mobileDownloads: 0,
                desktopDownloads: 0,
                mobilePercentage: 0,
                desktopPercentage: 0
            },
            chartData: {
                labels: [],
                datasets: []
            },
            chart: null
        },
        mounted() {
            // 设置默认日期范围（本月）
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            this.startDate = this.formatDateForInput(firstDay);
            this.endDate = this.formatDateForInput(now);

            // 加载软件列表
            this.loadSoftwareList();

            // 加载统计数据
            this.loadStats();
        },
        methods: {
            // 格式化日期为输入框格式 (YYYY-MM-DD)
            formatDateForInput(date) {
                return date.toISOString().split('T')[0];
            },

            // 格式化时间戳
            formatTime(timestamp) {
                if (!timestamp) return '未知';
                const date = new Date(timestamp * 1000);
                return date.toLocaleString();
            },

            // 加载软件列表
            async loadSoftwareList() {
                try {
                    const data = await apiGet('<?php echo $apiPath; ?>panel/software.php');

                    if (data && data.success) {
                        this.softwareList = data.data.items || [];
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }
                } catch (error) {
                    console.error('加载软件列表出错：', error);
                }
            },

            // 加载统计数据
            async loadStats() {
                this.loading = true;

                // 构建查询参数
                let params = `page=${this.pagination.page}&pageSize=${this.pagination.pageSize}&timeRange=${this.timeRange}`;

                if (this.timeRange === 'custom') {
                    params += `&startDate=${this.startDate}&endDate=${this.endDate}`;
                }

                if (this.selectedSoftware) {
                    params += `&software=${this.selectedSoftware}`;
                }

                try {
                    // 加载下载记录
                    const data = await apiGet(`<?php echo $apiPath; ?>panel/stats.php?${params}`);

                    if (data && data.success) {
                        // 更新下载记录
                        this.logs = data.data.logs || [];
                        this.pagination = data.data.pagination || this.pagination;

                        // 更新统计摘要
                        this.summary = data.data.summary || this.summary;

                        // 更新图表数据
                        this.chartData = data.data.chart || { labels: [], datasets: [] };

                        // 渲染图表
                        this.renderChart();
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }

                    this.loading = false;
                } catch (error) {
                    console.error('加载统计数据出错：', error);
                    this.loading = false;
                }
            },

            // 渲染图表
            renderChart() {
                // 如果没有数据，不渲染图表
                if (!this.chartData.labels.length) {
                    return;
                }

                // 如果图表已存在，销毁它
                if (this.chart) {
                    this.chart.destroy();
                }

                // 获取画布上下文
                const ctx = document.getElementById('downloadChart').getContext('2d');

                // 创建图表
                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: this.chartData.labels,
                        datasets: [
                            {
                                label: '下载量',
                                data: this.chartData.datasets[0].data,
                                backgroundColor: 'rgba(0, 123, 255, 0.2)',
                                borderColor: 'rgba(0, 123, 255, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                pointBackgroundColor: 'rgba(0, 123, 255, 1)',
                                pointRadius: 3
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            }
                        }
                    }
                });
            },

            // 切换页码
            changePage(page) {
                if (page < 1 || page > this.pagination.totalPages) {
                    return;
                }
                this.pagination.page = page;
                this.loadStats();
            },

            // 导出数据
            exportData() {
                // 构建查询参数
                let params = `export=true&timeRange=${this.timeRange}`;

                if (this.timeRange === 'custom') {
                    params += `&startDate=${this.startDate}&endDate=${this.endDate}`;
                }

                if (this.selectedSoftware) {
                    params += `&software=${this.selectedSoftware}`;
                }

                // 打开导出链接
                window.open(`<?php echo $apiPath; ?>panel/stats.php?${params}`);
            }
        }
    });
</script>

<?php
// 渲染页脚
render_footer();
?>
