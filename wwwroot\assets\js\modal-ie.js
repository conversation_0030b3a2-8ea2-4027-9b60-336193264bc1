/**
 * IE兼容的通用弹窗管理器
 * 使用ES5语法和构造函数模式替代ES6 class
 */

/**
 * 通用弹窗管理器构造函数 (IE兼容版本)
 */
function UniversalModal() {
    this.modal = document.getElementById('universalModal');
    this.content = document.getElementById('universalContent');
    this.currentType = null;
    this.currentData = null;

    // 检查元素是否存在
    if (!this.modal) {
        console.error('universalModal 元素未找到');
        return;
    }

    if (!this.content) {
        console.error('universalContent 元素未找到');
        return;
    }

    console.log('UniversalModal 构造函数完成，modal:', this.modal, 'content:', this.content);

    // 绑定点击外部关闭事件
    var self = this;
    this.modal.addEventListener('click', function(e) {
        if (e.target === self.modal) {
            self.hide();
        }
    });
}

/**
 * 显示弹窗
 * @param {string} type 弹窗类型
 * @param {object} data 数据
 */
UniversalModal.prototype.show = function(type, data) {
    data = data || {};
    console.log('UniversalModal.show 被调用，类型:', type, '数据:', data);

    if (!this.modal || !this.content) {
        console.error('弹窗元素未找到，无法显示');
        return;
    }

    this.currentType = type;
    this.currentData = data;

    // 根据类型生成内容
    var content = this.generateContent(type, data);
    console.log('生成的内容长度:', content.length);

    // 对于支付选择弹窗，内容已经包含完整的弹窗结构
    if (type === 'payment-options') {
        this.content.innerHTML = content;
        // 移除默认的弹窗内容样式，让新样式生效
        this.content.style.background = 'transparent';
        this.content.style.border = 'none';
        this.content.style.borderRadius = '0';
        this.content.style.padding = '0';
        this.content.style.boxShadow = 'none';
        this.content.style.animation = 'none'; // 移除默认的pulse动画
        this.content.style.width = 'auto'; // 让宽度自适应内容
        this.content.style.maxWidth = 'none'; // 移除最大宽度限制
    } else {
        this.content.innerHTML = content;
        // 恢复其他弹窗的默认样式
        this.content.style.background = 'linear-gradient(135deg, #001133, #003366)';
        this.content.style.border = '1px solid #00aaff';
        this.content.style.borderRadius = '10px';
        this.content.style.padding = '1.5rem';
        this.content.style.boxShadow = '0 0 30px rgba(0, 170, 255, 0.5)';
        this.content.style.animation = 'pulse 2s infinite'; // 恢复pulse动画
        this.content.style.width = '500px'; // 恢复固定宽度
        this.content.style.maxWidth = '90%'; // 恢复最大宽度限制
    }

    // 显示弹窗
    this.modal.style.display = 'flex';
    this.modal.style.display = '-ms-flexbox'; // IE10+
    var self = this;
    setTimeout(function() {
        self.modal.style.opacity = '1';
    }, 10);

    // 绑定事件
    this.bindEvents(type, data);

    console.log('弹窗显示完成');
};

/**
 * 隐藏弹窗
 */
UniversalModal.prototype.hide = function() {
    var self = this;
    this.modal.style.opacity = '0';
    setTimeout(function() {
        self.modal.style.display = 'none';
        self.content.innerHTML = '';
        self.currentType = null;
        self.currentData = null;
    }, 300);
};

/**
 * 根据类型生成内容
 * @param {string} type 弹窗类型
 * @param {object} data 数据
 * @returns {string} HTML内容
 */
UniversalModal.prototype.generateContent = function(type, data) {
    switch (type) {
        case 'download':
            return this.generateDownloadContent(data);
        case 'payment-options':
            return this.generatePaymentOptionsContent(data);
        case 'payment-qr':
            return this.generatePaymentQRContent(data);
        case 'announcement':
            return this.generateAnnouncementContent(data);
        default:
            return '<p>未知的弹窗类型</p>';
    }
};

/**
 * 生成下载选项内容
 */
UniversalModal.prototype.generateDownloadContent = function(data) {
    var software = data.software;

    return '<span class="modal-close" id="modalClose" style="position: absolute; top: 10px; right: 15px; color: #aaccff; cursor: pointer; font-size: 1.5rem; font-weight: bold; line-height: 1;">&times;</span>' +
           '<h3 style="color: #00ffff; font-size: 1.3rem; margin-bottom: 1rem; text-shadow: 0 0 10px #00aaff; margin-top: 0;">选择下载方式</h3>' +
           '<div style="color: #aaccff; font-size: 0.9rem; margin: 1rem 0; padding: 0.8rem; background: rgba(0, 50, 100, 0.3); border-radius: 6px; border-left: 3px solid #00aaff; text-align: left;">' +
               '<p style="margin: 0 0 0.5rem 0;"><strong>下载说明：</strong></p>' +
               '<p style="margin: 0 0 0.3rem 0;">1. 请根据您的网络环境选择合适的下载方式</p>' +
               '<p style="margin: 0 0 0.3rem 0;">2. 下载过程中请勿关闭页面</p>' +
               '<p style="margin: 0 0 0.3rem 0;">3. 如遇下载失败，请尝试其他下载方式</p>' +
               '<p style="margin: 0;">4. 下载完成后请检查文件完整性</p>' +
           '</div>' +
           '<div id="downloadOptions" style="display: flex; flex-direction: column; gap: 0.8rem; margin-bottom: 1.5rem;">' +
               '<!-- 下载选项将在这里动态添加 -->' +
           '</div>' +
           '<button id="modalCancel" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">取消</button>';
};

/**
 * 生成支付选择内容
 */
UniversalModal.prototype.generatePaymentOptionsContent = function(data) {
    var software = data.software;
    var availablePayments = data.availablePayments;

    // 获取支付按钮布局配置
    var buttonLayout = (typeof paymentConfig !== 'undefined' && paymentConfig.button_layout)
        ? paymentConfig.button_layout
        : 'horizontal'; // 默认使用水平布局

    // 根据布局决定容器样式（使用Flexbox兼容早期Chrome，避免CSS Grid兼容性问题）
    var containerStyle = buttonLayout === 'horizontal'
        ? 'display: -ms-flexbox; display: flex; -ms-flex-direction: row; flex-direction: row; margin-top: 1.5rem;'
        : 'display: -ms-flexbox; display: flex; -ms-flex-direction: column; flex-direction: column; margin-top: 1.5rem;';

    // 根据布局决定按钮样式
    var wechatButtonStyle = buttonLayout === 'horizontal'
        ? '-ms-flex: 1; flex: 1; margin-right: 0.5rem;'
        : 'margin-bottom: 0.5rem;';

    var alipayButtonStyle = buttonLayout === 'horizontal'
        ? '-ms-flex: 1; flex: 1; margin-left: 0.5rem;'
        : 'margin-bottom: 0.5rem;';

    var wechatPayButton = '';
    var alipayButton = '';

    // 检查是否包含微信支付
    if (availablePayments.indexOf('wechat_pay') !== -1) {
        wechatPayButton = '<button id="wechat-pay-btn" style="' +
            'background-color: #059669;' +
            'color: white;' +
            'padding: 1rem 2rem;' +
            'border-radius: 6px;' +
            'border: none;' +
            'cursor: pointer;' +
            'display: -ms-flexbox;' +
            'display: flex;' +
            '-ms-flex-align: center;' +
            'align-items: center;' +
            '-ms-flex-pack: center;' +
            'justify-content: center;' +
            'transition: background-color 0.2s ease;' +
            'font-size: 0.9rem;' +
            'font-weight: 500;' +
            wechatButtonStyle +
        '">' +
            '<i class="fab fa-weixin" style="font-size: 1.5rem; margin-right: 0.5rem;"></i>' +
            '<span>微信支付</span>' +
        '</button>';
    }

    // 检查是否包含支付宝
    if (availablePayments.indexOf('alipay') !== -1) {
        alipayButton = '<button id="alipay-btn" style="' +
            'background-color: #2563eb;' +
            'color: white;' +
            'padding: 1rem 2rem;' +
            'border-radius: 6px;' +
            'border: none;' +
            'cursor: pointer;' +
            'display: -ms-flexbox;' +
            'display: flex;' +
            '-ms-flex-align: center;' +
            'align-items: center;' +
            '-ms-flex-pack: center;' +
            'justify-content: center;' +
            'transition: background-color 0.2s ease;' +
            'font-size: 0.9rem;' +
            'font-weight: 500;' +
            alipayButtonStyle +
        '">' +
            '<i class="fab fa-alipay" style="font-size: 1.5rem; margin-right: 0.5rem;"></i>' +
            '<span>支付宝</span>' +
        '</button>';
    }

    var priceText = software.price || (software.price_value ? software.price_value.toFixed(2) + '元' : '免费');

    return '<div style="' +
        'background-color: #111827;' +
        'border-radius: 8px;' +
        'box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 0 30px rgba(0, 170, 255, 0.3), 0 0 60px rgba(0, 170, 255, 0.1);' +
        'border: 1px solid rgba(0, 170, 255, 0.3);' +
        'width: 100%;' +
        'max-width: 700px;' +
        'padding: 1.5rem;' +
        'position: relative;' +
        'margin: 0 auto;' +
        'animation: paymentPulse 3s ease-in-out infinite;' +
    '">' +
        '<button id="modalClose" style="' +
            'position: absolute;' +
            'top: 0.75rem;' +
            'right: 0.75rem;' +
            'color: #9ca3af;' +
            'background: none;' +
            'border: none;' +
            'cursor: pointer;' +
            'font-size: 1.25rem;' +
            'transition: color 0.2s ease;' +
        '">' +
            '<i class="fas fa-times"></i>' +
        '</button>' +
        '<div style="text-align: center; margin-bottom: 1rem; width: 400px;">' +
            '<h3 style="font-size: 1.25rem; color: #93c5fd; font-weight: 600; margin: 0 0 0.5rem 0;">选择支付方式</h3>' +
            '<p style="margin: 0.5rem 0; color: #d1d5db; font-size: 0.9rem;">软件: ' + software.name + '</p>' +
            '<p style="color: #fbbf24; font-weight: 500; margin: 0; font-size: 1rem;">价格: ' + priceText + '</p>' +
        '</div>' +
        '<div style="' + containerStyle + '">' +
            wechatPayButton +
            alipayButton +
        '</div>' +
        '<div style="margin-top: 1.5rem; text-align: center; color: #9ca3af; font-size: 0.875rem;">' +
            '<p style="margin: 0;">支付完成后将自动显示下载页面</p>' +
        '</div>' +
        '<div style="margin-top: 1rem; text-align: center;">' +
            '<button id="modalCancel" style="color: #9ca3af; background: none; border: none; cursor: pointer; font-size: 0.875rem; transition: color 0.2s ease;">取消</button>' +
        '</div>' +
    '</div>';
};

/**
 * 生成支付二维码内容
 */
UniversalModal.prototype.generatePaymentQRContent = function(data) {
    var orderData = data.orderData;
    var paymentTypeName = orderData.payment_type === 'wechat_pay' ? '微信支付' : '支付宝';
    var scanAppName = orderData.payment_type === 'wechat_pay' ? '微信' : '支付宝';

    return '<span class="modal-close" id="modalClose" style="position: absolute; top: 10px; right: 15px; color: #aaccff; cursor: pointer; font-size: 1.5rem; font-weight: bold; line-height: 1;">&times;</span>' +
           '<h3 style="color: #00ffff; font-size: 1.3rem; margin-bottom: 1rem; text-shadow: 0 0 10px #00aaff; margin-top: 0;">请扫码支付</h3>' +
           '<div style="color: #aaccff; font-size: 0.9rem; margin: 1rem 0; padding: 0.8rem; background: rgba(0, 50, 100, 0.3); border-radius: 6px; border-left: 3px solid #00aaff; text-align: left;">' +
               '<p style="margin: 0 0 0.5rem 0;"><strong>订单号:</strong> ' + orderData.out_trade_no + '</p>' +
               '<p style="margin: 0 0 0.5rem 0;"><strong>支付方式:</strong> ' + paymentTypeName + '</p>' +
               '<p style="margin: 0;"><strong>说明:</strong> 请使用' + scanAppName + '扫描二维码完成支付</p>' +
           '</div>' +
           '<div style="display: flex; justify-content: center; margin: 1.5rem 0;">' +
               '<div id="qrcode-container" style="background: white; padding: 10px; border-radius: 8px; border: 2px solid #00aaff; box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);"></div>' +
           '</div>' +
           '<div id="payment-status" style="margin: 1rem 0; text-align: center; display: none;">' +
               '<div style="display: inline-block; padding: 0.5rem 1rem; border-radius: 20px; background: rgba(0, 100, 255, 0.2); color: #00ccff; border: 1px solid #334477;">' +
                   '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i> 检查支付状态...' +
               '</div>' +
           '</div>' +
           '<div style="display: flex; gap: 1rem; justify-content: center;">' +
               '<button id="payment-back" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">返回</button>' +
               '<button id="modalCancel" style="background: rgba(255, 100, 100, 0.3); color: #ffaaaa; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">取消</button>' +
           '</div>';
};

/**
 * 生成公告内容
 */
UniversalModal.prototype.generateAnnouncementContent = function(data) {
    var announcement = data.announcement;

    return '<span class="modal-close" id="modalClose" style="position: absolute; top: 10px; right: 15px; color: #aaccff; cursor: pointer; font-size: 1.5rem; font-weight: bold; line-height: 1;">&times;</span>' +
           '<h3 style="color: #00ffff; font-size: 1.3rem; margin-bottom: 1rem; text-shadow: 0 0 10px #00aaff; margin-top: 0;">' + announcement.title + '</h3>' +
           '<div style="color: #aaccff; font-size: 0.9rem; line-height: 1.4; margin-bottom: 1.2rem; text-align: left;">' +
               announcement.content +
           '</div>' +
           '<button id="modalConfirm" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.6rem 1.2rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">确定</button>';
};

/**
 * 绑定事件
 */
UniversalModal.prototype.bindEvents = function(type, data) {
    var self = this;

    // 通用关闭按钮
    var closeBtn = document.getElementById('modalClose');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            self.hide();
        });
        this.addHoverEffect(closeBtn, '#ffffff', '#aaccff');
    }

    // 通用取消按钮
    var cancelBtn = document.getElementById('modalCancel');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            self.hide();
        });
        this.addHoverEffect(cancelBtn, 'rgba(0, 170, 255, 0.5)', 'rgba(0, 170, 255, 0.3)');
    }

    // 根据类型绑定特定事件
    switch (type) {
        case 'download':
            this.bindDownloadEvents(data);
            break;
        case 'payment-options':
            this.bindPaymentOptionsEvents(data);
            break;
        case 'payment-qr':
            this.bindPaymentQREvents(data);
            break;
        case 'announcement':
            this.bindAnnouncementEvents(data);
            break;
    }
};

/**
 * 添加hover效果
 */
UniversalModal.prototype.addHoverEffect = function(element, hoverValue, normalValue, property) {
    property = property || 'color';
    element.addEventListener('mouseenter', function() {
        element.style[property] = hoverValue;
    });
    element.addEventListener('mouseleave', function() {
        element.style[property] = normalValue;
    });
};

/**
 * 绑定下载事件
 */
UniversalModal.prototype.bindDownloadEvents = function(data) {
    var software = data.software;
    // 生成下载选项
    this.generateDownloadOptions(software);
};

/**
 * 绑定支付选择事件
 */
UniversalModal.prototype.bindPaymentOptionsEvents = function(data) {
    var software = data.software;
    var self = this;

    // 关闭按钮hover效果
    var closeBtn = document.getElementById('modalClose');
    if (closeBtn) {
        closeBtn.addEventListener('mouseenter', function() {
            closeBtn.style.color = '#ffffff';
        });
        closeBtn.addEventListener('mouseleave', function() {
            closeBtn.style.color = '#9ca3af';
        });
    }

    // 取消按钮hover效果
    var cancelBtn = document.getElementById('modalCancel');
    if (cancelBtn) {
        cancelBtn.addEventListener('mouseenter', function() {
            cancelBtn.style.color = '#ffffff';
        });
        cancelBtn.addEventListener('mouseleave', function() {
            cancelBtn.style.color = '#9ca3af';
        });
    }

    // 微信支付按钮
    var wechatPayBtn = document.getElementById('wechat-pay-btn');
    if (wechatPayBtn) {
        // 微信支付按钮的hover效果
        wechatPayBtn.addEventListener('mouseenter', function() {
            wechatPayBtn.style.backgroundColor = '#047857';
        });
        wechatPayBtn.addEventListener('mouseleave', function() {
            wechatPayBtn.style.backgroundColor = '#059669';
        });
        wechatPayBtn.addEventListener('click', function() {
            if (typeof paymentHandler !== 'undefined') {
                paymentHandler.createOrder(software.id, 'wechat_pay');
            }
        });
    }

    // 支付宝按钮
    var alipayBtn = document.getElementById('alipay-btn');
    if (alipayBtn) {
        // 支付宝按钮的hover效果
        alipayBtn.addEventListener('mouseenter', function() {
            alipayBtn.style.backgroundColor = '#1d4ed8';
        });
        alipayBtn.addEventListener('mouseleave', function() {
            alipayBtn.style.backgroundColor = '#2563eb';
        });
        alipayBtn.addEventListener('click', function() {
            if (typeof paymentHandler !== 'undefined') {
                // 显示加载动画
                self.showAlipayLoadingAnimation(alipayBtn);

                // 延迟一下再跳转，让用户看到动画
                setTimeout(function() {
                    paymentHandler.createOrder(software.id, 'alipay');
                }, 800);
            }
        });
    }
};

/**
 * 绑定支付二维码事件
 */
UniversalModal.prototype.bindPaymentQREvents = function(data) {
    var orderData = data.orderData;
    var self = this;

    // 返回按钮
    var backBtn = document.getElementById('payment-back');
    if (backBtn) {
        this.addHoverEffect(backBtn, 'rgba(0, 170, 255, 0.5)', 'rgba(0, 170, 255, 0.3)', 'background');
        backBtn.addEventListener('click', function() {
            // 停止支付状态检查
            if (paymentHandler) {
                paymentHandler.stopCheckStatus();
            }

            // 返回支付选择界面
            if (self.currentData && self.currentData.software) {
                var availablePayments = paymentHandler.getAvailablePayments();
                self.show('payment-options', {
                    software: self.currentData.software,
                    availablePayments: availablePayments
                });
            }
        });
    }

    // 生成二维码
    this.generateQRCode(orderData);

    // 开始支付状态检查
    if (typeof paymentHandler !== 'undefined') {
        paymentHandler.startCheckStatus(orderData.out_trade_no);
    }
};

/**
 * 绑定公告事件
 */
UniversalModal.prototype.bindAnnouncementEvents = function(data) {
    var self = this;

    // 确定按钮
    var confirmBtn = document.getElementById('modalConfirm');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            self.hide();
        });
        this.addHoverEffect(confirmBtn, 'rgba(0, 170, 255, 0.5)', 'rgba(0, 170, 255, 0.3)', 'background');
    }
};

/**
 * 显示支付宝加载动画
 */
UniversalModal.prototype.showAlipayLoadingAnimation = function(button) {
    // 禁用按钮
    button.disabled = true;
    button.style.opacity = '0.8';
    button.style.cursor = 'not-allowed';

    // 更改按钮内容
    button.innerHTML = '<i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; margin-right: 0.5rem;"></i><span>正在跳转...</span>';

    // 添加脉冲动画
    button.style.animation = 'alipayPulse 1s ease-in-out infinite';
};

/**
 * 生成下载选项
 */
UniversalModal.prototype.generateDownloadOptions = function(software) {
    var container = document.getElementById('downloadOptions');
    if (!container) return;

    container.innerHTML = '';

    // 获取下载配置
    var downloadConfig = window.downloadConfig || {};

    // 遍历5个下载URL字段
    for (var i = 1; i <= 5; i++) {
        var urlField = 'download_url_' + i;
        var url = software[urlField];

        if (url && url.trim()) {
            // 获取配置或使用默认值
            var config = downloadConfig[i] || this.getDefaultDownloadConfig(i);

            var optionHtml = this.createDownloadOptionHtml(software, i, config, url);
            container.innerHTML += optionHtml;
        }
    }

    // 绑定下载选项事件
    this.bindDownloadOptionEvents(software);
};

/**
 * 获取默认下载配置
 */
UniversalModal.prototype.getDefaultDownloadConfig = function(index) {
    var defaultConfigs = {
        1: { name: '本地下载', icon: 'fas fa-download', description: '从本地服务器下载最新版本', useDownloadUrl: true },
        2: { name: '备用下载', icon: 'fas fa-cloud-download-alt', description: '备用下载服务器，网络不佳时推荐', useDownloadUrl: false },
        3: { name: '百度网盘', icon: 'fab fa-baidu', description: '适合大文件下载，需登录百度账号', useDownloadUrl: false },
        4: { name: '下载方式4', icon: 'fas fa-link', description: '扩展下载方式', useDownloadUrl: false },
        5: { name: '下载方式5', icon: 'fas fa-external-link-alt', description: '扩展下载方式', useDownloadUrl: false }
    };
    return defaultConfigs[index] || defaultConfigs[1];
};

/**
 * 创建下载选项HTML
 */
UniversalModal.prototype.createDownloadOptionHtml = function(software, index, config, url) {
    return '<div class="download-option" style="display: flex; align-items: center; padding: 0.8rem; background: rgba(0, 100, 255, 0.2); border-radius: 6px; border: 1px solid #334477; transition: all 0.3s ease; cursor: pointer;">' +
        '<div class="download-option-icon" style="width: 30px; height: 30px; margin-right: 1rem; display: flex; align-items: center; justify-content: center; background: rgba(0, 170, 255, 0.2); border-radius: 50%; color: #00aaff;">' +
            '<i class="' + config.icon + '"></i>' +
        '</div>' +
        '<div class="download-option-text" style="flex: 1; text-align: left;">' +
            '<div class="download-option-name" style="color: #00ccff; font-weight: bold; margin-bottom: 0.2rem;">' + config.name + '</div>' +
            '<div class="download-option-desc" style="color: #aaccff; font-size: 0.8rem;">' + config.description + '</div>' +
        '</div>' +
        '<div class="download-option-buttons" style="display: flex; gap: 0.5rem;">' +
            '<button class="download-copy-btn" data-url="' + url + '" data-index="' + index + '" style="background: rgba(0, 170, 255, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.3rem 0.6rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.8rem;">复制链接</button>' +
            '<button class="download-now-btn" data-software-id="' + software.id + '" data-index="' + index + '" data-use-download-url="' + (config.useDownloadUrl ? 'true' : 'false') + '" data-url="' + url + '" style="background: rgba(0, 200, 100, 0.3); color: #00ffff; border: none; border-radius: 4px; padding: 0.3rem 0.6rem; cursor: pointer; transition: all 0.3s ease; font-size: 0.8rem;">立即下载</button>' +
        '</div>' +
    '</div>';
};

/**
 * 绑定下载选项事件
 */
UniversalModal.prototype.bindDownloadOptionEvents = function(software) {
    var self = this;

    // 复制链接按钮
    var copyBtns = document.querySelectorAll('.download-copy-btn');
    for (var i = 0; i < copyBtns.length; i++) {
        copyBtns[i].addEventListener('click', function(e) {
            e.stopPropagation();
            var url = this.getAttribute('data-url');
            var index = this.getAttribute('data-index');

            // 构建完整URL
            var fullUrl = url;
            if (window.siteConfig && window.siteConfig.frontendUrl && !url.match(/^https?:\/\//)) {
                fullUrl = window.siteConfig.frontendUrl.replace(/\/$/, '') + '/' + url.replace(/^\//, '');
            }

            self.copyToClipboard(fullUrl, this);
        });

        // hover效果
        copyBtns[i].addEventListener('mouseenter', function() {
            this.style.background = 'rgba(0, 170, 255, 0.5)';
        });
        copyBtns[i].addEventListener('mouseleave', function() {
            this.style.background = 'rgba(0, 170, 255, 0.3)';
        });
    }

    // 立即下载按钮
    var downloadBtns = document.querySelectorAll('.download-now-btn');
    for (var i = 0; i < downloadBtns.length; i++) {
        downloadBtns[i].addEventListener('click', function(e) {
            e.stopPropagation();
            var softwareId = this.getAttribute('data-software-id');
            var index = this.getAttribute('data-index');
            var useDownloadUrl = this.getAttribute('data-use-download-url') === 'true';
            var url = this.getAttribute('data-url');

            if (useDownloadUrl) {
                // 使用系统下载URL
                var downloadUrl = self.getDownloadUrl(softwareId, index);
                window.open(downloadUrl, '_blank');
            } else {
                // 直接使用原始URL
                window.open(url, '_blank');
            }
        });

        // hover效果
        downloadBtns[i].addEventListener('mouseenter', function() {
            this.style.background = 'rgba(0, 200, 100, 0.5)';
        });
        downloadBtns[i].addEventListener('mouseleave', function() {
            this.style.background = 'rgba(0, 200, 100, 0.3)';
        });
    }

    // 下载选项hover效果
    var options = document.querySelectorAll('.download-option');
    for (var i = 0; i < options.length; i++) {
        options[i].addEventListener('mouseenter', function() {
            this.style.background = 'rgba(0, 170, 255, 0.3)';
            this.style.borderColor = '#00aaff';
        });
        options[i].addEventListener('mouseleave', function() {
            this.style.background = 'rgba(0, 100, 255, 0.2)';
            this.style.borderColor = '#334477';
        });
    }
};

/**
 * 复制到剪贴板
 */
UniversalModal.prototype.copyToClipboard = function(text, button) {
    // 尝试使用现代API
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(function() {
            button.textContent = '已复制';
            setTimeout(function() {
                button.textContent = '复制链接';
            }, 2000);
        }).catch(function() {
            // 降级到传统方法
            this.fallbackCopyToClipboard(text, button);
        }.bind(this));
    } else {
        // 使用传统方法
        this.fallbackCopyToClipboard(text, button);
    }
};

/**
 * 传统复制方法 (IE兼容)
 */
UniversalModal.prototype.fallbackCopyToClipboard = function(text, button) {
    var textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        var successful = document.execCommand('copy');
        if (successful) {
            button.textContent = '已复制';
            setTimeout(function() {
                button.textContent = '复制链接';
            }, 2000);
        } else {
            alert('复制失败，请手动复制链接：' + text);
        }
    } catch (err) {
        alert('复制失败，请手动复制链接：' + text);
    }

    document.body.removeChild(textArea);
};

/**
 * 获取下载URL
 */
UniversalModal.prototype.getDownloadUrl = function(softwareId, urlIndex) {
    urlIndex = urlIndex || 1;

    // 使用当前时间戳（秒级）
    var timestamp = Math.floor(Date.now() / 1000);

    // 构建令牌字符串：软件ID_时间戳
    var tokenStr = softwareId + '_' + timestamp;

    console.log('生成下载令牌:', tokenStr); // 调试信息

    // 使用MD5生成令牌
    var token = '';
    try {
        // 使用JavaScript的MD5库，如果可用的话
        if (typeof md5 === 'function') {
            token = md5(tokenStr);
        } else {
            // 简单替代方案，非生产环境使用
            console.error('MD5函数不可用，使用替代方案');
            token = btoa(tokenStr);
        }
    } catch (e) {
        console.error('生成令牌出错:', e);
        // 出错情况下的简单替代方案
        token = btoa(tokenStr);
    }

    return '/api/public/download.php?id=' + softwareId + '&token=' + token + '&ts=' + timestamp + '&url_index=' + urlIndex;
};

/**
 * 生成二维码
 */
UniversalModal.prototype.generateQRCode = function(orderData) {
    var container = document.getElementById('qrcode-container');
    if (!container) return;

    // 清空容器
    container.innerHTML = '';

    // 创建canvas元素
    var canvas = document.createElement('canvas');
    container.appendChild(canvas);

    // 生成二维码
    if (typeof QRCode !== 'undefined' && QRCode.toCanvas) {
        QRCode.toCanvas(canvas, orderData.code_url, {
            width: 200,
            height: 200,
            margin: 1
        }, function(error) {
            if (error) {
                console.error('生成二维码失败:', error);
                container.innerHTML = '<div style="width: 200px; height: 200px; display: flex; align-items: center; justify-content: center; background: #f0f0f0; color: #666;">二维码生成失败</div>';
            }
        });
    } else {
        // 如果QRCode库不可用，显示文本链接
        container.innerHTML = '<div style="width: 200px; height: 200px; display: flex; align-items: center; justify-content: center; background: #f0f0f0; color: #666; text-align: center; padding: 1rem; font-size: 0.8rem;">二维码库未加载<br><a href="' + orderData.code_url + '" target="_blank" style="color: #00aaff;">点击此处支付</a></div>';
    }
};

// 创建全局实例
var universalModal;

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        universalModal = new UniversalModal();
        console.log('UniversalModal 全局实例已创建');
    });
} else {
    universalModal = new UniversalModal();
    console.log('UniversalModal 全局实例已创建');
}

console.log('IE兼容的通用弹窗管理器已加载');
