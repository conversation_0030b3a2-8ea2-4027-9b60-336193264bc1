<?php
/**
 * 管理后台公共函数库
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 定义管理后台根目录常量
if (!defined('__ADMIN_ROOT__')) {
    define('__ADMIN_ROOT__', __DIR__);
}

/**
 * 获取组件配置信息
 *
 * @return array 组件配置信息
 */
function get_config() {
    // 加载管理后台配置
    return include __ADMIN_ROOT__ . DS . 'config.php';
}

/**
 * 获取站点设置信息
 *
 * @return array 站点设置信息
 */
function get_settings() {
    // 加载管理后台配置
    $adminConfig = include __ADMIN_ROOT__ . DS . 'config.php';

    // 获取站点设置文件路径
    if (!isset($adminConfig['site_settings_path'])) {
        throw new RuntimeException('no site_settings_path in adminConfig?');
    }

    // 确保Settings类已加载
    if (!class_exists('Settings')) {
        require_once dirname(__ADMIN_ROOT__) . DS . 'includes' . DS . 'Settings.php';
        Settings::setSettingsPath($adminConfig['site_settings_path']);
    } else {
        // 如果类已存在，只需设置路径
        Settings::setSettingsPath($adminConfig['site_settings_path']);
    }

    // 加载站点设置
    return Settings::load();
}

/**
 * 返回JSON响应
 *
 * @param array $data 响应数据
 * @param int $statusCode HTTP状态码
 */
function json_response($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * 格式化时间戳为可读时间
 *
 * @param int $timestamp 时间戳
 * @return string 格式化后的时间
 */
function format_time($timestamp) {
    if (!$timestamp) {
        return '未知';
    }
    return date('Y-m-d H:i:s', $timestamp);
}

/**
 * 生成随机字符串
 *
 * @param int $length 字符串长度
 * @return string 随机字符串
 */
function generate_random_string($length = 16) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * 获取JSON请求数据
 *
 * @return array 解析后的JSON数据
 */
function get_json_data() {
    $json = file_get_contents('php://input');
    return json_decode($json, true) ?: [];
}

/**
 * 防止直接访问文件
 * 如果直接访问，则退出脚本
 */
function prevent_direct_access() {
    if (basename($_SERVER['SCRIPT_FILENAME']) === basename($_SERVER['PHP_SELF'])) {
        header('HTTP/1.0 403 Forbidden');
        exit('禁止直接访问此文件');
    }
}

/**
 * 检查站点是否已安装
 * 如果未安装，则重定向到安装页面
 */
function check_installed() {
    // 加载管理后台配置
    $adminConfig = include __ADMIN_ROOT__ . DS . 'config.php';

    // 确保Settings类已加载
    if (!class_exists('Settings')) {
        require_once dirname(__ADMIN_ROOT__) . DS . 'includes' . DS . 'Settings.php';
        Settings::setSettingsPath($adminConfig['site_settings_path']);
    }

    // 检查是否已安装
    if (!Settings::isInstalled()) {
        // 获取安装页面URL
        $installUrl = isset($adminConfig['install_url']) ? $adminConfig['install_url'] : '/install.php';

        // 如果是AJAX请求，返回JSON响应
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            json_response([
                'success' => false,
                'redirect' => $installUrl,
                'message' => '系统尚未安装，请先完成安装'
            ], 303);
        }

        // 否则重定向到安装页面
        header('Location: ' . $installUrl);
        exit;
    }
}
