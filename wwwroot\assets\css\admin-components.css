/**
 * 管理后台组件样式文件
 * 包含各个组件的专用样式
 */

/* 阴影效果 */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06); }
.shadow-none { box-shadow: none; }

/* 透明度 */
.opacity-0 { opacity: 0; }
.opacity-5 { opacity: 0.05; }
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-25 { opacity: 0.25; }
.opacity-30 { opacity: 0.3; }
.opacity-40 { opacity: 0.4; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-70 { opacity: 0.7; }
.opacity-75 { opacity: 0.75; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }
.opacity-95 { opacity: 0.95; }
.opacity-100 { opacity: 1; }

/* 鼠标样式 */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

/* 过渡效果 */
.transition-none { transition-property: none; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition { transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: background-color, border-color, color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

/* 持续时间 */
.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.duration-700 { transition-duration: 700ms; }
.duration-1000 { transition-duration: 1000ms; }

/* 缓动函数 */
.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* 响应式断点 */
@media (min-width: 640px) {
    .sm\:block { display: block; }
    .sm\:flex { display: flex; }
    .sm\:hidden { display: none; }
    .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
    .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
    .sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
}

@media (min-width: 768px) {
    .md\:block { display: block; }
    .md\:flex { display: flex; }
    .md\:hidden { display: none; }
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .md\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
    .md\:text-base { font-size: 1rem; line-height: 1.5rem; }
    .md\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
    .md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
}

@media (min-width: 1024px) {
    .lg\:block { display: block; }
    .lg\:flex { display: flex; }
    .lg\:hidden { display: none; }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    .lg\:text-base { font-size: 1rem; line-height: 1.5rem; }
    .lg\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
    .lg\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
}

@media (min-width: 1280px) {
    .xl\:block { display: block; }
    .xl\:flex { display: flex; }
    .xl\:hidden { display: none; }
    .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .xl\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
    .xl\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
    .xl\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
}

/* 悬停效果 */
.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:bg-gray-700:hover { background-color: #374151; }
.hover\:bg-gray-800:hover { background-color: #1f2937; }
.hover\:bg-blue-50:hover { background-color: #eff6ff; }
.hover\:bg-blue-100:hover { background-color: #dbeafe; }
.hover\:bg-blue-600:hover { background-color: #2563eb; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-green-600:hover { background-color: #16a34a; }
.hover\:bg-green-700:hover { background-color: #15803d; }
.hover\:bg-red-600:hover { background-color: #dc2626; }
.hover\:bg-red-700:hover { background-color: #b91c1c; }
.hover\:bg-yellow-600:hover { background-color: #ca8a04; }
.hover\:bg-yellow-700:hover { background-color: #a16207; }

.hover\:text-white:hover { color: #ffffff; }
.hover\:text-gray-900:hover { color: #111827; }
.hover\:text-blue-300:hover { color: #93c5fd; }
.hover\:text-blue-400:hover { color: #60a5fa; }
.hover\:text-green-300:hover { color: #86efac; }
.hover\:text-green-400:hover { color: #4ade80; }
.hover\:text-red-300:hover { color: #fca5a5; }
.hover\:text-red-400:hover { color: #f87171; }
.hover\:text-yellow-300:hover { color: #fde047; }
.hover\:text-yellow-400:hover { color: #facc15; }

.hover\:border-gray-300:hover { border-color: #d1d5db; }
.hover\:border-blue-400:hover { border-color: #60a5fa; }
.hover\:border-blue-500:hover { border-color: #3b82f6; }

.hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* 焦点效果 */
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-blue-500:focus { --tw-ring-opacity: 1; --tw-ring-color: rgba(59, 130, 246, var(--tw-ring-opacity)); }
.focus\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }

/* 禁用状态 */
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.disabled\:bg-gray-300:disabled { background-color: #d1d5db; }
.disabled\:text-gray-500:disabled { color: #6b7280; }

/* 动画 */
@keyframes spin {
    to { transform: rotate(360deg); }
}
@keyframes ping {
    75%, 100% { transform: scale(2); opacity: 0; }
}
@keyframes pulse {
    50% { opacity: .5; }
}
@keyframes bounce {
    0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8,0,1,1); }
    50% { transform: none; animation-timing-function: cubic-bezier(0,0,0.2,1); }
}

.animate-spin { animation: spin 1s linear infinite; }
.animate-ping { animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce { animation: bounce 1s infinite; }

/* FontAwesome 动画 */
.fa-spin { animation: fa-spin 2s infinite linear; }
@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 管理后台专用组件样式 */

/* 内容卡片 */
.content-card {
    background-color: #1f2937;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.content-card h2 {
    color: #93c5fd;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* 数据表格 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #1f2937;
    border-radius: 0.5rem;
    overflow: hidden;
}

.data-table th {
    background-color: #374151;
    color: #d1d5db;
    font-weight: 600;
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #4b5563;
}

.data-table td {
    color: #f3f4f6;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #374151;
}

.data-table tbody tr:hover {
    background-color: #374151;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* 表单输入 */
.form-input {
    background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 0.375rem;
    color: #f3f4f6;
    padding: 0.5rem 0.75rem;
    width: 100%;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

/* 表单组 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    color: #d1d5db;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
    border: none;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4b5563;
}

.btn-success {
    background-color: #22c55e;
    color: #ffffff;
}

.btn-success:hover:not(:disabled) {
    background-color: #16a34a;
}

.btn-danger {
    background-color: #ef4444;
    color: #ffffff;
}

.btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
}

.btn-warning {
    background-color: #eab308;
    color: #ffffff;
}

.btn-warning:hover:not(:disabled) {
    background-color: #ca8a04;
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #374151;
    border-top: 2px solid #60a5fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
