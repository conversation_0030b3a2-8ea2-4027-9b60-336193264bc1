<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

try {
    // 准备菜单项
    $menu = [
        // 仪表盘组
        [
            'id' => 'dashboard_group',
            'type' => 'group',
            'title' => '仪表盘',
            'items' => [
                [
                    'id' => 'dashboard',
                    'title' => '控制面板',
                    'icon' => 'fas fa-tachometer-alt',
                    'url' => 'panel/dashboard.html',
                    'permission' => null // 所有人都可以访问
                ],
                [
                    'id' => 'stats',
                    'title' => '下载统计',
                    'icon' => 'fas fa-chart-bar',
                    'url' => 'panel/stats.html',
                    'permission' => 'stats.view',
                    'badge' => $db->querySingle("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='download_logs'") > 0 ?
                               $db->querySingle("SELECT COUNT(*) FROM download_logs") : null
                ]
            ]
        ],

        // 内容管理组
        [
            'id' => 'content_group',
            'type' => 'group',
            'title' => '内容管理',
            'items' => [
                [
                    'id' => 'software',
                    'title' => '软件管理',
                    'icon' => 'fas fa-laptop-code',
                    'url' => 'panel/software.html',
                    'permission' => 'software.view',
                    'badge' => $db->querySingle('SELECT COUNT(*) FROM softwares')
                ],
                [
                    'id' => 'category',
                    'title' => '分类管理',
                    'icon' => 'fas fa-folder',
                    'url' => 'panel/category.html',
                    'permission' => 'category.view',
                    'badge' => $db->querySingle('SELECT COUNT(*) FROM categories')
                ],
                [
                    'id' => 'bulk',
                    'title' => '批量管理',
                    'icon' => 'fas fa-tasks',
                    'url' => 'panel/bulk-manage.html',
                    'permission' => 'software.edit'
                ],
                [
                    'id' => 'orders',
                    'title' => '订单管理',
                    'icon' => 'fas fa-shopping-cart',
                    'url' => 'panel/orders.html',
                    'permission' => 'orders.view',
                    'badge' => $db->querySingle('SELECT COUNT(*) FROM payment_orders')
                ],
                [
                    'id' => 'payment_notify',
                    'title' => '支付通知记录',
                    'icon' => 'fas fa-bell',
                    'url' => 'panel/payment-notify.html',
                    'permission' => 'payment_notify.view',
                    'badge' => $db->querySingle("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='payment_notify_logs'") > 0 ?
                               $db->querySingle("SELECT COUNT(*) FROM payment_notify_logs WHERE signature_valid = 0") : null
                ]
            ]
        ],

        // 系统管理组
        [
            'id' => 'system_group',
            'type' => 'group',
            'title' => '系统管理',
            'items' => [
                [
                    'id' => 'admin',
                    'title' => '管理员管理',
                    'icon' => 'fas fa-users-cog',
                    'url' => 'panel/admin.html',
                    'permission' => 'admin.view',
                    'badge' => $db->querySingle('SELECT COUNT(*) FROM admins')
                ],
                [
                    'id' => 'roles',
                    'title' => '角色管理',
                    'icon' => 'fas fa-user-shield',
                    'url' => 'panel/roles.html',
                    'permission' => 'role.view',
                    'badge' => $db->querySingle('SELECT COUNT(*) FROM admin_roles')
                ],
                [
                    'id' => 'settings',
                    'title' => '站点设置',
                    'icon' => 'fas fa-cog',
                    'url' => 'panel/settings.html',
                    'permission' => 'admin.view' // 通常只有管理员可以修改设置
                ]
            ]
        ]
    ];

    // 返回菜单数据
    json_response(['success' => true, 'data' => $menu]);

} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '获取菜单数据失败: ' . $e->getMessage()
    ], 500);
}
