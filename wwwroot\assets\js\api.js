/**
 * API 请求工具函数
 * 封装 fetch API，确保所有请求都包含正确的头信息
 * 并统一处理消息提示和加载状态
 */

/**
 * 全局加载遮罩管理
 */
const LoadingMask = {
    element: null,
    counter: 0,

    /**
     * 显示加载遮罩
     * @param {string} message 加载提示文本
     */
    show: function(message = '加载中...') {
        this.counter++;

        if (!this.element) {
            this.element = document.createElement('div');
            this.element.id = 'api-loading-mask';
            this.element.innerHTML = `
                <div class="loading-backdrop">
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">${message}</div>
                    </div>
                </div>
            `;

            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                #api-loading-mask {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                #api-loading-mask .loading-backdrop {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    backdrop-filter: blur(2px);
                }

                #api-loading-mask .loading-content {
                    position: relative;
                    background: rgba(20, 30, 60, 0.9);
                    border: 1px solid #00aaff;
                    border-radius: 8px;
                    padding: 2rem;
                    text-align: center;
                    box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);
                    min-width: 200px;
                }

                #api-loading-mask .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid rgba(0, 170, 255, 0.3);
                    border-top: 4px solid #00aaff;
                    border-radius: 50%;
                    animation: api-loading-spin 1s linear infinite;
                    margin: 0 auto 1rem;
                }

                #api-loading-mask .loading-text {
                    color: #00ccff;
                    font-size: 1rem;
                    font-weight: 500;
                }

                @keyframes api-loading-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;

            if (!document.getElementById('api-loading-mask-style')) {
                style.id = 'api-loading-mask-style';
                document.head.appendChild(style);
            }

            document.body.appendChild(this.element);
        } else {
            // 更新加载文本
            const textElement = this.element.querySelector('.loading-text');
            if (textElement) {
                textElement.textContent = message;
            }
            this.element.style.display = 'flex';
        }
    },

    /**
     * 隐藏加载遮罩
     */
    hide: function() {
        this.counter = Math.max(0, this.counter - 1);

        if (this.counter === 0 && this.element) {
            this.element.style.display = 'none';
        }
    },

    /**
     * 强制隐藏加载遮罩
     */
    forceHide: function() {
        this.counter = 0;
        if (this.element) {
            this.element.style.display = 'none';
        }
    }
};

/**
 * 显示消息提示
 *
 * @param {Object} data API返回的数据
 * @param {string} successMessage 成功时显示的消息，如果为null则不显示
 * @param {string} operation 操作类型，用于构建默认错误消息
 * @returns {boolean} 操作是否成功
 */
function showMessage(data, successMessage = null, operation = '操作') {
    if (!data) return false;

    // 如果有消息，显示消息
    if (data.message) {
        if (data.success) {
            // 成功消息
            alert(data.message);
            return true;
        } else {
            // 错误消息
            alert(data.message || `${operation}失败：未知错误`);
            return false;
        }
    } else if (data.success && successMessage) {
        // 如果成功且提供了成功消息，显示成功消息
        alert(successMessage);
        return true;
    } else if (!data.success) {
        // 如果失败但没有消息，显示默认错误消息
        alert(`${operation}失败：未知错误`);
        return false;
    }

    return data.success || false;
}

/**
 * 发送 GET 请求
 *
 * @param {string} url 请求 URL
 * @param {Object} requestData 请求数据（转换为查询参数）
 * @param {Object} options 额外的请求选项
 * @param {Object} messageOptions 消息选项 {showSuccess: boolean, successMessage: string, operation: string, showLoading: boolean, loadingMessage: string}
 * @returns {Promise} 请求结果
 */
async function apiGet(url, requestData = {}, options = {}, messageOptions = null) {
    // 处理加载遮罩选项
    const showLoading = messageOptions?.showLoading !== false; // 默认显示加载遮罩
    const loadingMessage = messageOptions?.loadingMessage || '加载中...';

    // 显示加载遮罩
    if (showLoading) {
        LoadingMask.show(loadingMessage);
    }

    try {
        // 构建查询参数
        if (requestData && Object.keys(requestData).length > 0) {
            const queryParams = new URLSearchParams();
            for (const key in requestData) {
                if (requestData[key] !== undefined && requestData[key] !== null) {
                    queryParams.append(key, requestData[key]);
                }
            }
            const queryString = queryParams.toString();
            if (queryString) {
                url += (url.includes('?') ? '&' : '?') + queryString;
            }
        }

        const defaultOptions = {
            headers: {
                'Accept': 'application/json'
            }
        };

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...(options.headers || {})
            }
        };

        const response = await fetch(url, mergedOptions);

        // 检查响应状态
        const responseData = await response.json();

        // 检查是否需要重定向（无论状态码如何）
        if (responseData.redirect) {
            window.location.href = responseData.redirect;
            return null;
        }

        // 检查是否需要重定向到登录页面
        if (response.status === 401) {
            window.location.href = '/admin/index.php/public/login.html';
            return null;
        }

        // 如果响应不成功，但有数据返回
        if (!response.ok) {
            console.error(`API 请求失败: ${response.status}`, responseData);

            // 处理服务器错误（500）的情况，显示详细错误信息
            if (response.status === 500 && responseData.error) {
                // 构建更友好的错误消息
                const errorMsg = responseData.error.message || '未知服务器错误';
                responseData.message = `${responseData.message || '服务器错误'}: ${errorMsg}`;

                // 记录详细错误信息到控制台
                console.error(`错误详情: ${responseData.error.type} 在 ${responseData.error.file} 第 ${responseData.error.line} 行`);
            }

            // 处理消息提示
            if (messageOptions) {
                const { operation = '获取' } = messageOptions;
                showMessage(responseData, null, operation);
            }

            return responseData;
        }

        // 处理消息提示
        if (messageOptions) {
            const { showSuccess = false, successMessage = null, operation = '获取' } = messageOptions;
            if (showSuccess || !responseData.success) {
                showMessage(responseData, successMessage, operation);
            }
        }

        return responseData;
    } catch (error) {
        console.error('API 请求出错:', error);
        if (messageOptions) {
            alert(`${messageOptions.operation || '获取'}请求出错，请重试`);
        }
        throw error;
    } finally {
        // 隐藏加载遮罩
        if (showLoading) {
            LoadingMask.hide();
        }
    }
}

/**
 * 发送 POST 请求
 *
 * @param {string} url 请求 URL
 * @param {Object} requestData 请求数据
 * @param {Object} options 额外的请求选项
 * @param {Object} messageOptions 消息选项 {showSuccess: boolean, successMessage: string, operation: string, showLoading: boolean, loadingMessage: string}
 * @returns {Promise} 请求结果
 */
async function apiPost(url, requestData, options = {}, messageOptions = null) {
    // 处理加载遮罩选项
    const showLoading = messageOptions?.showLoading !== false; // 默认显示加载遮罩
    const loadingMessage = messageOptions?.loadingMessage || '提交中...';

    // 显示加载遮罩
    if (showLoading) {
        LoadingMask.show(loadingMessage);
    }

    try {
        const defaultOptions = {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        };

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...(options.headers || {})
            }
        };

        const response = await fetch(url, mergedOptions);

        // 检查响应状态
        const responseData = await response.json();

        // 检查是否需要重定向（无论状态码如何）
        if (responseData.redirect) {
            window.location.href = responseData.redirect;
            return null;
        }

        // 检查是否需要重定向到登录页面
        if (response.status === 401) {
            window.location.href = '/admin/index.php/public/login.html';
            return null;
        }

        // 如果响应不成功，但有数据返回
        if (!response.ok) {
            console.error(`API 请求失败: ${response.status}`, responseData);

            // 处理服务器错误（500）的情况，显示详细错误信息
            if (response.status === 500 && responseData.error) {
                // 构建更友好的错误消息
                const errorMsg = responseData.error.message || '未知服务器错误';
                responseData.message = `${responseData.message || '服务器错误'}: ${errorMsg}`;

                // 记录详细错误信息到控制台
                console.error(`错误详情: ${responseData.error.type} 在 ${responseData.error.file} 第 ${responseData.error.line} 行`);
            }

            // 处理消息提示
            if (messageOptions) {
                const { operation = '提交' } = messageOptions;
                showMessage(responseData, null, operation);
            }

            return responseData;
        }

        // 处理消息提示
        if (messageOptions) {
            const { showSuccess = false, successMessage = null, operation = '提交' } = messageOptions;
            if (showSuccess || !responseData.success) {
                showMessage(responseData, successMessage, operation);
            }
        }

        return responseData;
    } catch (error) {
        console.error('API 请求出错:', error);
        if (messageOptions) {
            alert(`${messageOptions.operation || '提交'}请求出错，请重试`);
        }
        throw error;
    } finally {
        // 隐藏加载遮罩
        if (showLoading) {
            LoadingMask.hide();
        }
    }
}

/**
 * 发送 PUT 请求
 *
 * @param {string} url 请求 URL
 * @param {Object} requestData 请求数据
 * @param {Object} options 额外的请求选项
 * @param {Object} messageOptions 消息选项 {showSuccess: boolean, successMessage: string, operation: string, showLoading: boolean, loadingMessage: string}
 * @returns {Promise} 请求结果
 */
async function apiPut(url, requestData, options = {}, messageOptions = null) {
    // 设置默认的加载消息
    const putMessageOptions = {
        ...messageOptions,
        loadingMessage: messageOptions?.loadingMessage || '更新中...'
    };
    return apiPost(url, requestData, { ...options, method: 'PUT' }, putMessageOptions);
}

/**
 * 发送 DELETE 请求
 *
 * @param {string} url 请求 URL
 * @param {Object} requestData 请求数据（可以作为查询参数或请求体）
 * @param {Object} options 额外的请求选项
 * @param {Object} messageOptions 消息选项 {showSuccess: boolean, successMessage: string, operation: string, showLoading: boolean, loadingMessage: string}
 * @returns {Promise} 请求结果
 */
async function apiDelete(url, requestData = {}, options = {}, messageOptions = null) {
    // 处理加载遮罩选项
    const showLoading = messageOptions?.showLoading !== false; // 默认显示加载遮罩
    const loadingMessage = messageOptions?.loadingMessage || '删除中...';

    // 显示加载遮罩
    if (showLoading) {
        LoadingMask.show(loadingMessage);
    }

    try {
        // 构建查询参数或请求体
        if (requestData && Object.keys(requestData).length > 0) {
            // 如果options中指定了useBody为true，则将requestData作为请求体
            if (options.useBody) {
                options.body = JSON.stringify(requestData);
                options.headers = {
                    ...(options.headers || {}),
                    'Content-Type': 'application/json'
                };
            } else {
                // 否则作为查询参数
                const queryParams = new URLSearchParams();
                for (const key in requestData) {
                    if (requestData[key] !== undefined && requestData[key] !== null) {
                        queryParams.append(key, requestData[key]);
                    }
                }
                const queryString = queryParams.toString();
                if (queryString) {
                    url += (url.includes('?') ? '&' : '?') + queryString;
                }
            }
        }

        const defaultOptions = {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json'
            }
        };

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...(options.headers || {})
            }
        };

        const response = await fetch(url, mergedOptions);

        // 检查响应状态
        const responseData = await response.json();

        // 检查是否需要重定向（无论状态码如何）
        if (responseData.redirect) {
            window.location.href = responseData.redirect;
            return null;
        }

        // 检查是否需要重定向到登录页面
        if (response.status === 401) {
            window.location.href = '/admin/index.php/public/login.html';
            return null;
        }

        // 如果响应不成功，但有数据返回
        if (!response.ok) {
            console.error(`API 请求失败: ${response.status}`, responseData);

            // 处理服务器错误（500）的情况，显示详细错误信息
            if (response.status === 500 && responseData.error) {
                // 构建更友好的错误消息
                const errorMsg = responseData.error.message || '未知服务器错误';
                responseData.message = `${responseData.message || '服务器错误'}: ${errorMsg}`;

                // 记录详细错误信息到控制台
                console.error(`错误详情: ${responseData.error.type} 在 ${responseData.error.file} 第 ${responseData.error.line} 行`);
            }

            // 处理消息提示
            if (messageOptions) {
                const { operation = '删除' } = messageOptions;
                showMessage(responseData, null, operation);
            }

            return responseData;
        }

        // 处理消息提示
        if (messageOptions) {
            const { showSuccess = false, successMessage = null, operation = '删除' } = messageOptions;
            if (showSuccess || !responseData.success) {
                showMessage(responseData, successMessage, operation);
            }
        }

        return responseData;
    } catch (error) {
        console.error('API 请求出错:', error);
        if (messageOptions) {
            alert(`${messageOptions.operation || '删除'}请求出错，请重试`);
        }
        throw error;
    } finally {
        // 隐藏加载遮罩
        if (showLoading) {
            LoadingMask.hide();
        }
    }
}

/**
 * 便捷的全局加载遮罩函数
 */
window.showLoading = function(message = '加载中...') {
    LoadingMask.show(message);
};

window.hideLoading = function() {
    LoadingMask.hide();
};

window.forceHideLoading = function() {
    LoadingMask.forceHide();
};

/**
 * 使用说明：
 *
 * 1. 默认行为：
 *    所有API调用（apiGet、apiPost、apiPut、apiDelete）默认会显示加载遮罩
 *
 * 2. 禁用加载遮罩：
 *    apiGet(url, data, options, { showLoading: false })
 *
 * 3. 自定义加载文本：
 *    apiGet(url, data, options, { loadingMessage: '正在获取数据...' })
 *
 * 4. 手动控制加载遮罩：
 *    showLoading('自定义加载文本');
 *    hideLoading();
 *    forceHideLoading(); // 强制隐藏，忽略计数器
 *
 * 5. 示例：
 *    // 默认显示"加载中..."
 *    const data = await apiGet('/api/data');
 *
 *    // 自定义加载文本
 *    const data = await apiPost('/api/save', formData, {}, {
 *        loadingMessage: '正在保存数据...',
 *        showSuccess: true,
 *        successMessage: '保存成功！'
 *    });
 *
 *    // 禁用加载遮罩
 *    const data = await apiGet('/api/quick-check', {}, {}, { showLoading: false });
 */