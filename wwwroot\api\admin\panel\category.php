<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

// 辅助函数：将树状结构扁平化，保持层级关系
function flattenCategoryTree($tree, &$result, $level = 0) {
    foreach ($tree as $category) {
        $category['_level'] = $level; // 添加显示层级标记
        $children = $category['children'];
        $category['children'] = []; // 清空children，避免数据重复
        $result[] = $category;

        if (!empty($children)) {
            flattenCategoryTree($children, $result, $level + 1);
        }
    }
}

try {
    // 根据请求方法处理不同操作
    switch ($method) {
        case 'GET':
            // 列出所有分类或获取单个分类详情
            if (isset($_GET['id'])) {
                // 获取单个分类详情
                $id = intval($_GET['id']);
                $stmt = $db->prepare('SELECT * FROM categories WHERE id = :id');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $result = $stmt->execute();
                $category = $result->fetchArray(SQLITE3_ASSOC);

                if (!$category) {
                    json_response(['success' => false, 'message' => '分类不存在'], 404);
                }

                json_response(['success' => true, 'data' => $category]);
            } else {
                // 列出所有分类（不分页，支持搜索）
                $search = isset($_GET['search']) ? trim($_GET['search']) : '';

                // 构建查询条件
                $whereClause = '';
                $params = [];

                if (!empty($search)) {
                    $whereClause = ' WHERE c.name LIKE :search';
                    $params[':search'] = '%' . $search . '%';
                }

                // 获取所有分类数据 - 树状结构（不分页）
                $query = "SELECT c.*, COUNT(s.id) as software_count,
                          (SELECT COUNT(*) FROM categories WHERE parent_id = c.id) as child_count
                          FROM categories c
                          LEFT JOIN softwares s ON c.id = s.category
                          $whereClause
                          GROUP BY c.id
                          ORDER BY c.level ASC, c.sort ASC, c.id ASC";

                $stmt = $db->prepare($query);

                foreach ($params as $param => $value) {
                    $stmt->bindValue($param, $value, SQLITE3_TEXT);
                }

                $result = $stmt->execute();

                $categories = [];
                $categoryMap = [];

                // 获取所有父分类名称
                $parentQuery = "SELECT id, name FROM categories";
                $parentResult = $db->query($parentQuery);
                $parentNames = [];

                while ($parentRow = $parentResult->fetchArray(SQLITE3_ASSOC)) {
                    $parentNames[$parentRow['id']] = $parentRow['name'];
                }

                // 先获取所有分类
                while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                    // 添加父分类名称
                    if ($row['parent_id'] > 0 && isset($parentNames[$row['parent_id']])) {
                        $row['parent_name'] = $parentNames[$row['parent_id']];
                    } else {
                        $row['parent_name'] = '';
                    }

                    // 添加children数组用于存放子分类
                    $row['children'] = [];

                    $categories[] = $row;
                    $categoryMap[$row['id']] = &$categories[count($categories) - 1]; // 使用引用，方便后续添加子分类
                }

                // 如果搜索条件为空，则构建树状结构
                if (empty($search)) {
                    $tree = [];
                    foreach ($categories as $category) {
                        if ($category['parent_id'] == 0) {
                            // 顶级分类直接添加到树中
                            $tree[] = &$categoryMap[$category['id']];
                        } else {
                            // 子分类添加到父分类的children数组中
                            if (isset($categoryMap[$category['parent_id']])) {
                                $categoryMap[$category['parent_id']]['children'][] = &$categoryMap[$category['id']];
                            }
                        }
                    }

                    // 使用树状结构
                    $flattenedTree = [];
                    flattenCategoryTree($tree, $flattenedTree);
                    $categories = $flattenedTree;
                }

                json_response([
                    'success' => true,
                    'data' => [
                        'items' => $categories,
                        'total' => count($categories)
                    ]
                ]);

            }
            break;

        case 'POST':
            // 创建新分类
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);

            // 验证必填字段
            if (empty($data['name'])) {
                json_response(['success' => false, 'message' => '分类名称不能为空'], 400);
            }

            // 准备数据
            $name = $data['name'];
            $sort = isset($data['sort']) ? intval($data['sort']) : 0;
            $parentId = isset($data['parent_id']) ? intval($data['parent_id']) : 0;
            $createdAt = time();

            // 验证父分类是否存在
            if ($parentId > 0) {
                $parentStmt = $db->prepare('SELECT id, level, path FROM categories WHERE id = :id');
                $parentStmt->bindValue(':id', $parentId, SQLITE3_INTEGER);
                $parentResult = $parentStmt->execute();
                $parent = $parentResult->fetchArray(SQLITE3_ASSOC);

                if (!$parent) {
                    json_response(['success' => false, 'message' => '父分类不存在'], 400);
                }

                // 设置level和path
                $level = $parent['level'] + 1;
                $path = $parent['path'] ? $parent['path'] . ',' . $parent['id'] : $parent['id'];
            } else {
                // 顶级分类
                $level = 0;
                $path = '';
            }

            // 插入数据
            $stmt = $db->prepare('INSERT INTO categories (name, parent_id, level, path, sort, created_at) VALUES (:name, :parent_id, :level, :path, :sort, :created_at)');
            $stmt->bindValue(':name', $name, SQLITE3_TEXT);
            $stmt->bindValue(':parent_id', $parentId, SQLITE3_INTEGER);
            $stmt->bindValue(':level', $level, SQLITE3_INTEGER);
            $stmt->bindValue(':path', $path, SQLITE3_TEXT);
            $stmt->bindValue(':sort', $sort, SQLITE3_INTEGER);
            $stmt->bindValue(':created_at', $createdAt, SQLITE3_INTEGER);
            $stmt->execute();

            $newId = $db->lastInsertRowID();

            json_response([
                'success' => true,
                'message' => '分类添加成功',
                'data' => ['id' => $newId]
            ], 201);
            break;

        case 'PUT':
            // 更新分类
            if (!isset($_GET['id'])) {
                json_response(['success' => false, 'message' => '缺少分类ID'], 400);
            }

            $id = intval($_GET['id']);

            // 检查分类是否存在
            $stmt = $db->prepare('SELECT id FROM categories WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $category = $result->fetchArray(SQLITE3_ASSOC);

            if (!$category) {
                json_response(['success' => false, 'message' => '分类不存在'], 404);
            }

            $json = file_get_contents('php://input');
            $data = json_decode($json, true);

            // 验证必填字段
            if (empty($data['name'])) {
                json_response(['success' => false, 'message' => '分类名称不能为空'], 400);
            }

            // 准备数据
            $name = $data['name'];
            $sort = isset($data['sort']) ? intval($data['sort']) : 0;
            $parentId = isset($data['parent_id']) ? intval($data['parent_id']) : 0;

            // 检查是否尝试将分类设为自己的子分类
            if ($parentId == $id) {
                json_response(['success' => false, 'message' => '不能将分类设为自己的子分类'], 400);
            }

            // 检查是否尝试将分类设为其子分类的子分类
            if ($parentId > 0) {
                // 获取所有子分类ID
                $childrenQuery = "SELECT id FROM categories WHERE path LIKE '%,$id,%' OR path LIKE '%,$id' OR path = '$id'";
                $childrenResult = $db->query($childrenQuery);
                $childrenIds = [];

                while ($childRow = $childrenResult->fetchArray(SQLITE3_ASSOC)) {
                    $childrenIds[] = $childRow['id'];
                }

                if (in_array($parentId, $childrenIds)) {
                    json_response(['success' => false, 'message' => '不能将分类设为其子分类的子分类'], 400);
                }

                // 验证父分类是否存在
                $parentStmt = $db->prepare('SELECT id, level, path FROM categories WHERE id = :id');
                $parentStmt->bindValue(':id', $parentId, SQLITE3_INTEGER);
                $parentResult = $parentStmt->execute();
                $parent = $parentResult->fetchArray(SQLITE3_ASSOC);

                if (!$parent) {
                    json_response(['success' => false, 'message' => '父分类不存在'], 400);
                }

                // 设置level和path
                $level = $parent['level'] + 1;
                $path = $parent['path'] ? $parent['path'] . ',' . $parent['id'] : $parent['id'];
            } else {
                // 顶级分类
                $level = 0;
                $path = '';
            }

            // 获取当前分类信息
            $currentStmt = $db->prepare('SELECT parent_id, level, path FROM categories WHERE id = :id');
            $currentStmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $currentResult = $currentStmt->execute();
            $current = $currentResult->fetchArray(SQLITE3_ASSOC);

            // 开始事务
            $db->exec('BEGIN TRANSACTION');

            try {
                // 更新当前分类
                $stmt = $db->prepare('UPDATE categories SET name = :name, parent_id = :parent_id, level = :level, path = :path, sort = :sort WHERE id = :id');
                $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                $stmt->bindValue(':name', $name, SQLITE3_TEXT);
                $stmt->bindValue(':parent_id', $parentId, SQLITE3_INTEGER);
                $stmt->bindValue(':level', $level, SQLITE3_INTEGER);
                $stmt->bindValue(':path', $path, SQLITE3_TEXT);
                $stmt->bindValue(':sort', $sort, SQLITE3_INTEGER);
                $stmt->execute();

                // 如果父分类发生变化，需要更新所有子分类的level和path
                if ($current['parent_id'] != $parentId) {
                    // 获取所有子分类
                    $oldPath = $current['path'] ? $current['path'] . ',' . $id : $id;
                    $childrenQuery = "SELECT id, level, path FROM categories WHERE path LIKE '$oldPath,%' OR path = '$oldPath'";
                    $childrenResult = $db->query($childrenQuery);

                    while ($child = $childrenResult->fetchArray(SQLITE3_ASSOC)) {
                        // 计算新的level和path
                        $levelDiff = $level - $current['level'];
                        $newChildLevel = $child['level'] + $levelDiff;

                        // 替换path中的前缀
                        $newChildPath = $path ? $path . ',' . $id : $id;
                        if (strpos($child['path'], $oldPath . ',') === 0) {
                            $newChildPath .= substr($child['path'], strlen($oldPath));
                        }

                        // 更新子分类
                        $updateChildStmt = $db->prepare('UPDATE categories SET level = :level, path = :path WHERE id = :id');
                        $updateChildStmt->bindValue(':id', $child['id'], SQLITE3_INTEGER);
                        $updateChildStmt->bindValue(':level', $newChildLevel, SQLITE3_INTEGER);
                        $updateChildStmt->bindValue(':path', $newChildPath, SQLITE3_TEXT);
                        $updateChildStmt->execute();
                    }
                }

                // 提交事务
                $db->exec('COMMIT');
            } catch (Exception $e) {
                // 回滚事务
                $db->exec('ROLLBACK');
                throw $e;
            }

            json_response(['success' => true, 'message' => '分类更新成功']);
            break;

        case 'DELETE':
            // 删除分类
            if (!isset($_GET['id'])) {
                json_response(['success' => false, 'message' => '缺少分类ID'], 400);
            }

            $id = intval($_GET['id']);

            // 检查分类是否存在
            $stmt = $db->prepare('SELECT id FROM categories WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $category = $result->fetchArray(SQLITE3_ASSOC);

            if (!$category) {
                json_response(['success' => false, 'message' => '分类不存在'], 404);
            }

            // 检查是否有子分类
            $childStmt = $db->prepare('SELECT COUNT(*) FROM categories WHERE parent_id = :parent_id');
            $childStmt->bindValue(':parent_id', $id, SQLITE3_INTEGER);
            $childResult = $childStmt->execute();
            $childCount = $childResult->fetchArray()[0];

            if ($childCount > 0) {
                json_response([
                    'success' => false,
                    'message' => '该分类下有' . $childCount . '个子分类，无法删除，请先删除所有子分类'
                ], 400);
            }

            // 检查分类是否有关联的软件
            $stmt = $db->prepare('SELECT COUNT(*) FROM softwares WHERE category = :category');
            $stmt->bindValue(':category', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $count = $result->fetchArray()[0];

            if ($count > 0) {
                json_response([
                    'success' => false,
                    'message' => '该分类下有' . $count . '个软件，无法删除，请先将软件移至其他分类'
                ], 400);
            }

            // 删除分类
            $stmt = $db->prepare('DELETE FROM categories WHERE id = :id');
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $stmt->execute();

            json_response(['success' => true, 'message' => '分类删除成功']);
            break;

        default:
            json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}
