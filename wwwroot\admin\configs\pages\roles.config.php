<?php
/**
 * 角色管理页面配置
 */

return [
    'title' => '角色管理',
    'apiEndpoint' => '/api/admin/panel/roles.php',
    'activeMenu' => 'roles',
    'permissions' => ['role.view'],
    'appId' => 'rolesApp',
    
    // 功能开关
    'enableSearch' => true,
    'enableAdd' => true,
    'enableEdit' => true,
    'enableDelete' => true,
    'enableBatchDelete' => false,
    'enableSelection' => false,
    
    // 搜索配置
    'searchPlaceholder' => '搜索角色...',
    'searchFields' => ['name'],
    
    // 空数据配置
    'emptyText' => '暂无角色数据',
    'emptyIcon' => 'fas fa-user-shield',
    
    // 添加按钮配置
    'addButtonText' => '添加角色',
    'addPermission' => 'role.add',
    
    // 分页配置
    'defaultPageSize' => 10,
    
    // 表格列配置
    'columns' => [
        [
            'key' => 'id',
            'label' => 'ID',
            'type' => 'number',
            'class' => 'w-16'
        ],
        [
            'key' => 'name',
            'label' => '角色名称',
            'type' => 'text'
        ],
        [
            'key' => 'description',
            'label' => '描述',
            'type' => 'custom'
        ],
        [
            'key' => 'admin_count',
            'label' => '管理员数量',
            'type' => 'number'
        ],
        [
            'key' => 'created_at',
            'label' => '创建时间',
            'type' => 'datetime'
        ]
    ],
    
    // 操作按钮配置
    'actions' => [
        [
            'key' => 'edit',
            'icon' => 'fas fa-edit',
            'title' => '编辑角色',
            'class' => 'text-blue-400 hover:text-blue-300',
            'permission' => 'role.edit',
            'handler' => 'editRole'
        ],
        [
            'key' => 'delete',
            'icon' => 'fas fa-trash-alt',
            'title' => '删除角色',
            'class' => 'text-red-400 hover:text-red-300',
            'permission' => 'role.delete',
            'condition' => 'item.admin_count === 0',
            'disabledClass' => 'opacity-50',
            'handler' => 'confirmDelete'
        ]
    ],
    
    // 表单字段配置
    'fields' => [
        [
            'key' => 'name',
            'label' => '角色名称',
            'type' => 'text',
            'required' => true,
            'placeholder' => '请输入角色名称'
        ],
        [
            'key' => 'description',
            'label' => '描述',
            'type' => 'textarea',
            'required' => false,
            'placeholder' => '请输入角色描述',
            'rows' => 3
        ],
        [
            'key' => 'permissions',
            'label' => '权限设置',
            'type' => 'permissions',
            'required' => false,
            'help' => '选择该角色拥有的权限'
        ]
    ],
    
    // 删除确认配置
    'deleteConfirmation' => [
        'title' => '确认删除',
        'message' => '您确定要删除角色 "{name}" 吗？此操作不可撤销。',
        'restrictions' => [
            [
                'condition' => 'admin_count > 0',
                'message' => '该角色下有 {admin_count} 个管理员，无法删除。请先移除这些管理员的角色。'
            ]
        ]
    ],
    
    // 添加/编辑模态框配置
    'modal' => [
        'addTitle' => '添加角色',
        'editTitle' => '编辑角色',
        'addSubmitText' => '添加角色',
        'editSubmitText' => '保存修改',
        'width' => 'max-w-4xl'  // 权限设置需要更宽的模态框
    ],
    
    // 权限组配置
    'permissionGroups' => [
        'admin' => '管理员管理',
        'role' => '角色管理',
        'software' => '软件管理',
        'category' => '分类管理',
        'orders' => '订单管理',
        'payment_notify' => '支付通知',
        'stats' => '统计数据',
        'system' => '系统设置'
    ],
    
    // 特殊配置
    'specialFeatures' => [
        'loadPermissions' => true,      // 需要加载权限列表
        'preventDeleteWithAdmins' => true,  // 防止删除有管理员的角色
        'permissionManagement' => true  // 包含权限管理功能
    ]
];
