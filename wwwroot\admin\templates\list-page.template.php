<?php
/**
 * 列表页面模板
 * 
 * 使用方式：
 * $pageConfig = [
 *     'title' => '页面标题',
 *     'apiEndpoint' => 'api端点',
 *     'permissions' => ['required.permission'],
 *     'columns' => [...],
 *     'filters' => [...],
 *     'actions' => [...]
 * ];
 * include_template('list-page', $pageConfig);
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 验证配置
if (!isset($pageConfig) || !is_array($pageConfig)) {
    throw new Exception('页面配置未定义或格式错误');
}

// 设置默认值
$config = array_merge([
    'title' => '数据管理',
    'apiEndpoint' => '',
    'permissions' => [],
    'activeMenu' => '',
    'enableSearch' => true,
    'enableAdd' => true,
    'enableEdit' => true,
    'enableDelete' => true,
    'enableBatchDelete' => true,
    'enableSelection' => true,
    'searchPlaceholder' => '搜索...',
    'addButtonText' => '添加',
    'emptyText' => '暂无数据',
    'emptyIcon' => 'fas fa-inbox',
    'defaultPageSize' => 10,
    'columns' => [],
    'filters' => [],
    'actions' => [],
    'fields' => []
], $pageConfig);

// 渲染头部
$requiredPermissions = !empty($config['permissions']) ? $config['permissions'] : null;
render_header($config['title'], $config['activeMenu'], $requiredPermissions);
?>

<!-- 页面内容开始 -->
<div id="<?php echo $config['appId'] ?? strtolower(str_replace(' ', '', $config['title'])) . 'App'; ?>">
    <!-- 使用AdminListView组件 -->
    <admin-list-view :config="pageConfig" ref="listView">
        <!-- 自定义列插槽 -->
        <?php if (!empty($config['customColumns'])): ?>
            <?php foreach ($config['customColumns'] as $columnKey => $columnTemplate): ?>
                <template #column-<?php echo $columnKey; ?>="{ item, value }">
                    <?php echo $columnTemplate; ?>
                </template>
            <?php endforeach; ?>
        <?php endif; ?>
        
        <!-- 模态框插槽 -->
        <template #modals>
            <!-- 添加/编辑模态框 -->
            <div v-if="showAddModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-2xl max-h-screen overflow-y-auto">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl text-blue-300 font-semibold">
                                {{ showAddModal ? '添加<?php echo $config['title']; ?>' : '编辑<?php echo $config['title']; ?>' }}
                            </h3>
                            <button @click="closeModals" class="text-gray-400 hover:text-white">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <form @submit.prevent="submitForm">
                            <?php if (!empty($config['fields'])): ?>
                                <?php foreach ($config['fields'] as $field): ?>
                                    <div class="form-group">
                                        <label class="form-label"><?php echo $field['label']; ?></label>
                                        <?php if ($field['type'] === 'text'): ?>
                                            <input 
                                                type="text" 
                                                v-model="formData.<?php echo $field['key']; ?>" 
                                                class="form-input"
                                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                                                <?php echo !empty($field['placeholder']) ? 'placeholder="' . $field['placeholder'] . '"' : ''; ?>
                                            >
                                        <?php elseif ($field['type'] === 'textarea'): ?>
                                            <textarea 
                                                v-model="formData.<?php echo $field['key']; ?>" 
                                                class="form-input"
                                                rows="<?php echo $field['rows'] ?? 3; ?>"
                                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                                                <?php echo !empty($field['placeholder']) ? 'placeholder="' . $field['placeholder'] . '"' : ''; ?>
                                            ></textarea>
                                        <?php elseif ($field['type'] === 'select'): ?>
                                            <select 
                                                v-model="formData.<?php echo $field['key']; ?>" 
                                                class="form-input"
                                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                                            >
                                                <option value="">请选择</option>
                                                <?php if (!empty($field['options'])): ?>
                                                    <?php foreach ($field['options'] as $option): ?>
                                                        <option value="<?php echo $option['value']; ?>"><?php echo $option['label']; ?></option>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </select>
                                        <?php elseif ($field['type'] === 'number'): ?>
                                            <input 
                                                type="number" 
                                                v-model="formData.<?php echo $field['key']; ?>" 
                                                class="form-input"
                                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                                                <?php echo !empty($field['min']) ? 'min="' . $field['min'] . '"' : ''; ?>
                                                <?php echo !empty($field['max']) ? 'max="' . $field['max'] . '"' : ''; ?>
                                                <?php echo !empty($field['step']) ? 'step="' . $field['step'] . '"' : ''; ?>
                                            >
                                        <?php elseif ($field['type'] === 'email'): ?>
                                            <input 
                                                type="email" 
                                                v-model="formData.<?php echo $field['key']; ?>" 
                                                class="form-input"
                                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                                            >
                                        <?php elseif ($field['type'] === 'url'): ?>
                                            <input 
                                                type="url" 
                                                v-model="formData.<?php echo $field['key']; ?>" 
                                                class="form-input"
                                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                                            >
                                        <?php elseif ($field['type'] === 'password'): ?>
                                            <input 
                                                type="password" 
                                                v-model="formData.<?php echo $field['key']; ?>" 
                                                class="form-input"
                                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                                            >
                                        <?php elseif ($field['type'] === 'checkbox'): ?>
                                            <label class="flex items-center">
                                                <input 
                                                    type="checkbox" 
                                                    v-model="formData.<?php echo $field['key']; ?>" 
                                                    class="mr-2"
                                                >
                                                <?php echo $field['checkboxLabel'] ?? $field['label']; ?>
                                            </label>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" @click="closeModals" class="btn bg-gray-700 text-white">取消</button>
                                <button type="submit" class="btn btn-primary" :disabled="submitting">
                                    <span v-if="submitting"><i class="fas fa-spinner fa-spin mr-2"></i> 保存中...</span>
                                    <span v-else><i class="fas fa-save mr-2"></i> 保存</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 删除确认模态框 -->
            <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-gray-900 rounded-lg shadow-lg w-full max-w-md">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-exclamation-triangle text-yellow-400 text-2xl mr-3"></i>
                            <h3 class="text-lg text-white font-semibold">确认删除</h3>
                        </div>
                        
                        <p class="text-gray-300 mb-6">
                            {{ currentItem ? '确定要删除这条记录吗？' : `确定要删除选中的 ${selectedItems.length} 条记录吗？` }}
                            此操作不可撤销。
                        </p>
                        
                        <div class="flex justify-end space-x-3">
                            <button @click="closeModals" class="btn bg-gray-700 text-white">取消</button>
                            <button @click="deleteItem" class="btn btn-danger" :disabled="deleting">
                                <span v-if="deleting"><i class="fas fa-spinner fa-spin mr-2"></i> 删除中...</span>
                                <span v-else><i class="fas fa-trash mr-2"></i> 确认删除</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </admin-list-view>
</div>

<script>
    new Vue({
        el: '#<?php echo $config['appId'] ?? strtolower(str_replace(' ', '', $config['title'])) . 'App'; ?>',
        data: {
            pageConfig: <?php echo json_encode($config, JSON_UNESCAPED_UNICODE); ?>,
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            currentItem: null,
            formData: {},
            submitting: false,
            deleting: false,
            selectedItems: []
        },
        methods: {
            // 检查权限
            checkPermission(permission) {
                return AdminUtils.checkPermission(permission);
            },

            // 显示添加表单
            showAddForm() {
                this.$refs.listView.showAddForm();
                this.showAddModal = true;
            },

            // 显示编辑表单
            showEditForm(item) {
                this.$refs.listView.showEditForm(item);
                this.showEditModal = true;
            },

            // 确认删除
            confirmDelete(item) {
                this.$refs.listView.confirmDelete(item);
                this.showDeleteModal = true;
            },

            // 提交表单
            async submitForm() {
                await this.$refs.listView.submitForm();
            },

            // 删除项目
            async deleteItem() {
                await this.$refs.listView.deleteItem();
            },

            // 关闭模态框
            closeModals() {
                this.showAddModal = false;
                this.showEditModal = false;
                this.showDeleteModal = false;
                this.$refs.listView.closeModals();
            },

            // 格式化日期时间
            formatDateTime(timestamp) {
                return AdminUtils.formatDateTime(timestamp);
            }
        }
    });
</script>

<?php
// 渲染页面底部
render_footer();
?>
