<?php
/**
 * 支付签名验证类
 *
 * 提供微信支付和支付宝的签名生成和验证功能
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

class PaymentSignature {

    /**
     * 微信支付签名生成
     *
     * @param array $params 参数
     * @param string $key 密钥
     * @return string 签名
     */
    public static function generateWechatSign($params, $key) {
        // 按照参数名ASCII码从小到大排序
        ksort($params);

        // 拼接参数
        $stringA = '';
        foreach ($params as $k => $v) {
            if ($k != 'sign' && $v !== '' && !is_null($v)) {
                $stringA .= $k . '=' . $v . '&';
            }
        }
        $stringA .= 'key=' . $key;

        // MD5加密并转换为大写
        return strtoupper(md5($stringA));
    }

    /**
     * 微信支付签名验证
     *
     * @param array $params 参数
     * @param string $key 密钥
     * @param string $signature 待验证的签名
     * @return array 验证结果 ['valid' => bool, 'error' => string]
     */
    public static function verifyWechatSign($params, $key, $signature) {
        try {
            // 移除签名参数
            $verifyParams = $params;
            unset($verifyParams['sign']);
            unset($verifyParams['type']);

            // 生成签名
            $calculatedSign = self::generateWechatSign($verifyParams, $key);

            // 比较签名
            $valid = ($signature === $calculatedSign);

            return [
                'valid' => $valid,
                'error' => $valid ? null : '微信签名验证失败',
                'calculated_sign' => $calculatedSign,
                'provided_sign' => $signature
            ];
        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => '签名验证异常: ' . $e->getMessage(),
                'calculated_sign' => null,
                'provided_sign' => $signature
            ];
        }
    }

    /**
     * 支付宝签名生成
     *
     * @param array $params 参数
     * @param string $privateKey 私钥
     * @return string 签名
     */
    public static function generateAlipaySign($params, $privateKey) {
        // 按照参数名ASCII码从小到大排序
        ksort($params);

        // 拼接参数
        $stringToBeSigned = '';
        foreach ($params as $k => $v) {
            if ($k != 'sign' && $v !== '' && !is_null($v)) {
                $stringToBeSigned .= $k . '=' . $v . '&';
            }
        }
        $stringToBeSigned = rtrim($stringToBeSigned, '&');

        // 使用私钥签名
        $privateKey = self::formatPrivateKey($privateKey);

        $signature = '';
        openssl_sign($stringToBeSigned, $signature, $privateKey, OPENSSL_ALGO_SHA256);

        // Base64编码
        return base64_encode($signature);
    }

    /**
     * 支付宝签名验证
     *
     * @param array $params 参数
     * @param string $publicKey 公钥
     * @param string $signature 待验证的签名
     * @param bool $removeEmptyValues 是否移除空值参数
     * @return array 验证结果 ['valid' => bool, 'error' => string]
     */
    public static function verifyAlipaySign($params, $publicKey, $signature, $removeEmptyValues = true) {
        try {
            // 移除签名和签名类型参数
            $verifyParams = $params;
            unset($verifyParams['sign'], $verifyParams['sign_type']);
            unset($verifyParams['type']);

            // 移除空值参数（可选）
            if ($removeEmptyValues) {
                foreach ($verifyParams as $k => $v) {
                    if ($v === '' || is_null($v)) {
                        unset($verifyParams[$k]);
                    }
                }
            }

            // 按照参数名ASCII码从小到大排序
            ksort($verifyParams);

            // 拼接参数
            $stringToBeSigned = '';
            foreach ($verifyParams as $k => $v) {
                $stringToBeSigned .= $k . '=' . $v . '&';
            }
            $stringToBeSigned = rtrim($stringToBeSigned, '&');

            // 格式化公钥
            $formattedPublicKey = self::formatPublicKey($publicKey);

            // 验证公钥格式
            if (!openssl_pkey_get_public($formattedPublicKey)) {
                return [
                    'valid' => false,
                    'error' => '公钥格式错误',
                    'string_to_be_signed' => $stringToBeSigned,
                    'formatted_public_key' => $formattedPublicKey
                ];
            }

            // 验证签名
            $result = openssl_verify($stringToBeSigned, base64_decode($signature), $formattedPublicKey, OPENSSL_ALGO_SHA256);

            $valid = ($result === 1);
            $error = null;

            if ($result === 0) {
                $error = '签名验证失败';
            } elseif ($result === -1) {
                $opensslError = openssl_error_string();
                $error = '签名验证发生错误: ' . ($opensslError ?: '未知OpenSSL错误');
            }

            return [
                'valid' => $valid,
                'error' => $error,
                'string_to_be_signed' => $stringToBeSigned,
                'verify_result' => $result,
                'formatted_public_key' => $formattedPublicKey,
                'debug_info' => [
                    'openssl_verify_result' => $result,
                    'signature_length' => strlen($signature),
                    'decoded_signature_length' => strlen(base64_decode($signature)),
                    'string_to_be_signed_length' => strlen($stringToBeSigned),
                    'public_key_length' => strlen($formattedPublicKey)
                ]
            ];
        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => '签名验证异常: ' . $e->getMessage(),
                'string_to_be_signed' => isset($stringToBeSigned) ? $stringToBeSigned : null
            ];
        }
    }

    /**
     * 格式化私钥
     *
     * @param string $privateKey 原始私钥
     * @return string 格式化后的私钥
     */
    private static function formatPrivateKey($privateKey) {
        // 如果已经包含BEGIN/END标记，直接返回
        if (strpos($privateKey, '-----BEGIN') !== false) {
            return $privateKey;
        }

        // 添加PEM格式头尾
        return "-----BEGIN RSA PRIVATE KEY-----\n" .
               wordwrap($privateKey, 64, "\n", true) .
               "\n-----END RSA PRIVATE KEY-----";
    }

    /**
     * 格式化公钥
     *
     * @param string $publicKey 原始公钥
     * @return string 格式化后的公钥
     */
    private static function formatPublicKey($publicKey) {
        // 如果已经包含BEGIN/END标记，直接返回
        if (strpos($publicKey, '-----BEGIN') !== false) {
            return $publicKey;
        }

        // 添加PEM格式头尾
        return "-----BEGIN PUBLIC KEY-----\n" .
               wordwrap($publicKey, 64, "\n", true) .
               "\n-----END PUBLIC KEY-----";
    }

    /**
     * 测试支付宝配置
     *
     * @param array $config 支付宝配置
     * @return array 测试结果
     */
    public static function testAlipayConfig($config) {
        $result = [
            'app_id' => $config['app_id'] ?? null,
            'public_key_length' => isset($config['public_key']) ? strlen($config['public_key']) : 0,
            'private_key_length' => isset($config['private_key']) ? strlen($config['private_key']) : 0,
            'gateway' => $config['gateway'] ?? null,
            'notify_url' => $config['notify_url'] ?? null,
            'public_key_valid' => false,
            'private_key_valid' => false
        ];

        // 测试公钥格式
        if (isset($config['public_key'])) {
            $formattedPublicKey = self::formatPublicKey($config['public_key']);
            $result['public_key_valid'] = openssl_pkey_get_public($formattedPublicKey) !== false;
            $result['public_key_formatted'] = $formattedPublicKey;
        }

        // 测试私钥格式
        if (isset($config['private_key'])) {
            $formattedPrivateKey = self::formatPrivateKey($config['private_key']);
            $result['private_key_valid'] = openssl_pkey_get_private($formattedPrivateKey) !== false;
        }

        return $result;
    }

    /**
     * 测试支付宝签名
     *
     * @param array $config 支付宝配置
     * @return array 测试结果
     */
    public static function testAlipaySign($config) {
        // 测试参数
        $testParams = [
            'app_id' => $config['app_id'] ?? '2021004139607564',
            'method' => 'alipay.trade.page.pay',
            'charset' => 'utf-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'out_trade_no' => 'test_' . time(),
            'total_amount' => '0.01',
            'subject' => '测试订单'
        ];

        try {
            // 生成签名
            $sign = self::generateAlipaySign($testParams, $config['private_key']);
            $testParams['sign'] = $sign;

            // 验证签名
            $verifyResult = self::verifyAlipaySign($testParams, $config['public_key'], $sign);

            return [
                'success' => true,
                'test_params' => $testParams,
                'sign' => $sign,
                'verify_result' => $verifyResult
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'test_params' => $testParams
            ];
        }
    }
}
