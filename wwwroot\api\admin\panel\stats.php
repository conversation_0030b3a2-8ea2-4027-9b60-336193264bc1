<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once __DIR__ . '/../panel.php';

// 获取数据库连接
$db = get_db_connection();

// 获取请求参数
$period = get_param('period', 'today');
$startDate = get_param('start_date', null);
$endDate = get_param('end_date', null);

// 根据时间段获取适当的时间戳
$currentTime = time();
$startTimestamp = null;
$endTimestamp = $currentTime;

switch ($period) {
    case 'today':
        $startTimestamp = strtotime('today');
        break;
    case 'yesterday':
        $startTimestamp = strtotime('yesterday');
        $endTimestamp = strtotime('today') - 1;
        break;
    case 'week':
        $startTimestamp = strtotime('-7 days');
        break;
    case 'month':
        $startTimestamp = strtotime('-30 days');
        break;
    case 'year':
        $startTimestamp = strtotime('-365 days');
        break;
    case 'custom':
        if ($startDate) {
            $startTimestamp = strtotime($startDate);
        }
        if ($endDate) {
            $endTimestamp = strtotime($endDate) + 86399; // End of the day
        }
        break;
}

// 检查是否存在下载日志表
$tableExists = $db->querySingle("SELECT name FROM sqlite_master WHERE type='table' AND name='download_logs'");
if (!$tableExists) {
    // 创建下载日志表
    $db->exec('
    CREATE TABLE download_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        software_id INTEGER NOT NULL,
        ip TEXT,
        user_agent TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (software_id) REFERENCES softwares(id)
    )');
}

try {
    // 获取总下载数据
    $totalDownloadsQuery = "SELECT COUNT(*) as count, SUM(s.downloads) as total_downloads 
                          FROM softwares s";
    $totalDownloadsResult = $db->query($totalDownloadsQuery);
    $totalDownloads = $totalDownloadsResult->fetchArray(SQLITE3_ASSOC);

    // 按分类统计软件数量和下载量
    $categoryStatsQuery = "
        SELECT 
            c.id, c.name, 
            COUNT(s.id) as software_count, 
            SUM(s.downloads) as download_count
        FROM 
            categories c
        LEFT JOIN 
            softwares s ON c.id = s.category
        GROUP BY 
            c.id
        ORDER BY 
            download_count DESC, software_count DESC
    ";
    $categoryStatsResult = $db->query($categoryStatsQuery);
    
    $categoryStats = [];
    while ($row = $categoryStatsResult->fetchArray(SQLITE3_ASSOC)) {
        $categoryStats[] = $row;
    }

    // 最受欢迎的软件（下载量最多）
    $popularSoftwareQuery = "
        SELECT 
            id, name, downloads, fake_downloads,
            CASE WHEN fake_downloads IS NOT NULL THEN fake_downloads || '万' ELSE downloads END as display_downloads
        FROM 
            softwares
        ORDER BY 
            downloads DESC
        LIMIT 10
    ";
    $popularSoftwareResult = $db->query($popularSoftwareQuery);
    
    $popularSoftware = [];
    while ($row = $popularSoftwareResult->fetchArray(SQLITE3_ASSOC)) {
        $popularSoftware[] = $row;
    }

    // 如果下载日志表存在，获取最近下载记录
    $recentDownloads = [];
    if ($tableExists) {
        // 准备时间条件
        $timeCondition = '';
        $params = [];
        
        if ($startTimestamp !== null) {
            $timeCondition = 'WHERE dl.created_at >= :start_time';
            $params[':start_time'] = $startTimestamp;
            
            if ($endTimestamp !== null) {
                $timeCondition .= ' AND dl.created_at <= :end_time';
                $params[':end_time'] = $endTimestamp;
            }
        }
        
        // 获取时间段内的下载数
        $periodDownloadsQuery = "
            SELECT COUNT(*) as count
            FROM download_logs dl
            $timeCondition
        ";
        $stmt = $db->prepare($periodDownloadsQuery);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $periodDownloadsResult = $stmt->execute();
        $periodDownloads = $periodDownloadsResult->fetchArray(SQLITE3_ASSOC);
        
        // 获取最近的下载记录
        $recentDownloadsQuery = "
            SELECT 
                dl.id, dl.software_id, dl.ip, dl.created_at,
                s.name as software_name
            FROM 
                download_logs dl
            LEFT JOIN 
                softwares s ON dl.software_id = s.id
            $timeCondition
            ORDER BY 
                dl.created_at DESC
            LIMIT 20
        ";
        
        $stmt = $db->prepare($recentDownloadsQuery);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $recentDownloadsResult = $stmt->execute();
        
        while ($row = $recentDownloadsResult->fetchArray(SQLITE3_ASSOC)) {
            // 格式化日期时间
            $row['formatted_date'] = date('Y-m-d H:i:s', $row['created_at']);
            $recentDownloads[] = $row;
        }
        
        // 按软件分组统计时间段内的下载数
        $softwareStatsQuery = "
            SELECT 
                dl.software_id,
                s.name as software_name,
                COUNT(dl.id) as download_count
            FROM 
                download_logs dl
            LEFT JOIN 
                softwares s ON dl.software_id = s.id
            $timeCondition
            GROUP BY 
                dl.software_id
            ORDER BY 
                download_count DESC
            LIMIT 10
        ";
        
        $stmt = $db->prepare($softwareStatsQuery);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $softwareStatsResult = $stmt->execute();
        
        $softwareStats = [];
        while ($row = $softwareStatsResult->fetchArray(SQLITE3_ASSOC)) {
            $softwareStats[] = $row;
        }
    } else {
        $periodDownloads = ['count' => 0];
        $softwareStats = [];
    }

    // 整理统计数据
    $stats = [
        'overview' => [
            'total_software' => $totalDownloads['count'],
            'total_downloads' => $totalDownloads['total_downloads'],
            'period_downloads' => $periodDownloads['count'] ?? 0
        ],
        'category_stats' => $categoryStats,
        'popular_software' => $popularSoftware,
        'recent_downloads' => $recentDownloads,
        'software_stats' => $softwareStats ?? [],
        'period' => $period,
        'time_range' => [
            'start' => $startTimestamp ? date('Y-m-d', $startTimestamp) : null,
            'end' => $endTimestamp ? date('Y-m-d', $endTimestamp) : date('Y-m-d')
        ]
    ];

    json_response(['success' => true, 'data' => $stats]);
} catch (Exception $e) {
    json_response(['success' => false, 'message' => '统计数据获取失败: ' . $e->getMessage()], 500);
} 
