<?php
/**
 * 支付通知记录页面配置
 */

return [
    'title' => '支付通知记录',
    'apiEndpoint' => '/api/admin/panel/payment_notify.php',
    'activeMenu' => 'payment_notify',
    'permissions' => ['payment_notify.view'],
    'appId' => 'paymentNotifyApp',

    // 功能开关
    'enableSearch' => true,
    'enableAdd' => false,  // 支付通知记录不允许手动添加
    'enableEdit' => false, // 支付通知记录不允许编辑
    'enableDelete' => true,
    'enableBatchDelete' => true,
    'enableSelection' => true,

    // 搜索配置
    'searchPlaceholder' => '搜索订单号或IP地址...',
    'searchFields' => ['order_no', 'ip_address'],

    // 空数据配置
    'emptyText' => '暂无支付通知记录',
    'emptyIcon' => 'fas fa-bell-slash',

    // 分页配置
    'defaultPageSize' => 20,

    // 表格列配置
    'columns' => [
        [
            'key' => 'id',
            'label' => 'ID',
            'class' => 'w-16'
        ],
        [
            'key' => 'payment_type',
            'label' => '支付类型',
            'type' => 'custom'
        ],
        [
            'key' => 'order_no',
            'label' => '订单号'
        ],
        [
            'key' => 'signature_valid',
            'label' => '签名验证',
            'type' => 'custom'
        ],
        [
            'key' => 'processing_result',
            'label' => '处理结果',
            'type' => 'custom'
        ],
        [
            'key' => 'ip_address',
            'label' => 'IP地址'
        ],
        [
            'key' => 'created_at',
            'label' => '时间',
            'type' => 'datetime'
        ]
    ],

    // 自定义列模板（标记为使用插槽）
    'customColumns' => [
        'payment_type' => 'slot',
        'signature_valid' => 'slot',
        'processing_result' => 'slot'
    ],

    // 筛选器配置
    'filters' => [
        [
            'key' => 'payment_type',
            'placeholder' => '所有支付类型',
            'width' => '150px',
            'options' => [
                ['value' => 'wechat_pay', 'label' => '微信支付'],
                ['value' => 'alipay', 'label' => '支付宝']
            ]
        ],
        [
            'key' => 'signature_valid',
            'placeholder' => '所有验证状态',
            'width' => '120px',
            'options' => [
                ['value' => '1', 'label' => '验证成功'],
                ['value' => '0', 'label' => '验证失败'],
                ['value' => '-1', 'label' => '未验证']
            ]
        ],
        [
            'key' => 'processing_result',
            'placeholder' => '所有处理结果',
            'width' => '120px',
            'options' => [
                ['value' => 'success', 'label' => '成功'],
                ['value' => 'failed', 'label' => '失败'],
                ['value' => 'error', 'label' => '错误']
            ]
        ]
    ],

    // 操作按钮配置
    'actions' => [
        [
            'key' => 'detail',
            'icon' => 'fas fa-eye',
            'title' => '查看详情',
            'class' => 'text-blue-400 hover:text-blue-300',
            'handler' => 'showDetail'
        ],
        [
            'key' => 'verify',
            'icon' => 'fas fa-shield-alt',
            'title' => '手动验签',
            'class' => 'text-yellow-400 hover:text-yellow-300',
            'permission' => 'payment_notify.verify',
            'condition' => 'item.signature_valid !== 1',
            'handler' => 'verifySignature'
        ]
    ]
];
