<?php
/**
 * 管理面板 - 批量管理
 *
 * 注意：此文件生成静态HTML，不包含任何动态操作
 */

// 引入面板公共文件
require_once dirname(__DIR__) . '/panel.php';

// 设置页面标题和活动菜单
$pageTitle = '批量管理';
$activeMenu = 'bulk';

// 渲染头部（需要'software.edit'权限）
render_header($pageTitle, $activeMenu, 'software.edit');
?>

<!-- 页面内容开始 -->
<div id="bulkManageApp">
    <!-- 顶部操作栏 -->
    <div class="flex flex-wrap justify-between items-center mb-4 gap-4">
        <div class="flex flex-wrap items-center gap-4">
            <div class="flex items-center space-x-4">
                <category-selector
                    v-model="selectedCategory"
                    :categories="categories"
                    placeholder="所有分类"
                    noneOptionText="所有分类"
                    width="200px"
                    @input="onCategoryChange">
                </category-selector>

                <div v-if="selectedCategory" class="flex items-center">
                    <input type="checkbox" id="include-subcategories" v-model="includeSubcategories" @change="onCategoryChange" class="form-checkbox h-4 w-4 text-blue-600">
                    <label for="include-subcategories" class="ml-2 text-sm text-gray-300">包含子分类</label>
                </div>
            </div>

            <div class="relative">
                <input type="text" v-model="searchQuery" @input="debounceSearch" placeholder="搜索软件..." class="form-input pr-10">
                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            </div>
        </div>

        <div>
            <button @click="applyBulkAction" class="btn btn-primary" :disabled="selectedSoftware.length === 0 || processing">
                <span v-if="processing"><i class="fas fa-spinner fa-spin mr-2"></i> 处理中...</span>
                <span v-else><i class="fas fa-play mr-2"></i> 执行操作</span>
            </button>
        </div>
    </div>

    <!-- 批量操作选项 -->
    <div class="content-card mb-6">
        <h2>批量操作</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <div class="form-group">
                    <label class="form-label">操作类型</label>
                    <select v-model="bulkAction" class="form-input bg-transparent">
                        <option value="">选择操作类型</option>
                        <option value="change_category">更改分类</option>
                        <option value="update_price">更新价格</option>
                        <option value="update_downloads">更新下载量</option>
                        <option value="delete">删除软件</option>
                    </select>
                </div>

                <div v-if="bulkAction === 'change_category'" class="form-group">
                    <label class="form-label">目标分类</label>
                    <category-selector
                        v-model="bulkActionParams.targetCategory"
                        :categories="categories"
                        placeholder="选择目标分类"
                        noneOptionText="无分类"
                        width="100%">
                    </category-selector>
                </div>

                <div v-if="bulkAction === 'update_price'" class="form-group">
                    <label class="form-label">价格设置</label>
                    <input type="text" v-model="bulkActionParams.price" class="form-input" placeholder="输入价格">
                </div>

                <div v-if="bulkAction === 'update_downloads'" class="form-group">
                    <label class="form-label">下载量设置</label>
                    <div class="flex items-center gap-2">
                        <select v-model="bulkActionParams.downloadOperation" class="form-input bg-transparent">
                            <option value="set">设置为</option>
                            <option value="add">增加</option>
                            <option value="subtract">减少</option>
                        </select>
                        <input type="number" v-model="bulkActionParams.downloadValue" class="form-input" min="0" placeholder="输入数值">
                    </div>
                </div>

                <div v-if="bulkAction === 'delete'" class="form-group">
                    <div class="bg-red-900 bg-opacity-20 p-4 rounded-lg border border-red-800">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-red-500 mt-1 mr-3"></i>
                            <div>
                                <div class="text-red-400 font-medium mb-1">警告：此操作不可撤销</div>
                                <div class="text-gray-300 text-sm">删除操作将永久移除所选软件，且无法恢复。</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="inline-flex items-center">
                                <input type="checkbox" v-model="bulkActionParams.confirmDelete" class="form-checkbox">
                                <span class="ml-2">我已了解风险并确认删除</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <div class="form-group">
                    <label class="form-label">已选择 {{ selectedSoftware.length }} 个软件</label>
                    <div class="bg-gray-800 bg-opacity-50 p-3 rounded-lg border border-gray-700 h-32 overflow-y-auto">
                        <div v-if="selectedSoftware.length === 0" class="text-gray-500 text-center py-8">
                            请从下方列表中选择软件
                        </div>
                        <div v-else>
                            <div v-for="id in selectedSoftware" :key="id" class="flex justify-between items-center py-1 px-2 hover:bg-gray-700 rounded">
                                <div>{{ getSoftwareName(id) }}</div>
                                <button @click="toggleSelection(id)" class="text-red-400 hover:text-red-300">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="flex justify-between">
                        <button @click="selectAll" class="btn bg-blue-900 text-blue-200 hover:bg-blue-800">
                            <i class="fas fa-check-square mr-1"></i> 全选
                        </button>
                        <button @click="deselectAll" class="btn bg-gray-700 text-gray-200 hover:bg-gray-600">
                            <i class="fas fa-square mr-1"></i> 取消全选
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 软件列表 -->
    <div class="content-card">
        <h2>软件列表</h2>

        <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>

        <div v-else-if="softwareList.length === 0" class="text-center py-8 text-gray-400">
            <i class="fas fa-inbox text-4xl mb-3"></i>
            <p>暂无软件数据</p>
        </div>

        <div v-else>
            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="w-12">
                                <input type="checkbox" @change="toggleSelectAll" :checked="allSelected" class="form-checkbox">
                            </th>
                            <th class="w-16">ID</th>
                            <th>软件名称</th>
                            <th>分类</th>
                            <th>版本</th>
                            <th>价格</th>
                            <th>下载量</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="software in softwareList" :key="software.id">
                            <td>
                                <input type="checkbox" v-model="selectedItems[software.id]" @change="updateSelectedSoftware" class="form-checkbox">
                            </td>
                            <td>{{ software.id }}</td>
                            <td>{{ software.name }}</td>
                            <td>{{ software.category_name || '未分类' }}</td>
                            <td>{{ software.version || '未知' }}</td>
                            <td>{{ software.price || '免费' }}</td>
                            <td>{{ software.downloads }} <span v-if="software.fake_downloads" class="text-xs text-gray-400">({{ software.fake_downloads }}万)</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-400">
                    共 {{ pagination.totalItems }} 条记录，第 {{ pagination.page }}/{{ pagination.totalPages }} 页
                </div>
                <div class="flex space-x-2">
                    <button @click="changePage(1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button @click="changePage(pagination.page - 1)" :disabled="pagination.page === 1" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === 1 }">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button @click="changePage(pagination.page + 1)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button @click="changePage(pagination.totalPages)" :disabled="pagination.page === pagination.totalPages" class="btn btn-primary" :class="{ 'opacity-50 cursor-not-allowed': pagination.page === pagination.totalPages }">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 页面内容结束 -->

<!-- 树状分类选择组件模板 -->
<script type="text/x-template" id="category-tree-select-template">
    <div class="category-tree-select">
        <div v-for="category in categories" :key="category.id" class="category-tree-item">
            <div :class="['category-item-content cursor-pointer p-2 hover:bg-gray-700 rounded', {'bg-blue-900': selectedId == category.id}]"
                 @click="$emit('select', category.id)">
                <div class="flex items-center">
                    <!-- 缩进和展开/折叠图标 -->
                    <div v-if="category.level > 0" :style="{ width: (category.level * 16) + 'px' }" class="flex-shrink-0"></div>
                    <i v-if="category.child_count > 0"
                       class="fas fa-caret-right mr-2 transition-transform duration-200"
                       :class="{ 'transform rotate-90': isExpanded(category.id) }"
                       @click.stop="$emit('toggle-expand', category.id)"></i>
                    <span v-else-if="category.level > 0" class="w-4 mr-2"></span>

                    <!-- 分类名称 -->
                    <span>{{ category.name }}</span>
                    <span v-if="category.software_count > 0" class="ml-2 text-xs text-gray-400">({{ category.software_count }})</span>
                </div>
            </div>

            <!-- 子分类 -->
            <div v-if="category.children && category.children.length > 0 && isExpanded(category.id)" class="pl-4">
                <category-tree-select
                    :categories="category.children"
                    :selected-id="selectedId"
                    :expanded-categories="expandedCategories"
                    @select="id => $emit('select', id)"
                    @toggle-expand="id => $emit('toggle-expand', id)">
                </category-tree-select>
            </div>
        </div>
    </div>
</script>

<script>
    // 注册树状分类选择组件
    Vue.component('category-tree-select', {
        template: '#category-tree-select-template',
        props: {
            categories: {
                type: Array,
                required: true
            },
            selectedId: {
                type: [Number, String, null],
                default: null
            },
            expandedCategories: {
                type: Array,
                default: () => []
            }
        },
        methods: {
            isExpanded(categoryId) {
                return this.expandedCategories.includes(categoryId);
            }
        }
    });

    new Vue({
        el: '#bulkManageApp',
        data: {
            loading: true,
            processing: false,
            searchQuery: '',
            selectedCategory: '',
            includeSubcategories: true,
            // 移除不再需要的变量
            expandedCategories: [],
            categories: [],
            softwareList: [],
            selectedItems: {},
            selectedSoftware: [],
            pagination: {
                page: 1,
                pageSize: 20,
                totalItems: 0,
                totalPages: 1
            },
            bulkAction: '',
            bulkActionParams: {
                targetCategory: '',
                price: '',
                downloadOperation: 'set',
                downloadValue: 0,
                confirmDelete: false
            },
            searchTimeout: null,
            allSelected: false
        },
        mounted() {
            this.loadCategories();
            this.loadSoftwareList();
        },
        methods: {
            // 加载分类列表（树状结构）
            async loadCategories() {
                try {
                    const data = await apiGet('<?php echo $apiPath; ?>panel/category.php');

                    if (data && data.success) {
                        this.categories = data.data.items || [];

                        // 如果有选中的分类，更新分类名称
                        if (this.selectedCategory) {
                            this.updateSelectedCategoryName();
                        }

                        // 默认展开所有顶级分类
                        this.categories.forEach(category => {
                            if (category.level === 0 && category.child_count > 0) {
                                this.expandedCategories.push(category.id);
                            }
                        });
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }
                } catch (error) {
                    console.error('加载分类数据出错：', error);
                }
            },

            // 移除不再需要的方法

            // 分类变更处理
            onCategoryChange(categoryId) {
                this.loadSoftwareList();
            },

            // 移除不再需要的方法

            // 切换分类展开/折叠状态
            toggleExpand(categoryId) {
                const index = this.expandedCategories.indexOf(categoryId);
                if (index === -1) {
                    this.expandedCategories.push(categoryId);
                } else {
                    this.expandedCategories.splice(index, 1);
                }
            },

            // 加载软件列表
            async loadSoftwareList() {
                this.loading = true;

                let url = `<?php echo $apiPath; ?>panel/software.php?page=${this.pagination.page}&pageSize=${this.pagination.pageSize}`;

                if (this.searchQuery) {
                    url += `&search=${encodeURIComponent(this.searchQuery)}`;
                }

                if (this.selectedCategory) {
                    url += `&category=${this.selectedCategory}`;

                    // 如果选中了"包含子分类"选项，添加参数
                    if (this.includeSubcategories) {
                        url += `&include_subcategories=1`;
                    }
                }

                try {
                    const data = await apiGet(url);

                    if (data && data.success) {
                        this.softwareList = data.data.items || [];
                        this.pagination = data.data.pagination || this.pagination;

                        // 重置选择状态
                        this.selectedItems = {};
                        this.softwareList.forEach(software => {
                            // 如果ID在已选择列表中，则保持选中状态
                            this.selectedItems[software.id] = this.selectedSoftware.includes(software.id);
                        });

                        // 更新全选状态
                        this.updateAllSelected();
                    } else if (data && data.redirect) {
                        window.location.href = data.redirect;
                    }

                    this.loading = false;
                } catch (error) {
                    console.error('加载软件数据出错：', error);
                    this.loading = false;
                }
            },

            // 防抖搜索
            debounceSearch() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.pagination.page = 1;
                    this.loadSoftwareList();
                }, 300);
            },

            // 切换页码
            changePage(page) {
                if (page < 1 || page > this.pagination.totalPages) {
                    return;
                }
                this.pagination.page = page;
                this.loadSoftwareList();
            },

            // 更新已选择的软件列表
            updateSelectedSoftware() {
                this.selectedSoftware = Object.keys(this.selectedItems)
                    .filter(id => this.selectedItems[id])
                    .map(id => parseInt(id));

                // 更新全选状态
                this.updateAllSelected();
            },

            // 更新全选状态
            updateAllSelected() {
                if (this.softwareList.length === 0) {
                    this.allSelected = false;
                    return;
                }

                this.allSelected = this.softwareList.every(software => this.selectedItems[software.id]);
            },

            // 切换全选/取消全选
            toggleSelectAll(event) {
                const checked = event.target.checked;
                this.softwareList.forEach(software => {
                    this.selectedItems[software.id] = checked;
                });
                this.updateSelectedSoftware();
            },

            // 全选当前页
            selectAll() {
                this.softwareList.forEach(software => {
                    this.selectedItems[software.id] = true;
                });
                this.updateSelectedSoftware();
            },

            // 取消全选
            deselectAll() {
                this.softwareList.forEach(software => {
                    this.selectedItems[software.id] = false;
                });
                this.updateSelectedSoftware();
            },

            // 切换单个软件的选择状态
            toggleSelection(id) {
                this.selectedItems[id] = !this.selectedItems[id];
                this.updateSelectedSoftware();
            },

            // 获取软件名称
            getSoftwareName(id) {
                const software = this.softwareList.find(s => s.id === id);
                return software ? software.name : `软件 #${id}`;
            },

            // 执行批量操作
            async applyBulkAction() {
                if (!this.bulkAction) {
                    alert('请选择操作类型');
                    return;
                }

                if (this.selectedSoftware.length === 0) {
                    alert('请选择要操作的软件');
                    return;
                }

                // 验证操作参数
                switch (this.bulkAction) {
                    case 'change_category':
                        if (!this.bulkActionParams.targetCategory) {
                            alert('请选择目标分类');
                            return;
                        }
                        break;

                    case 'update_price':
                        if (this.bulkActionParams.price === '') {
                            alert('请输入价格');
                            return;
                        }
                        break;

                    case 'update_downloads':
                        if (this.bulkActionParams.downloadValue === '' || isNaN(this.bulkActionParams.downloadValue)) {
                            alert('请输入有效的下载量数值');
                            return;
                        }
                        break;

                    case 'delete':
                        if (!this.bulkActionParams.confirmDelete) {
                            alert('请确认删除操作');
                            return;
                        }

                        // 二次确认
                        if (!confirm(`确定要删除选中的 ${this.selectedSoftware.length} 个软件吗？此操作不可撤销！`)) {
                            return;
                        }
                        break;
                }

                this.processing = true;

                try {
                    // 发送批量操作请求
                    const data = await apiPost(
                        '<?php echo $apiPath; ?>panel/bulk-manage.php',
                        {
                            action: this.bulkAction,
                            software_ids: this.selectedSoftware,
                            params: this.bulkActionParams
                        },
                        {},
                        {
                            showSuccess: true,
                            successMessage: `操作成功完成，共处理 ${this.selectedSoftware.length} 个软件`,
                            operation: '批量操作'
                        }
                    );

                    this.processing = false;

                    if (data && data.success) {
                        // 重置选择和操作
                        this.selectedItems = {};
                        this.selectedSoftware = [];
                        this.bulkAction = '';
                        this.bulkActionParams = {
                            targetCategory: '',
                            price: '',
                            downloadOperation: 'set',
                            downloadValue: 0,
                            confirmDelete: false
                        };

                        // 重新加载软件列表
                        this.loadSoftwareList();
                    }
                } catch (error) {
                    this.processing = false;
                    console.error('批量操作出错：', error);
                }
            }
        }
    });
</script>

<?php
// 渲染页脚
render_footer();
?>
