<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 检查权限
require_permission('admin.settings');

// 处理GET请求 - 获取设置
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // 获取所有设置，确保应用默认值
    $settings = Settings::getAll();

    // 将嵌套结构转换为扁平结构
    $flatSettings = Settings::flattenSettings($settings);

    // 返回设置
    api_success($flatSettings);
}

// 处理POST请求 - 保存设置
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data) {
        api_error('无效的数据格式');
    }

    // 验证必填字段
    include dirname(__DIR__) . '/config/settings_tabs.php';
    $tabsConfig = load_settings_tabs(dirname(__DIR__) . '/config');
    $validationErrors = Settings::validateSettings($data, $tabsConfig);

    if (!empty($validationErrors)) {
        api_error('验证失败: ' . implode(', ', $validationErrors));
    }

    // 将扁平结构转换为嵌套结构
    $nestedSettings = Settings::nestSettings($data);

    // 保存设置
    if (Settings::save($nestedSettings)) {
        api_success(['message' => '设置保存成功']);
    } else {
        api_error('保存设置失败');
    }
}
