# 支付宝签名验证文档

本文档详细说明娜宝贝软件系统中支付宝RSA2签名验证的实现原理、使用方法和问题排查。

## 概述

系统实现了完整的支付宝RSA2签名验证机制，包括：
- 支付请求签名生成
- 异步回调通知验签
- 同步回调返回验签
- 调试和测试功能

## 相关文件

### 核心文件
- `/api/payment.php` - 支付处理和签名生成
- `/api/public/payment_notify.php` - 异步回调验签
- `/api/public/payment_return.php` - 同步回调验签和测试接口
- `/includes/Settings.php` - 支付配置管理

### 配置文件
- `/api/admin/config/settings_tabs_payment.php` - 支付设置界面配置
- `/api/admin/config/settings_tabs_debug.php` - 调试设置界面配置

## 签名验证算法

### 1. 参数预处理

```php
// 移除不参与签名的参数
unset($params['sign'], $params['sign_type']);

// 移除空值参数（仅在某些场景下）
foreach ($params as $k => $v) {
    if ($v === '' || is_null($v)) {
        unset($params[$k]);
    }
}
```

### 2. 参数排序

```php
// 按参数名ASCII码从小到大排序
ksort($params);
```

### 3. 构建待签名字符串

```php
$stringToBeSigned = '';
foreach ($params as $k => $v) {
    $stringToBeSigned .= $k . '=' . $v . '&';
}
$stringToBeSigned = rtrim($stringToBeSigned, '&');
```

### 4. 公钥格式化

```php
// 如果公钥不包含PEM格式头尾，则添加
if (strpos($publicKeyContent, '-----BEGIN') === false) {
    $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                 wordwrap($publicKeyContent, 64, "\n", true) .
                 "\n-----END PUBLIC KEY-----";
} else {
    $publicKey = $publicKeyContent;
}
```

### 5. RSA2签名验证

```php
$result = openssl_verify($stringToBeSigned, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);
// $result === 1 表示验证成功
// $result === 0 表示验证失败
// $result === -1 表示发生错误
```

## 验签场景

### 1. 支付请求签名生成

**文件**: `/api/payment.php` - `generate_alipay_sign()` 函数

**用途**: 创建支付订单时生成请求签名

**特点**:
- 使用商户私钥签名
- 排除 `sign` 参数
- 排除空值参数

### 2. 异步回调验签

**文件**: `/api/public/payment_notify.php` - `process_alipay_notify()` 函数

**用途**: 验证支付宝服务器发送的异步通知

**特点**:
- 使用支付宝公钥验证
- 检查交易状态（`TRADE_SUCCESS` 或 `TRADE_FINISHED`）
- 验证成功后更新订单状态

### 3. 同步回调验签

**文件**: `/api/public/payment_return.php` - `verify_alipay_return` 操作

**用途**: 验证用户支付完成后的同步跳转

**特点**:
- 使用支付宝公钥验证
- 检查订单号和金额匹配
- 支持调试模式跳过验证

## 测试接口

### 1. 测试配置接口

**URL**: `/api/public/payment_return.php?action=test_config`

**功能**:
- 检查支付宝配置完整性
- 验证公钥格式正确性
- 返回配置状态信息

**返回示例**:
```json
{
    "success": true,
    "data": {
        "app_id": "2021004139607564",
        "public_key_length": 392,
        "private_key_length": 1675,
        "gateway": "https://openapi.alipay.com/gateway.do",
        "notify_url": "api/public/payment_notify.php?type=alipay",
        "public_key_valid": true,
        "public_key_formatted": "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----"
    }
}
```

### 2. 测试签名接口

**URL**: `/api/public/payment_return.php?action=test_sign`

**功能**:
- 使用固定测试参数验证签名
- 返回详细的验签过程信息
- 包含待签名字符串和验证结果

**测试参数**:
```php
$testParams = [
    'app_id' => '2021004139607564',
    'auth_app_id' => '2021004139607564',
    'charset' => 'utf-8',
    'method' => 'alipay.trade.page.pay.return',
    'order_no' => '202505251500412770',
    'out_trade_no' => '202505251500412770',
    'seller_id' => '2088742202512445',
    'timestamp' => '2025-05-25 23:01:14',
    'total_amount' => '0.01',
    'trade_no' => '2025052522001437801446816132',
    'version' => '1.0'
];
```

## 配置说明

### 支付宝配置项

在后台设置 → 支付设置 → 支付宝设置中配置：

1. **应用ID (app_id)**: 支付宝开放平台应用ID
2. **商户私钥**: 用于签名请求的RSA私钥
3. **支付宝公钥**: 用于验证回调的支付宝RSA公钥
4. **支付通知URL**: 异步回调地址（默认：`api/public/payment_notify.php?type=alipay`）
5. **支付宝网关**: 
   - 正式环境：`https://openapi.alipay.com/gateway.do`
   - 沙箱环境：`https://openapi.alipaydev.com/gateway.do`

### 调试设置

在后台设置 → 调试设置中配置：

1. **启用调试模式**: 显示详细错误信息
2. **跳过支付签名验证**: ⚠️ 仅测试环境使用
3. **记录支付回调日志**: 记录详细回调信息

## 常见问题排查

### 1. 签名验证失败

**可能原因**:
- 支付宝公钥配置错误
- 参数编码不是UTF-8
- 参数值被意外修改
- 时间戳格式错误

**排查步骤**:
1. 使用 `test_config` 接口检查配置
2. 使用 `test_sign` 接口测试固定参数
3. 检查公钥是否从支付宝开放平台正确复制
4. 确认服务器时间同步

### 2. 公钥格式错误

**症状**: `public_key_valid` 返回 `false`

**解决方案**:
- 确保公钥包含完整内容，无多余空格或换行
- 系统会自动添加PEM格式头尾，无需手动添加
- 从支付宝开放平台重新复制公钥

### 3. 参数缺失

**症状**: "回调参数不完整" 错误

**检查项**:
- URL中是否包含 `sign` 参数
- URL中是否包含 `out_trade_no` 参数
- URL中是否包含 `total_amount` 参数

### 4. 订单验证失败

**可能原因**:
- 订单号不匹配
- 金额不一致
- 订单状态异常

**解决方案**:
- 检查数据库中的订单信息
- 确认支付金额格式（保留两位小数）
- 验证订单状态流转逻辑

## 安全建议

1. **生产环境必须关闭调试模式**
2. **定期更新支付宝公钥**
3. **保护商户私钥安全**
4. **启用HTTPS传输**
5. **记录关键操作日志**

## 技术支持

如遇到验签问题，可以：
1. 查看系统调试日志
2. 使用测试接口排查
3. 检查支付宝开放平台配置
4. 联系技术支持团队
