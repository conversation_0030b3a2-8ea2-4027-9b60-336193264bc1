/**
 * 管理后台通用工具函数库
 */

const AdminUtils = {
    /**
     * 格式化日期时间
     * @param {number} timestamp Unix时间戳
     * @param {string} format 格式化字符串，默认为 'YYYY-MM-DD HH:mm:ss'
     * @returns {string} 格式化后的日期时间字符串
     */
    formatDateTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!timestamp) return '-';
        
        const date = new Date(timestamp * 1000);
        
        if (format === 'relative') {
            return this.getRelativeTime(date);
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    /**
     * 获取相对时间
     * @param {Date} date 日期对象
     * @returns {string} 相对时间字符串
     */
    getRelativeTime(date) {
        const now = new Date();
        const diff = now - date;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) return `${days}天前`;
        if (hours > 0) return `${hours}小时前`;
        if (minutes > 0) return `${minutes}分钟前`;
        return '刚刚';
    },

    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @param {number} decimals 小数位数，默认为2
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes, decimals = 2) {
        if (!bytes || bytes === 0) return '0 B';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },

    /**
     * 格式化数字
     * @param {number} num 数字
     * @param {string} locale 地区代码，默认为 'zh-CN'
     * @returns {string} 格式化后的数字
     */
    formatNumber(num, locale = 'zh-CN') {
        if (num === null || num === undefined) return '-';
        return new Intl.NumberFormat(locale).format(num);
    },

    /**
     * 格式化货币
     * @param {number} amount 金额
     * @param {string} currency 货币代码，默认为 'CNY'
     * @param {string} locale 地区代码，默认为 'zh-CN'
     * @returns {string} 格式化后的货币
     */
    formatCurrency(amount, currency = 'CNY', locale = 'zh-CN') {
        if (amount === null || amount === undefined) return '-';
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency
        }).format(amount);
    },

    /**
     * 检查权限
     * @param {string} permission 权限名称
     * @returns {boolean} 是否有权限
     */
    checkPermission(permission) {
        const userPermissions = JSON.parse(localStorage.getItem('admin_permissions') || '[]');
        return userPermissions.includes('all') || userPermissions.includes(permission);
    },

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间（毫秒）
     * @param {boolean} immediate 是否立即执行
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    },

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 深拷贝对象
     * @param {any} obj 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * 生成随机字符串
     * @param {number} length 长度
     * @param {string} chars 字符集
     * @returns {string} 随机字符串
     */
    randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    /**
     * 获取URL参数
     * @param {string} name 参数名
     * @param {string} url URL字符串，默认为当前页面URL
     * @returns {string|null} 参数值
     */
    getUrlParam(name, url = window.location.href) {
        const urlObj = new URL(url);
        return urlObj.searchParams.get(name);
    },

    /**
     * 设置URL参数
     * @param {string} name 参数名
     * @param {string} value 参数值
     * @param {boolean} pushState 是否推送到历史记录
     */
    setUrlParam(name, value, pushState = false) {
        const url = new URL(window.location.href);
        if (value === null || value === undefined || value === '') {
            url.searchParams.delete(name);
        } else {
            url.searchParams.set(name, value);
        }
        
        if (pushState) {
            window.history.pushState({}, '', url.toString());
        } else {
            window.history.replaceState({}, '', url.toString());
        }
    },

    /**
     * 格式化JSON字符串
     * @param {string|object} json JSON字符串或对象
     * @param {number} indent 缩进空格数
     * @returns {string} 格式化后的JSON字符串
     */
    formatJson(json, indent = 2) {
        try {
            const obj = typeof json === 'string' ? JSON.parse(json) : json;
            return JSON.stringify(obj, null, indent);
        } catch (error) {
            return typeof json === 'string' ? json : JSON.stringify(json);
        }
    },

    /**
     * 验证邮箱格式
     * @param {string} email 邮箱地址
     * @returns {boolean} 是否为有效邮箱
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * 验证手机号格式
     * @param {string} phone 手机号
     * @returns {boolean} 是否为有效手机号
     */
    isValidPhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    },

    /**
     * 验证URL格式
     * @param {string} url URL地址
     * @returns {boolean} 是否为有效URL
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    /**
     * 转义HTML字符
     * @param {string} text 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    },

    /**
     * 截断文本
     * @param {string} text 要截断的文本
     * @param {number} length 最大长度
     * @param {string} suffix 后缀，默认为 '...'
     * @returns {string} 截断后的文本
     */
    truncateText(text, length, suffix = '...') {
        if (!text || text.length <= length) return text;
        return text.substring(0, length) + suffix;
    },

    /**
     * 计算分页信息
     * @param {number} page 当前页码
     * @param {number} pageSize 每页大小
     * @param {number} total 总记录数
     * @returns {object} 分页信息
     */
    calculatePagination(page, pageSize, total) {
        const totalPages = Math.ceil(total / pageSize);
        const offset = (page - 1) * pageSize;
        
        return {
            page,
            pageSize,
            total,
            totalPages,
            offset,
            hasNext: page < totalPages,
            hasPrev: page > 1,
            startRecord: offset + 1,
            endRecord: Math.min(offset + pageSize, total)
        };
    },

    /**
     * 生成分页页码数组
     * @param {number} currentPage 当前页码
     * @param {number} totalPages 总页数
     * @param {number} maxVisible 最大显示页码数
     * @returns {Array} 页码数组
     */
    generatePageNumbers(currentPage, totalPages, maxVisible = 5) {
        const pages = [];
        const half = Math.floor(maxVisible / 2);
        
        let start = Math.max(1, currentPage - half);
        let end = Math.min(totalPages, start + maxVisible - 1);
        
        if (end - start + 1 < maxVisible) {
            start = Math.max(1, end - maxVisible + 1);
        }
        
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        
        return pages;
    }
};

// 如果在浏览器环境中，将工具函数挂载到全局对象
if (typeof window !== 'undefined') {
    window.AdminUtils = AdminUtils;
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminUtils;
}
